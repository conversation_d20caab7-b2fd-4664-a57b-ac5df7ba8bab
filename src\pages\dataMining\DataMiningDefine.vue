<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <div class="item-label">名称：</div>
          <ltw-input :placeholder="$t('请输入名称')" v-model="queryParam.name" clearable @clear="refresh"> </ltw-input>
        </div>
        <div class="ltw-search-container ltw-tool-container">
          <div class="item-label">类型：</div>
          <el-select
            v-model="queryParam.type"
            clearable
            @clear="refresh"
            :placeholder="$t('请选择类型')"
            style="width: 100%"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="ltw-search-container ltw-tool-container">
          <div style="width: 140px">是否自动化：</div>
          <el-select
            v-model="queryParam.auto"
            clearable
            @clear="refresh"
            :placeholder="$t('请选择是否自动化')"
            style="width: 100%"
          >
            <el-option
              v-for="item in autoOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button type="primary"><ltw-icon icon-code="el-icon-search"></ltw-icon>查询</el-button>
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
        </div>
      </div>
      <el-table ref="tableRef" :data="pageData.records" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column prop="name" :label="$t('名称')" min-width="120"></el-table-column>
        <el-table-column prop="type" :label="$t('类型')" min-width="100">
          <template #default="scope">
            {{ getTypeLabel(scope.row.type) }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="workflowDefinitionName" :label="$t('关联工作流')" min-width="150"></el-table-column> -->
        <el-table-column prop="auto" :label="$t('是否自动化')" min-width="100">
          <template #default="scope">
            <el-tag :type="scope.row.auto === 1 ? 'success' : 'info'">
              {{ scope.row.auto === 1 ? $t('是') : $t('否') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('创建时间')" min-width="160"></el-table-column>
        <el-table-column :label="$t('操作')" fixed="right" width="150">
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button :type="item.buttonStyleType" @click="executeButtonMethod(item, scope.row)">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>

      <!-- 表单对话框 -->
      <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px" destroy-on-close @closed="dialogClosed">
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
          <el-form-item :label="$t('名称')" prop="name">
            <ltw-input v-model="formData.name" :disabled="formReadonly"></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('类型')" prop="type">
            <el-select v-model="formData.type" :disabled="formReadonly" style="width: 100%">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item :label="$t('关联工作流')" prop="workflowDefinitionId">
            <el-select
              v-model="formData.workflowDefinitionId"
              :disabled="formReadonly"
              filterable
              style="width: 100%"
              @change="handleWorkflowChange"
            >
              <el-option
                v-for="item in workflowDefinitionList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item :label="$t('是否自动化')" prop="auto">
            <el-radio-group v-model="formData.auto" :disabled="formReadonly">
              <el-radio :label="1">{{ $t('是') }}</el-radio>
              <el-radio :label="0">{{ $t('否') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('是否使用默认规则')" prop="defaultRuleIds">
            <el-radio-group v-model="formData.defaultRuleIds" :disabled="formReadonly">
              <el-radio :label="1">{{ $t('是') }}</el-radio>
              <el-radio :label="0">{{ $t('否') }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 静态筛选 -->
          <el-form-item :label="$t('静态筛选')" prop="staticFiltering" v-if="formData.defaultRuleIds === 1">
            <el-select
              v-model="formData.staticFiltering"
              multiple
              collapse-tags
              :disabled="formReadonly"
              style="width: 100%"
              :placeholder="$t('请选择静态筛选标签')"
            >
              <el-option
                v-for="item in staticFilteringOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <!-- 自车筛选 -->
          <el-form-item :label="$t('自车筛选')" prop="selfVehicleFiltering" v-if="formData.defaultRuleIds === 1">
            <el-select
              v-model="formData.selfVehicleFiltering"
              multiple
              collapse-tags
              :disabled="formReadonly"
              style="width: 100%"
              :placeholder="$t('请选择自车筛选标签')"
            >
              <el-option
                v-for="item in selfVehicleFilteringOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('描述')" prop="description">
            <el-input v-model="formData.description" :disabled="formReadonly" type="textarea" :rows="3"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">{{ $t('取消') }}</el-button>
            <el-button type="primary" @click="saveForm" v-if="!formReadonly">{{ $t('确定') }}</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { showToast, showConfirmToast } from '@/plugins/util'
import {
  pageDataMiningDefine,
  getDataMiningDefine,
  saveDataMiningDefine,
  updateDataMiningDefine,
  deleteDataMiningDefine
} from '@/apis/dataMining/data_mining_define'
import { listDataMiningRule } from '@/apis/dataMining/data_mining_rule'

import BASE_CONSTANT from '@/plugins/constants/base-constant'

const defaultFormData = {
  name: '',
  type:'',
  workflowDefinitionId: '',
  workflowDefinitionName: '',
  auto: 0, // 修改为数字类型，默认为0（否）
  defaultRuleIds: 0,
  staticFiltering: [], // 静态筛选
  selfVehicleFiltering: [], // 自车筛选
  description: ''
}

export default {
  name: 'DataMiningDefine',
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0,
        records: []
      },
      queryParam: {
        current: 1,
        size: 10,
        name: '',
        type: '',
        auto: '' // 修改查询参数名
      },
      selectedData: [],
      dialogVisible: false,
      dialogTitle: '',
      dialogStatus: '',
      formData: Object.assign({}, defaultFormData),
      formRules: {
        name: [{ required: true, message: this.$t('请输入名称'), trigger: 'blur' }],
        type: [{ required: true, message: this.$t('请选择类型'), trigger: 'change' }],
       // workflowDefinitionId: [{ required: true, message: this.$t('请选择关联工作流'), trigger: 'change' }]
      },
      formReadonly: false,
      typeOptions: [
        { value: 0, label: 'Common' },
        { value: 1, label: 'ER' },
        // { value: 2, label: 'Defect' }
      ],
      workflowDefinitionList: [],
      dataMiningRuleList: [],
      autoOptions: [
        { value: 1, label: this.$t('是') },
        { value: 0, label: this.$t('否') }
      ],
      // 静态筛选选项
      staticFilteringOptions: [
        { id: 1, name: '文字标签', type: 'text' },
        { id: 2, name: '数字标签', type: 'number' },
        { id: 3, name: '符号标签', type: 'symbol' },
        { id: 4, name: '箭头标签', type: 'arrow' }
      ],
      // 自车筛选选项
      selfVehicleFilteringOptions: [
        { id: 1, name: '自车标签1', type: 'self' },
        { id: 2, name: '自车标签2', type: 'self' },
        { id: 3, name: '自车标签3', type: 'self' }
      ]
    }
  },
  created() {
    this.initFunctionList()
    this.initPermission()
    this.query()
  },
  watch: {
    'formData.defaultRuleIds': {
      handler(newVal) {
        // 当选择使用默认规则时，添加静态筛选和自车筛选
        if (newVal === 1) {
          // 如果之前没有设置过，则设置默认值
          if (!this.formData.staticFiltering || this.formData.staticFiltering.length === 0) {
            // 默认选择前两个静态筛选选项
            this.formData.staticFiltering = this.staticFilteringOptions.slice(0, 2).map(item => item.id)
          }
          if (!this.formData.selfVehicleFiltering || this.formData.selfVehicleFiltering.length === 0) {
            // 默认选择第一个自车筛选选项
            this.formData.selfVehicleFiltering = this.selfVehicleFilteringOptions.slice(0, 1).map(item => item.id)
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    initFunctionList() {
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
    },
    initPermission() {
      // 初始化权限
      this.dataOperatePermission = {
        add: true,
        edit: true,
        remove: true,
        view: true
      }
    },
    executeButtonMethod(button, row) {
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageDataMiningDefine(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
      this.formReadonly = false
      this.formData = Object.assign({}, defaultFormData)
      this.loadWorkflowDefinitions()
      this.loadDataMiningRules()
    },
    edit(row) {
      this.dialogTitle = this.$t('编辑')
      this.dialogStatus = 'edit'
      this.dialogVisible = true
      this.formReadonly = false
      getDataMiningDefine(row.id).then(res => {
        this.formData = res.data
        // 确保defaultRuleIds是数字
        if (this.formData.defaultRuleIds === undefined || this.formData.defaultRuleIds === null) {
          this.formData.defaultRuleIds = 0
        }
        // 确保静态筛选和自车筛选是数组
        if (!this.formData.staticFiltering) {
          this.formData.staticFiltering = []
        }
        if (!this.formData.selfVehicleFiltering) {
          this.formData.selfVehicleFiltering = []
        }
        this.loadWorkflowDefinitions()
        this.loadDataMiningRules()
      })
    },
    view(row) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      this.dialogVisible = true
      this.formReadonly = true
      getDataMiningDefine(row.id).then(res => {
        this.formData = res.data
        // 确保defaultRuleIds是数字
        if (this.formData.defaultRuleIds === undefined || this.formData.defaultRuleIds === null) {
          this.formData.defaultRuleIds = 0
        }
        // 确保静态筛选和自车筛选是数组
        if (!this.formData.staticFiltering) {
          this.formData.staticFiltering = []
        }
        if (!this.formData.selfVehicleFiltering) {
          this.formData.selfVehicleFiltering = []
        }
        this.loadWorkflowDefinitions()
        this.loadDataMiningRules()
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    handleWorkflowChange(value) {
      // 更新工作流名称
      const selectedWorkflow = this.workflowDefinitionList.find(item => item.id === value)
      if (selectedWorkflow) {
        this.formData.workflowDefinitionName = selectedWorkflow.name
      }
    },
    loadWorkflowDefinitions() {
      //   listWorkflowDefinition().then(res => {
      //     this.workflowDefinitionList = res.data
      //   })
    },
    loadDataMiningRules() {
      listDataMiningRule().then(res => {
        this.dataMiningRuleList = res.data
      })
    },
    saveForm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return

        const postData = { ...this.formData }

        if (this.dialogStatus === 'add') {
          debugger
          saveDataMiningDefine(postData).then(() => {
            showToast(this.$t('保存成功'))
            this.dialogVisible = false
            this.query()
          })
        } else if (this.dialogStatus === 'edit') {
          updateDataMiningDefine(postData).then(() => {
            showToast(this.$t('更新成功'))
            this.dialogVisible = false
            this.query()
          })
        }
      })
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    remove(param) {
        debugger
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteDataMiningDefine(param).then(() => {
          showToast(this.$t('删除成功'))
          this.query()
        })
      })
    },
    dialogClosed() {
      this.formData = Object.assign({}, defaultFormData)
      this.$refs.formRef?.resetFields()
    },
    getTypeLabel(type) {
      const option = this.typeOptions.find(item => item.value === type)
      return option ? option.label : ''
    }
  }
}
</script>

<style scoped>
.ltw-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
}

.ltw-search-container {
  margin-right: 16px;
  min-width: 200px;
  display: flex;
  align-items: center;
}

.button-group {
  display: flex;
}
.item-label {
  width: 60px;
}
</style>
