<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <div class="item-label">名称：</div>
          <ltw-input :placeholder="$t('请输入名称')" v-model="queryParam.name" clearable> </ltw-input>
        </div>
        <div class="ltw-search-container ltw-tool-container">
          <div class="item-label">类型：</div>
          <el-select
            v-model="queryParam.type"
            clearable
            :placeholder="$t('请选择类型')"
            style="width: 100%"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="ltw-search-container ltw-tool-container">
          <div style="width: 140px">是否自动化：</div>
          <el-select
            v-model="queryParam.auto"
            clearable
            :placeholder="$t('请选择是否自动化')"
            style="width: 100%"
          >
            <el-option
              v-for="item in autoOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button type="primary" @click="query"><ltw-icon icon-code="el-icon-search"></ltw-icon>查询</el-button>
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
        </div>
      </div>
      <el-table ref="tableRef" :data="pageData.records" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column prop="name" :label="$t('名称')" min-width="120"></el-table-column>
        <el-table-column prop="type" :label="$t('类型')" min-width="100">
          <template #default="scope">
            {{ getTypeLabel(scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="$t('描述')" min-width="160"></el-table-column>
        <!-- <el-table-column prop="workflowDefinitionName" :label="$t('关联工作流')" min-width="150"></el-table-column> -->
        <el-table-column prop="auto" :label="$t('是否自动化')" min-width="100">
          <template #default="scope">
            <el-tag :type="scope.row.auto === 1 ? 'success' : 'info'">
              {{ scope.row.auto === 1 ? $t('是') : $t('否') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="defaultRules" :label="$t('默认规则')" min-width="100">
          <template #default="scope">
            <el-link type="primary" v-if="scope.row.defaultRules" @click="viewJsonData(scope.row.defaultRules, '默认规则')">
                查看
            </el-link>
            <span v-else style="color: #999;">-</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('操作')" fixed="right" width="150">
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button :type="item.buttonStyleType" @click="executeButtonMethod(item, scope.row)">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>

      <!-- 表单对话框 -->
      <el-dialog v-model="dialogVisible" :title="dialogTitle" width="900px" destroy-on-close @closed="dialogClosed" class="data-mining-define-dialog">
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="140px">
          <div class="form-section">
            <div class="section-title">基本信息</div>
            <div class="form-row">
              <el-form-item :label="$t('名称')" prop="name" class="form-item-flex">
                <ltw-input v-model="formData.name" :disabled="formReadonly"></ltw-input>
              </el-form-item>
              <el-form-item :label="$t('类型')" prop="type" class="form-item-flex">
                <el-select v-model="formData.type" :disabled="formReadonly" style="width: 100%">
                  <el-option
                    v-for="item in typeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <!-- <el-form-item :label="$t('关联工作流')" prop="workflowDefinitionId">
              <el-select
                v-model="formData.workflowDefinitionId"
                :disabled="formReadonly"
                filterable
                style="width: 100%"
                @change="handleWorkflowChange"
              >
                <el-option
                  v-for="item in workflowDefinitionList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item> -->
            <div class="form-row">
              <el-form-item :label="$t('是否自动化')" prop="auto" class="form-item-flex">
                <el-radio-group v-model="formData.auto" :disabled="formReadonly">
                  <el-radio :label="1">{{ $t('是') }}</el-radio>
                  <el-radio :label="0">{{ $t('否') }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('是否使用规则')" prop="enableRule" class="form-item-flex">
                <el-radio-group v-model="formData.enableRule" :disabled="formReadonly">
                  <el-radio :label="1">{{ $t('是') }}</el-radio>
                  <el-radio :label="0">{{ $t('否') }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('描述')" prop="description" class="form-item-flex">
                <el-input v-model="formData.description" :disabled="formReadonly" type="textarea" :rows="3"  maxlength="128"  show-word-limit></el-input>
            </el-form-item>
            </div>
          </div>

          <!-- 静态筛选 -->
          <div v-if="formData.enableRule === 1" class="form-section filtering-section">
            <div class="section-title">静态筛选</div>
            <!-- 静态标签输入框区域 -->
            <el-form-item label="静态标签（支持多选）" :disabled="formReadonly">
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                <div style="flex: 1"></div>
                <el-button type="danger" size="small" @click="resetStaticTags" :disabled="formReadonly">重置</el-button>
              </div>
            </el-form-item>

            <div class="info-box">
              <div style="flex: 1; min-height: 40px">
                <el-tag
                  v-for="tag in selectedStaticTags"
                  :key="tag.id"
                  :closable="!formReadonly"
                  @close="removeStaticTag(tag)"
                  style="margin-right: 8px; margin-bottom: 4px"
                  type="success"
                >
                  {{ tagLabelWithBuffer(tag) }}
                </el-tag>
                <span v-if="selectedStaticTags.length === 0" style="color: #bbb">请选择标签</span>
              </div>
            </div>

            <!-- 最外层根节点卡片（支持多个） -->
            <div v-for="rootInfo in staticRootTagInfoList" :key="rootInfo.id" class="root-section">
              <el-card class="root-tag-card" shadow="hover" :disabled="formReadonly">
                <template #header>
                  <div class="card-header">
                    <el-checkbox
                      :model-value="getRootCheckedState(rootInfo.id).checked"
                      :indeterminate="getRootCheckedState(rootInfo.id).indeterminate"
                      @change="(val) => handleRootChange(rootInfo.id, val)"
                      :disabled="formReadonly"
                    >
                      {{ rootInfo.name }}
                    </el-checkbox>
                  </div>
                </template>

                <!-- 标签分类卡片 -->
                <div class="static-tag-cards">
                  <el-card v-for="cat in getCategoriesByRootId(rootInfo.id)" :key="cat.type" class="tag-card" shadow="hover" :disabled="formReadonly">
                    <template #header>
                      <div class="card-header">
                        <el-checkbox
                          v-model="categoryCheckedMap[cat.type]"
                          :indeterminate="categoryCheckedStates[cat.type]?.indeterminate"
                          @change="(val) => handleCategoryChange(cat, val)"
                          :disabled="formReadonly"
                        >
                          {{ cat.name }}
                        </el-checkbox>
                      </div>
                    </template>

                    <div v-for="tag in cat.tags" :key="tag.id" class="tag-item">
                      <div class="tag-checkbox">
                        <el-checkbox v-model="checkedTagIds" :label="String(tag.id)" :disabled="formReadonly">
                          {{ tag.name }}
                        </el-checkbox>
                      </div>
                      <div v-if="isTagChecked(tag)" class="tag-buffer-settings">
                        <div>
                          <el-checkbox
                            v-model="bufferSettings[String(tag.id)].enabled"
                            :disabled="formReadonly"
                          >匹配缓冲区</el-checkbox>
                        </div>
                        <div class="tag-box" v-if="bufferSettings[String(tag.id)]?.enabled">
                          <el-radio-group
                            v-model="bufferSettings[String(tag.id)].type"
                            size="small"
                            @change="(val) => handleBufferTypeChange(String(tag.id), val)"
                            :disabled="formReadonly"
                          >
                            <el-radio-button label="distance">距离</el-radio-button>
                            <el-radio-button label="lane">车道</el-radio-button>
                          </el-radio-group>
                          <div class="number-input-wrapper">
                            <el-input-number
                              v-model="bufferSettings[String(tag.id)].value"
                              :min="0"
                              :max="bufferSettings[String(tag.id)].type === 'distance' ? 1000 : 10"
                              :step="1"
                              :precision="0"
                              size="small"
                              @change="(val) => handleBufferValueChange(String(tag.id), val)"
                              :disabled="formReadonly"
                            ></el-input-number>
                            <span class="unit">{{ bufferSettings[String(tag.id)].type === 'distance' ? 'm' : '个' }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-card>
                </div>
              </el-card>
            </div>
            <el-empty v-show="staticRootTagInfoList.length === 0" description="暂无标签" />
          </div>

          <!-- 自车筛选 -->
          <div v-if="formData.enableRule === 1" class="form-section filtering-section">
            <div class="section-title">自车筛选</div>
            <!-- 上方标签输入框区域 -->
            <el-form-item label="自车标签（支持多选）" :disabled="formReadonly">
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                <div style="flex: 1"></div>
                <el-button type="danger" size="small" @click="resetSelfTags" :disabled="formReadonly">重置</el-button>
              </div>
            </el-form-item>

            <div class="info-box">
              <div style="flex: 1; min-height: 40px">
                <el-tag
                  v-for="tag in getSelfSelectedTags()"
                  :key="tag.id"
                  :closable="!formReadonly"
                  @close="removeSelfTag(tag)"
                  style="margin-right: 8px; margin-bottom: 4px"
                  type="primary"
                >
                  {{ selfTagLabelWithSettings(tag) }}
                </el-tag>
                <span v-if="getSelfSelectedTags().length === 0" style="color: #bbb">请选择标签</span>
              </div>
            </div>

            <!-- 最外层根节点卡片（支持多个） -->
            <div v-for="rootInfo in selfRootTagInfoList" :key="rootInfo.id" class="root-section">
              <el-card class="root-tag-card" shadow="hover" :disabled="formReadonly">
                <template #header>
                  <div class="card-header">
                    <el-checkbox
                      :model-value="getSelfRootCheckedState(rootInfo.id).checked"
                      :indeterminate="getSelfRootCheckedState(rootInfo.id).indeterminate"
                      @change="(val) => handleSelfRootChange(rootInfo.id, val)"
                      :disabled="formReadonly"
                    >
                      {{ rootInfo.name }}
                    </el-checkbox>
                  </div>
                </template>

                <!-- 标签选择区域 -->
                <div class="self-tag-cards">
                  <!-- 全选卡片 -->
                  <!-- <el-card class="tag-card" shadow="hover">
                    <template #header>
                      <div class="card-header">
                        <el-checkbox
                          v-model="selfAllChecked"
                          :indeterminate="selfIndeterminate"
                          @change="handleSelfCheckAllChange"
                          :disabled="formReadonly"
                        >
                          全选
                        </el-checkbox>
                      </div>
                    </template>
                  </el-card> -->

                  <!-- 分类卡片 -->
                  <el-card v-for="cat in getSelfCategoriesByRootId(rootInfo.id)" :key="cat.type" class="tag-card" shadow="hover" :disabled="formReadonly">
                    <template #header>
                      <div class="card-header">
                        <el-checkbox
                          v-model="selfCategoryCheckedMap[cat.type]"
                          :indeterminate="selfCategoryCheckedStates[cat.type]?.indeterminate"
                          @change="(val) => handleSelfCategoryChange(cat, val)"
                          :disabled="formReadonly"
                        >
                          {{ cat.name }}
                        </el-checkbox>
                      </div>
                    </template>

                    <div v-for="tag in cat.tags" :key="tag.id" class="tag-item">
                      <div class="tag-checkbox">
                        <el-checkbox v-model="selfCheckedTagIds" :label="String(tag.id)" :disabled="formReadonly">
                          {{ tag.name }}
                        </el-checkbox>
                      </div>
                      <div v-if="isSelfTagChecked(tag)" class="tag-buffer-settings">
                        <!-- 匹配缓冲区设置 -->
                        <div>
                          <el-checkbox
                            v-model="selfBufferSettings[String(tag.id)].enabled"
                            :disabled="formReadonly"
                          >匹配缓冲区</el-checkbox>
                        </div>
                        <div class="tag-box" v-if="selfBufferSettings[String(tag.id)]?.enabled">
                          <el-radio-group
                            v-model="selfBufferSettings[String(tag.id)].type"
                            size="small"
                            :disabled="formReadonly"
                          >
                            <el-radio-button label="distance">距离</el-radio-button>
                            <el-radio-button label="lane">车道</el-radio-button>
                          </el-radio-group>
                          <div class="number-input-wrapper">
                            <el-input-number
                              v-model="selfBufferSettings[String(tag.id)].value"
                              :min="0"
                              :max="selfBufferSettings[String(tag.id)].type === 'distance' ? 1000 : 10"
                              :step="1"
                              :precision="0"
                              size="small"
                              :disabled="formReadonly"
                            ></el-input-number>
                            <span class="unit">{{ selfBufferSettings[String(tag.id)].type === 'distance' ? 'm' : '个' }}</span>
                          </div>
                        </div>

                        <!-- 数据补全设置 -->
                        <div style="margin-top: 8px;">
                          <el-checkbox
                            v-model="selfPatchSettings[String(tag.id)].enabled"
                            :disabled="formReadonly"
                          >数据补全</el-checkbox>
                        </div>
                        <div class="tag-box" v-if="selfPatchSettings[String(tag.id)]?.enabled">
                          <el-radio-group
                            v-model="selfPatchSettings[String(tag.id)].type"
                            size="small"
                            :disabled="formReadonly"
                          >
                            <el-radio-button label="distance">距离</el-radio-button>
                            <el-radio-button label="time">时间</el-radio-button>
                          </el-radio-group>
                          <div class="number-input-wrapper">
                            <el-input-number
                              v-model="selfPatchSettings[String(tag.id)].value"
                              :min="0"
                              :max="1000"
                              :step="1"
                              :precision="0"
                              size="small"
                              :disabled="formReadonly"
                            ></el-input-number>
                            <span class="unit">{{ selfPatchSettings[String(tag.id)].type === 'distance' ? 'm' : 's' }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-card>
                </div>
              </el-card>
            </div>
            <el-empty v-show="selfRootTagInfoList.length === 0" description="暂无标签" />
          </div>

        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">{{ $t('取消') }}</el-button>
            <el-button type="primary" @click="saveForm" v-if="!formReadonly">{{ $t('确定') }}</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- JSON查看对话框 -->
      <el-dialog
        v-model="jsonDialogVisible"
        :title="jsonDialogTitle"
        width="80%"
        destroy-on-close
        class="json-dialog"
      >

        <div class="json-viewer-container">
          <json-viewer
            v-if="jsonData"
            :value="jsonData"
            :expand-depth="3"
            copyable
            sort
            boxed
            theme="jv-light"
          ></json-viewer>
          <div v-else class="no-data">
            <el-empty description="暂无数据"></el-empty>
          </div>
        </div>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closeJsonDialog">关闭</el-button>
            <!-- <el-button type="primary" @click="copyJsonData">复制JSON</el-button> -->
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { showToast, showConfirmToast } from '@/plugins/util'
import {
  pageDataMiningDefine,
  getDataMiningDefine,
  saveDataMiningDefine,
  updateDataMiningDefine,
  deleteDataMiningDefine,
  getTagTreeData
} from '@/apis/dataMining/data_mining_define'

import BASE_CONSTANT from '@/plugins/constants/base-constant'
import JsonViewer from 'vue-json-viewer'

const defaultFormData = {
  name: '',
  type:'',
  // workflowDefinitionId: '',
  // workflowDefinitionName: '',
  auto: 0, // 修改为数字类型，默认为0（否）
  enableRule: 0, // 是否启用默认规则
  ruleSettings: '', // 规则设置JSON字符串
  description: ''
}

export default {
  name: 'DataMiningDefine',
  components: {
    JsonViewer
  },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0,
        records: []
      },
      queryParam: {
        current: 1,
        size: 10,
        name: '',
        type: '',
        auto: '' // 修改查询参数名
      },
      selectedData: [],
      dialogVisible: false,
      dialogTitle: '',
      dialogStatus: '',
      formData: Object.assign({}, defaultFormData),
      formRules: {
        name: [{ required: true, message: this.$t('请输入名称'), trigger: 'blur' }],
        type: [{ required: true, message: this.$t('请选择类型'), trigger: 'change' }],
       // workflowDefinitionId: [{ required: true, message: this.$t('请选择关联工作流'), trigger: 'change' }]
      },
      formReadonly: false,
      typeOptions: [
        { value: 0, label: 'Common' },
        { value: 1, label: 'ER' },
        { value: 2, label: 'Defect' }
      ],
      workflowDefinitionList: [],
      // dataMiningRuleList: [],
      autoOptions: [
        { value: 1, label: this.$t('是') },
        { value: 0, label: this.$t('否') }
      ],
      // 静态筛选相关
      staticTagCategories: [], // 动态从API加载
      staticRootTagInfoList: [], // 静态标签第一层根节点信息列表（支持多个）

      // 自车筛选相关数据结构
      selfTagCategories: [], // 自车标签分类数据
      selfRootTagInfoList: [], // 自车标签第一层根节点信息列表（支持多个）
      selectedStaticTags: [], // 已选静态标签
      checkedTagIds: [], // 选中的标签ID
      bufferSettings: {}, // 缓冲区设置
      categoryCheckedMap: {}, // 分类选中状态
      rootChecked: false, // 根节点选中状态
      rootIndeterminate: false, // 根节点半选状态

      // 自车筛选相关
      selfCheckedTagIds: [], // 自车选中的标签ID
      selfBufferSettings: {}, // 自车缓冲区设置
      selfPatchSettings: {}, // 自车数据补全设置
      selfAllChecked: false, // 自车全选状态
      selfIndeterminate: false, // 自车半选状态
      selfCategoryCheckedMap: {}, // 自车分类选中状态
      selfRootChecked: false, // 自车根节点选中状态
      selfRootIndeterminate: false, // 自车根节点半选状态

      // JSON查看对话框相关
      jsonDialogVisible: false,
      jsonDialogTitle: '',
      jsonData: null
    }
  },
  created() {
    this.initFunctionList()
    this.initPermission()
    this.query()
    this.loadStaticTags() // 加载静态标签数据
    this.loadSelfTags() // 加载自车标签数据
  },
  computed: {
    // 计算每个类别的选中状态（静态筛选）
    categoryCheckedStates() {
      const states = {}
      this.staticTagCategories.forEach(cat => {
        const allTagIds = cat.tags.map(tag => String(tag.id))
        const checkedCount = allTagIds.filter(id => this.checkedTagIds.includes(id)).length
        states[cat.type] = {
          checked: checkedCount === cat.tags.length && cat.tags.length > 0,
          indeterminate: checkedCount > 0 && checkedCount < cat.tags.length
        }
      })
      return states
    },
    // 计算每个类别的选中状态（自车筛选）
    selfCategoryCheckedStates() {
      const states = {}
      this.selfTagCategories.forEach(cat => {
        const allTagIds = cat.tags.map(tag => String(tag.id))
        const checkedCount = allTagIds.filter(id => this.selfCheckedTagIds.includes(id)).length
        states[cat.type] = {
          checked: checkedCount === cat.tags.length && cat.tags.length > 0,
          indeterminate: checkedCount > 0 && checkedCount < cat.tags.length
        }
      })
      return states
    },
    // 计算根节点选中状态（静态筛选）
    rootCheckedState() {
      const allTagIds = this.getAllTags().map(tag => String(tag.id))
      const checkedCount = allTagIds.filter(id => this.checkedTagIds.includes(id)).length
      return {
        checked: checkedCount === allTagIds.length && allTagIds.length > 0,
        indeterminate: checkedCount > 0 && checkedCount < allTagIds.length
      }
    },
    // 计算根节点选中状态（自车筛选）
    selfRootCheckedState() {
      const allTagIds = this.getAllTags().map(tag => String(tag.id))
      const checkedCount = allTagIds.filter(id => this.selfCheckedTagIds.includes(id)).length
      return {
        checked: checkedCount === allTagIds.length && allTagIds.length > 0,
        indeterminate: checkedCount > 0 && checkedCount < allTagIds.length
      }
    }
  },
  watch: {
    // 移除默认标签选择的watcher，让用户自行选择标签
    // 监听选中的静态标签变化
    checkedTagIds: {
      handler(newVal, oldVal) {
        if (!oldVal) return // 初始化时跳过

        // 初始化新选中标签的缓冲区设置
        newVal.forEach(id => {
          if (!this.bufferSettings[id]) {
            this.bufferSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
        })

        // 找出新增的标签
        const addedTags = newVal.filter(id => !oldVal.includes(id))

        // 找出移除的标签
        const removedTags = oldVal.filter(id => !newVal.includes(id))

        // 为新增的标签初始化缓冲区设置并添加到已选标签列表
        addedTags.forEach(tagId => {
          this.initBufferSetting(tagId)
          // 添加到已选标签列表
          const tag = this.findTagById(tagId)
          if (tag && !this.selectedStaticTags.some(t => t.id === tag.id)) {
            this.selectedStaticTags.push(tag)
          }
        })

        // 处理移除的标签
        removedTags.forEach(tagId => {
          // 从已选标签列表中移除
          this.selectedStaticTags = this.selectedStaticTags.filter(tag => String(tag.id) !== tagId)
          // 清除缓冲区设置
          delete this.bufferSettings[tagId]
        })

        // 更新分类选中状态
        this.updateCategoryCheckedStatus()

        // 更新表单数据
        this.formData.staticFiltering = this.selectedStaticTags.map(tag => tag.id)
      },
      deep: true
    },
    // 监听自车标签选中状态变化
    selfCheckedTagIds: {
      handler(newVal, oldVal) {
        // 初始化新选中标签的设置
        newVal.forEach(id => {
          if (!this.selfBufferSettings[id]) {
            this.selfBufferSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
          if (!this.selfPatchSettings[id]) {
            this.selfPatchSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
        })

        // 清理未选中标签的设置
        if (oldVal) {
          Object.keys(this.selfBufferSettings).forEach(id => {
            if (!newVal.includes(id)) {
              delete this.selfBufferSettings[id]
            }
          })
          Object.keys(this.selfPatchSettings).forEach(id => {
            if (!newVal.includes(id)) {
              delete this.selfPatchSettings[id]
            }
          })
        }

        // 更新全选状态
        const total = this.getAllSelfTags().length
        this.selfAllChecked = newVal.length === total && total > 0
        this.selfIndeterminate = newVal.length > 0 && newVal.length < total

        // 更新自车分类选中状态
        this.updateSelfCategoryCheckedStatus()

        // 更新表单数据
        this.formData.selfVehicleFiltering = this.getSelfSelectedTags().map(tag => tag.id)
      },
      deep: true
    }
  },
  methods: {
    initFunctionList() {
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
    },
    initPermission() {
      // 初始化权限
      this.dataOperatePermission = {
        add: true,
        edit: true,
        remove: true,
        view: true
      }
    },
    executeButtonMethod(button, row) {
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageDataMiningDefine(this.queryParam).then(res => {
        this.pageData = res.data
      }).catch(res =>{
        this.pageData = {
          total: 0,
          records: []
        }
      })
    },
    add() {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
      this.formReadonly = false
      this.formData = Object.assign({}, defaultFormData)
     // this.loadWorkflowDefinitions()
     // this.loadDataMiningRules()
    },
    edit(row) {
      this.dialogTitle = this.$t('编辑')
      this.dialogStatus = 'edit'
      this.dialogVisible = true
      this.formReadonly = false
      getDataMiningDefine(row.id).then(res => {
        this.formData = res.data
        if (this.formData.enableRule === undefined || this.formData.enableRule === null) {
          this.formData.enableRule = 0
        }else{
          this.formData.enableRule = Number(this.formData.enableRule)
        }

        this.restoreFilteringData()
      })
//       this.formData =   {
//     id:"1222",
//     name:"test01",
//     type: "0",
//     description: "21421325",
//     auto: "1",
//     enableRule: 1,
//     ruleSettings: {
//         staticRules: [
//             {
//                 tagCode: "1101010119",
//                 bufferType: "lane",
//                 bufferValue: 5
//             }
//         ]
//     }
// }
// this.restoreFilteringData()
    },
    view(row) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      this.dialogVisible = true
      this.formReadonly = true
      getDataMiningDefine(row.id).then(res => {
        this.formData = res.data
        if (this.formData.enableRule === undefined || this.formData.enableRule === null) {
          this.formData.enableRule = 0
        }else{
          this.formData.enableRule = Number(this.formData.enableRule)
        }
        this.restoreFilteringData()

      })
//       this.formData =   {
//     id:"1222",
//     name:"test01",
//     type: "0",
//     description: "21421325",
//     auto: "1",
//     enableRule: 1,
//     ruleSettings: {
//         staticRules: [
//             {
//                 tagCode: "1101010119",
//                 bufferType: "distance/lane",
//                 bufferValue: 5
//             }
//         ]
//     }
// }
// this.restoreFilteringData()
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    // handleWorkflowChange(value) {
    //   // 更新工作流名称
    //   const selectedWorkflow = this.workflowDefinitionList.find(item => item.id === value)
    //   if (selectedWorkflow) {
    //     this.formData.workflowDefinitionName = selectedWorkflow.name
    //   }
    // },
    // loadWorkflowDefinitions() {
    //   //   listWorkflowDefinition().then(res => {
    //   //     this.workflowDefinitionList = res.data
    //   //   })
    // },
    // loadDataMiningRules() {
    //   listDataMiningRule().then(res => {
    //     this.dataMiningRuleList = res.data
    //   })
    // },
    saveForm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return

        const postData = { ...this.formData }

        // 构建规则设置数据
        const ruleSettings = {
          staticRules: [],
          egoRules: [],
          othersRules: []
        }

        if (this.formData.enableRule === 1) {
          // 静态筛选：按照新格式保存
          ruleSettings.staticRules = this.selectedStaticTags.map(tag => {
            const tagId = String(tag.id)
            const bufferSetting = this.bufferSettings[tagId]
            return {
              tagCode: tag.code,
              bufferType: bufferSetting?.enabled ? bufferSetting.type : null,
              bufferValue: bufferSetting?.enabled ? bufferSetting.value : null
            }
          }).filter(rule => rule.tagCode) // 过滤掉无效的规则

          // 自车筛选：按照新格式保存
          ruleSettings.egoRules = this.getSelfSelectedTags().map(tag => {
            const tagId = String(tag.id)
            const bufferSetting = this.selfBufferSettings[tagId]
            const patchSetting = this.selfPatchSettings[tagId]
            return {
              tagCode: tag.code,
              bufferType: bufferSetting?.enabled ? bufferSetting.type : null,
              bufferValue: bufferSetting?.enabled ? bufferSetting.value : null,
              completeRuleType: patchSetting?.enabled ? patchSetting.type : null,
              completeRuleValue: patchSetting?.enabled ? patchSetting.value : null
            }
          }).filter(rule => rule.tagCode) // 过滤掉无效的规则
        }


        postData.ruleSettings = ruleSettings

        if (this.dialogStatus === 'add') {
          saveDataMiningDefine(postData).then(() => {
            showToast(this.$t('保存成功'))
            this.dialogVisible = false
            this.query()
          })
        } else if (this.dialogStatus === 'edit') {
          updateDataMiningDefine(postData).then(() => {
            showToast(this.$t('更新成功'))
            this.dialogVisible = false
            this.query()
          })
        }
      })
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteDataMiningDefine(param).then(() => {
          showToast(this.$t('删除成功'))
          this.query()
        })
      })
    },
    dialogClosed() {
      this.formData = Object.assign({}, defaultFormData)
      this.$refs.formRef?.resetFields()

      // 重置筛选相关数据
      this.selectedStaticTags = []
      this.checkedTagIds = []
      this.bufferSettings = {}
      this.categoryCheckedMap = {}
      this.rootChecked = false
      this.rootIndeterminate = false
      this.selfCheckedTagIds = []
      this.selfBufferSettings = {}
      this.selfPatchSettings = {}
      this.selfAllChecked = false
      this.selfIndeterminate = false
      this.selfCategoryCheckedMap = {}
      this.selfRootChecked = false
      this.selfRootIndeterminate = false
    },
    getTypeLabel(type) {
      const option = this.typeOptions.find(item => item.value === type)
      return option ? option.label : ''
    },

    // 静态筛选相关方法
    loadStaticTags() {
      // 调用API获取静态标签树形数据
      this.fetchTagTreeData('static').then(treeData => {
        this.processStaticTagTreeData(treeData)

        // 初始化后更新分类选中状态
        this.$nextTick(() => {
          if (this.checkedTagIds && this.checkedTagIds.length > 0) {
            this.updateCategoryCheckedStatus()
          }
        })
      })
    },

    // 自车筛选相关方法
    loadSelfTags() {
      // 调用API获取自车标签树形数据
      this.fetchTagTreeData('ego').then(treeData => {
        this.processSelfTagTreeData(treeData)

        // 初始化后更新分类选中状态
        this.$nextTick(() => {
          if (this.selfCheckedTagIds && this.selfCheckedTagIds.length > 0) {
            this.updateSelfCategoryCheckedStatus()
          }
        })
      })
    },

    // 获取标签树形数据的API调用
    async fetchTagTreeData(type) {
      try {
        // 调用真实API
        const response = await getTagTreeData(type)
        return response.data

        // return [
        //   {
        //     "asLeaf": false,
        //     "children": [
        //       {
        //         "asLeaf": true,
        //         "code": "11010101",
        //         "disabled": false,
        //         "enabled": true,
        //         "id": "1879781258942255104",
        //         "mutuallyExclusive": false,
        //         "name": "路口场景",
        //         "nameCn": "路口场景",
        //         "parentId": "1879781180735262720",
        //         "sortNum": 65536,
        //         "tagList": [
        //           {
        //             "acquisitionType": "driving",
        //             "code": "1101010119",
        //             "enabled": true,
        //             "followingDuration": 10,
        //             "groupCode": "11010101",
        //             "groupId": "1879781258942255104",
        //             "groupName": "路口场景",
        //             "groupNameCn": "路口场景",
        //             "id": "1909202548030881792",
        //             "mappingNewTag": false,
        //             "mutuallyExclusive": false,
        //             "name": "隧道入口",
        //             "nameCn": "隧道入口",
        //             "poi": true,
        //             "previousDuration": 5,
        //             "relatedTagCode": "1101010118",
        //             "relatedType": "extend",
        //             "sortNum": 1245184,
        //             "sourceAccuracy": "",
        //             "supportTrigger": false,
        //             "supportVoice": false,
        //             "type": "transient",
        //             "useCase": "common",
        //             "version": "20250430"
        //           },
        //           {
        //             "acquisitionType": "driving",
        //             "code": "1101010120",
        //             "enabled": true,
        //             "followingDuration": 10,
        //             "groupCode": "11010101",
        //             "groupId": "1879781258942255104",
        //             "groupName": "路口场景",
        //             "groupNameCn": "路口场景",
        //             "id": "1909202698648260608",
        //             "mappingNewTag": false,
        //             "mutuallyExclusive": false,
        //             "name": "隧道出口",
        //             "nameCn": "隧道出口",
        //             "poi": true,
        //             "previousDuration": 5,
        //             "relatedTagCode": "1101010118",
        //             "relatedType": "extend",
        //             "sortNum": 1310720,
        //             "sourceAccuracy": "",
        //             "supportTrigger": false,
        //             "supportVoice": false,
        //             "type": "transient",
        //             "useCase": "common",
        //             "version": "20250430"
        //           }
        //         ]
        //       }
        //     ],
        //     "code": "110101",
        //     "disabled": false,
        //     "enabled": true,
        //     "id": "1879781180735262720",
        //     "mutuallyExclusive": false,
        //     "name": "道路结构特性",
        //     "nameCn": "道路结构特性",
        //     "parentId": "root",
        //     "sortNum": 65536,
        //     "tagList": []
        //   }
        // ]
      } catch (error) {
        console.error('获取标签数据失败:', error)
        return null
      }
    },

    // 处理静态标签树形数据
    processStaticTagTreeData(treeDataList) {
      if (!treeDataList || !Array.isArray(treeDataList)) {
        console.warn('静态标签数据格式不正确，应为数组格式')
        return
      }

      // 重置静态标签数据
      this.staticRootTagInfoList = []
      this.staticTagCategories = []

      // 遍历所有根节点
      treeDataList.forEach(treeData => {
        if (!treeData || !treeData.children) {
          console.warn('根节点数据格式不正确')
          return
        }

        // 保存根节点信息
        const rootInfo = {
          id: treeData.id,
          code: treeData.code,
          name: treeData.nameCn || treeData.name,
          enabled: treeData.enabled
        }
        this.staticRootTagInfoList.push(rootInfo)

        // 遍历第二级分类
        treeData.children.forEach(category => {
          if (category.tagList && category.tagList.length > 0) {
            // 转换标签格式
            const tags = category.tagList.map(tag => ({
              id: tag.id,
              code: tag.code,
              name: tag.nameCn || tag.name,
              type: category.code, // 使用分类code作为type
              groupId: tag.groupId,
              groupName: tag.groupNameCn || tag.groupName,
              acquisitionType: tag.acquisitionType,
              enabled: tag.enabled,
              followingDuration: tag.followingDuration,
              previousDuration: tag.previousDuration,
              poi: tag.poi,
              supportTrigger: tag.supportTrigger,
              supportVoice: tag.supportVoice,
              useCase: tag.useCase,
              version: tag.version,
              rootId: treeData.id, // 添加根节点ID，用于分组
              rootName: treeData.nameCn || treeData.name
            }))

            // 添加到静态分类列表
            this.staticTagCategories.push({
              type: category.code,
              name: category.nameCn || category.name,
              id: category.id,
              enabled: category.enabled,
              tags: tags,
              rootId: treeData.id, // 添加根节点ID，用于分组
              rootName: treeData.nameCn || treeData.name
            })

            // 初始化分类选中状态
            this.categoryCheckedMap[category.code] = false
          }
        })
      })
    },

    // 处理自车标签树形数据
    processSelfTagTreeData(treeDataList) {
      if (!treeDataList || !Array.isArray(treeDataList)) {
        console.warn('自车标签数据格式不正确，应为数组格式')
        return
      }

      // 重置自车标签数据
      this.selfRootTagInfoList = []
      this.selfTagCategories = []

      // 遍历所有根节点
      treeDataList.forEach(treeData => {
        if (!treeData || !treeData.children) {
          console.warn('根节点数据格式不正确')
          return
        }

        // 保存根节点信息
        const rootInfo = {
          id: treeData.id,
          code: treeData.code,
          name: treeData.nameCn || treeData.name,
          enabled: treeData.enabled
        }
        this.selfRootTagInfoList.push(rootInfo)

        // 遍历第二级分类
        treeData.children.forEach(category => {
          if (category.tagList && category.tagList.length > 0) {
            // 转换标签格式
            const tags = category.tagList.map(tag => ({
              id: tag.id,
              code: tag.code,
              name: tag.nameCn || tag.name,
              type: category.code, // 使用分类code作为type
              groupId: tag.groupId,
              groupName: tag.groupNameCn || tag.groupName,
              acquisitionType: tag.acquisitionType,
              enabled: tag.enabled,
              followingDuration: tag.followingDuration,
              previousDuration: tag.previousDuration,
              poi: tag.poi,
              supportTrigger: tag.supportTrigger,
              supportVoice: tag.supportVoice,
              useCase: tag.useCase,
              version: tag.version,
              rootId: treeData.id, // 添加根节点ID，用于分组
              rootName: treeData.nameCn || treeData.name
            }))

            // 添加到自车分类列表
            this.selfTagCategories.push({
              type: category.code,
              name: category.nameCn || category.name,
              id: category.id,
              enabled: category.enabled,
              tags: tags,
              rootId: treeData.id, // 添加根节点ID，用于分组
              rootName: treeData.nameCn || treeData.name
            })

            // 初始化分类选中状态
            this.selfCategoryCheckedMap[category.code] = false
          }
        })
      })
    },

    // 初始化标签的缓冲区设置
    initBufferSetting(tagId) {
      if (!this.bufferSettings[tagId]) {
        this.bufferSettings[tagId] = {
          enabled: false,
          type: 'distance',
          value: 0
        }
      }
    },

    // 更新缓冲区值
    handleBufferValueChange(tagId, value) {
      if (this.bufferSettings[tagId]) {
        this.bufferSettings[tagId].value = value
      }
    },

    // 更新缓冲区类型
    handleBufferTypeChange(tagId, type) {
      if (this.bufferSettings[tagId]) {
        this.bufferSettings[tagId].type = type
        // 重置值为0，避免切换类型时值超出范围
        this.bufferSettings[tagId].value = 0
      }
    },

    // 检查标签是否被选中
    isTagChecked(tag) {
      return this.checkedTagIds.includes(String(tag.id))
    },

    // 处理类别复选框变化
    handleCategoryChange(cat, checked) {
      if (!cat.tags || cat.tags.length === 0) return

      const ids = cat.tags.map(tag => String(tag.id))
      if (checked) {
        // 选中分类下所有标签
        const newIds = ids.filter(id => !this.checkedTagIds.includes(id))
        this.checkedTagIds = [...this.checkedTagIds, ...newIds]
        // 初始化新选中标签的缓冲区设置和添加到已选标签列表
        newIds.forEach(id => {
          // 初始化缓冲区设置
          if (!this.bufferSettings[id]) {
            this.bufferSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
          // 添加到已选标签列表
          const tag = this.findTagById(id)
          if (tag && !this.selectedStaticTags.some(t => t.id === tag.id)) {
            this.selectedStaticTags.push(tag)
          }
        })
      } else {
        // 取消选中分类下所有标签
        this.checkedTagIds = this.checkedTagIds.filter(id => !ids.includes(id))
        // 清除取消选中标签的缓冲区设置和已选标签
        ids.forEach(id => {
          delete this.bufferSettings[id]
          this.selectedStaticTags = this.selectedStaticTags.filter(tag => String(tag.id) !== id)
        })
      }
    },

    // 移除静态标签
    removeStaticTag(tag) {
      if(this.formReadonly) return
      const tagId = String(tag.id)
      this.checkedTagIds = this.checkedTagIds.filter(id => id !== tagId)
      delete this.bufferSettings[tagId]
      this.selectedStaticTags = this.selectedStaticTags.filter(t => String(t.id) !== tagId)
      // 更新分类选中状态
      this.updateCategoryCheckedStatus()
    },

    // 重置静态标签
    resetStaticTags() {
      this.selectedStaticTags = []
      this.checkedTagIds = []
      this.bufferSettings = {}
      this.rootChecked = false
      this.rootIndeterminate = false
      // 重置分类选中状态
      this.staticTagCategories.forEach(cat => {
        if (cat.type) {
          this.categoryCheckedMap[cat.type] = false
        }
      })
    },

    // 更新分类选中状态
    updateCategoryCheckedStatus() {
      this.staticTagCategories.forEach(cat => {
        if (cat.tags && cat.tags.length > 0) {
          const catTagIds = cat.tags.map(tag => String(tag.id))
          const checkedCount = catTagIds.filter(id => this.checkedTagIds.includes(id)).length
          this.categoryCheckedMap[cat.type] = checkedCount === cat.tags.length && cat.tags.length > 0
        }
      })
    },

    // 更新自车分类选中状态
    updateSelfCategoryCheckedStatus() {
      this.selfTagCategories.forEach(cat => {
        if (cat.tags && cat.tags.length > 0) {
          const catTagIds = cat.tags.map(tag => String(tag.id))
          const checkedCount = catTagIds.filter(id => this.selfCheckedTagIds.includes(id)).length
          this.selfCategoryCheckedMap[cat.type] = checkedCount === cat.tags.length && cat.tags.length > 0
        }
      })
    },

    // 查找静态标签
    findTagById(id) {
      let foundTag = null
      this.staticTagCategories.forEach(cat => {
        if (cat.tags && cat.tags.length > 0) {
          const tag = cat.tags.find(t => String(t.id) === id)
          if (tag) foundTag = tag
        }
      })
      return foundTag
    },

    // 查找自车标签
    findSelfTagById(id) {
      let foundTag = null
      this.selfTagCategories.forEach(cat => {
        if (cat.tags && cat.tags.length > 0) {
          const tag = cat.tags.find(t => String(t.id) === id)
          if (tag) foundTag = tag
        }
      })
      return foundTag
    },

    // 生成标签显示文本
    tagLabelWithBuffer(tag) {
      const tagId = String(tag.id)
      const parts = [tag.name]

      if (this.bufferSettings[tagId]?.enabled) {
        const buffer = this.bufferSettings[tagId]
        const unit = buffer.type === 'distance' ? 'm' : '个'
        parts.push(`匹配缓冲区(${buffer.type === 'distance' ? '距离' : '车道'}/${buffer.value}${unit})`)
      }

      return parts.join(' ')
    },

    // 自车筛选相关方法
    // 获取所有可选标签
    getAllTags() {
      const tags = []
      this.staticTagCategories.forEach(cat => {
        if (cat.tags && cat.tags.length > 0) {
          tags.push(...cat.tags)
        }
      })
      return tags
    },

    // 获取所有自车标签
    getAllSelfTags() {
      const tags = []
      this.selfTagCategories.forEach(cat => {
        if (cat.tags && cat.tags.length > 0) {
          tags.push(...cat.tags)
        }
      })
      return tags
    },

    // 获取已选自车标签
    getSelfSelectedTags() {
      return this.getAllSelfTags().filter(tag => this.selfCheckedTagIds.includes(String(tag.id)))
    },

    // 生成自车标签显示文本
    selfTagLabelWithSettings(tag) {
      const tagId = String(tag.id)
      const parts = [tag.name]

      if (this.selfBufferSettings[tagId]?.enabled) {
        const buffer = this.selfBufferSettings[tagId]
        const unit = buffer.type === 'distance' ? 'm' : '个'
        parts.push(`匹配缓冲区(${buffer.type === 'distance' ? '距离' : '车道'}/${buffer.value}${unit})`)
      }

      if (this.selfPatchSettings[tagId]?.enabled) {
        const patch = this.selfPatchSettings[tagId]
        const unit = patch.type === 'distance' ? 'm' : 's'
        parts.push(`数据补全(${patch.type === 'distance' ? '距离' : '时间'}/${patch.value}${unit})`)
      }

      return parts.join(' ')
    },

    // 移除自车标签
    removeSelfTag(tag) {
      if(this.formReadonly) return
      const tagId = String(tag.id)
      this.selfCheckedTagIds = this.selfCheckedTagIds.filter(id => id !== tagId)
      // 更新自车分类选中状态
      this.updateSelfCategoryCheckedStatus()
    },

    // 重置自车标签
    resetSelfTags() {
      this.selfCheckedTagIds = []
      this.selfBufferSettings = {}
      this.selfPatchSettings = {}
      this.selfAllChecked = false
      this.selfIndeterminate = false
      this.selfRootChecked = false
      this.selfRootIndeterminate = false
      // 重置自车分类选中状态
      this.selfTagCategories.forEach(cat => {
        if (cat.type) {
          this.selfCategoryCheckedMap[cat.type] = false
        }
      })
    },

    // 检查自车标签是否被选中
    isSelfTagChecked(tag) {
      return this.selfCheckedTagIds.includes(String(tag.id))
    },

    // 处理自车全选变化
    handleSelfCheckAllChange(val) {
      this.selfCheckedTagIds = val ? this.getAllSelfTags().map(tag => String(tag.id)) : []
      this.selfIndeterminate = false
    },

    // 处理自车分类复选框变化
    handleSelfCategoryChange(cat, checked) {
      if (!cat.tags || cat.tags.length === 0) return

      const ids = cat.tags.map(tag => String(tag.id))
      if (checked) {
        // 选中分类下所有标签
        const newIds = ids.filter(id => !this.selfCheckedTagIds.includes(id))
        this.selfCheckedTagIds = [...this.selfCheckedTagIds, ...newIds]
        // 初始化新选中标签的设置
        newIds.forEach(id => {
          if (!this.selfBufferSettings[id]) {
            this.selfBufferSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
          if (!this.selfPatchSettings[id]) {
            this.selfPatchSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
        })
      } else {
        // 取消选中分类下所有标签
        this.selfCheckedTagIds = this.selfCheckedTagIds.filter(id => !ids.includes(id))
        // 清除取消选中标签的设置
        ids.forEach(id => {
          delete this.selfBufferSettings[id]
          delete this.selfPatchSettings[id]
        })
      }
    },

    // 根据根节点ID获取静态分类
    getCategoriesByRootId(rootId) {
      return this.staticTagCategories.filter(cat => cat.rootId === rootId)
    },

    // 根据根节点ID获取自车分类
    getSelfCategoriesByRootId(rootId) {
      return this.selfTagCategories.filter(cat => cat.rootId === rootId)
    },

    // 获取根节点选中状态（静态筛选）
    getRootCheckedState(rootId) {
      const categories = this.getCategoriesByRootId(rootId)
      const allTagIds = []
      categories.forEach(cat => {
        cat.tags.forEach(tag => {
          allTagIds.push(String(tag.id))
        })
      })
      const checkedCount = allTagIds.filter(id => this.checkedTagIds.includes(id)).length
      return {
        checked: checkedCount === allTagIds.length && allTagIds.length > 0,
        indeterminate: checkedCount > 0 && checkedCount < allTagIds.length
      }
    },

    // 获取根节点选中状态（自车筛选）
    getSelfRootCheckedState(rootId) {
      const categories = this.getSelfCategoriesByRootId(rootId)
      const allTagIds = []
      categories.forEach(cat => {
        cat.tags.forEach(tag => {
          allTagIds.push(String(tag.id))
        })
      })
      const checkedCount = allTagIds.filter(id => this.selfCheckedTagIds.includes(id)).length
      return {
        checked: checkedCount === allTagIds.length && allTagIds.length > 0,
        indeterminate: checkedCount > 0 && checkedCount < allTagIds.length
      }
    },

    // 处理根节点复选框变化（静态筛选）
    handleRootChange(rootId, checked) {
      const categories = this.getCategoriesByRootId(rootId)
      const allTagIds = []
      categories.forEach(cat => {
        cat.tags.forEach(tag => {
          allTagIds.push(String(tag.id))
        })
      })

      if (checked) {
        // 选中该根节点下所有标签
        const newIds = allTagIds.filter(id => !this.checkedTagIds.includes(id))
        this.checkedTagIds = [...this.checkedTagIds, ...newIds]
        // 初始化新选中标签的缓冲区设置和添加到已选标签列表
        newIds.forEach(id => {
          if (!this.bufferSettings[id]) {
            this.bufferSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
          const tag = this.findTagById(id)
          if (tag && !this.selectedStaticTags.some(t => t.id === tag.id)) {
            this.selectedStaticTags.push(tag)
          }
        })
      } else {
        // 取消选中该根节点下所有标签
        this.checkedTagIds = this.checkedTagIds.filter(id => !allTagIds.includes(id))
        // 清除取消选中标签的缓冲区设置和已选标签
        allTagIds.forEach(id => {
          delete this.bufferSettings[id]
          this.selectedStaticTags = this.selectedStaticTags.filter(tag => String(tag.id) !== id)
        })
      }
    },

    // 处理根节点复选框变化（自车筛选）
    handleSelfRootChange(rootId, checked) {
      const categories = this.getSelfCategoriesByRootId(rootId)
      const allTagIds = []
      categories.forEach(cat => {
        cat.tags.forEach(tag => {
          allTagIds.push(String(tag.id))
        })
      })

      if (checked) {
        // 选中该根节点下所有标签
        const newIds = allTagIds.filter(id => !this.selfCheckedTagIds.includes(id))
        this.selfCheckedTagIds = [...this.selfCheckedTagIds, ...newIds]
        // 初始化新选中标签的设置
        newIds.forEach(id => {
          if (!this.selfBufferSettings[id]) {
            this.selfBufferSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
          if (!this.selfPatchSettings[id]) {
            this.selfPatchSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
        })
      } else {
        // 取消选中该根节点下所有标签
        this.selfCheckedTagIds = this.selfCheckedTagIds.filter(id => !allTagIds.includes(id))
        // 清除取消选中标签的设置
        allTagIds.forEach(id => {
          delete this.selfBufferSettings[id]
          delete this.selfPatchSettings[id]
        })
      }
    },

    // 恢复筛选数据
    restoreFilteringData() {
      // 解析规则设置JSON字符串
      let ruleSettings = null
      try {
        if (this.formData.ruleSettings && typeof this.formData.ruleSettings === 'string') {
          ruleSettings = JSON.parse(this.formData.ruleSettings)
        } else if (this.formData.ruleSettings && typeof this.formData.ruleSettings === 'object') {
          ruleSettings = this.formData.ruleSettings
        }
      } catch (error) {
        console.warn('解析规则设置失败:', error)
        ruleSettings = {
          staticRules: [],
          egoRules: [],
          othersRules: []
        }
      }

      if (!ruleSettings) {
        ruleSettings = {
          staticRules: [],
          egoRules: [],
          othersRules: []
        }
      }

      // 如果使用默认规则，恢复选中状态
      if (this.formData.enableRule === 1) {
        // 恢复静态筛选数据
        if (ruleSettings.staticRules && ruleSettings.staticRules.length > 0) {
          ruleSettings.staticRules.forEach(rule => {
            // 根据tagCode找到对应的标签
            const tag = this.getAllTags().find(t => t.code === rule.tagCode)
            if (tag) {
              const tagId = String(tag.id)
              // 添加到选中列表
              if (!this.checkedTagIds.includes(tagId)) {
                this.checkedTagIds.push(tagId)
              }
              if (!this.selectedStaticTags.some(t => t.id === tag.id)) {
                this.selectedStaticTags.push(tag)
              }
              // 恢复缓冲区设置 - 确保所有字段都有值
              this.bufferSettings[tagId] = {
                enabled: !!(rule.bufferType && rule.bufferValue !== null && rule.bufferValue !== undefined),
                type: rule.bufferType || 'distance', // 确保type有值
                value: (rule.bufferValue !== null && rule.bufferValue !== undefined) ? rule.bufferValue : 0
              }
            }
          })
        }

        // 恢复自车筛选数据
        if (ruleSettings.egoRules && ruleSettings.egoRules.length > 0) {
          ruleSettings.egoRules.forEach(rule => {
            // 根据tagCode找到对应的标签
            const tag = this.getAllSelfTags().find(t => t.code === rule.tagCode)
            if (tag) {
              const tagId = String(tag.id)
              // 添加到选中列表
              if (!this.selfCheckedTagIds.includes(tagId)) {
                this.selfCheckedTagIds.push(tagId)
              }
              // 恢复缓冲区设置 - 确保所有字段都有值
              this.selfBufferSettings[tagId] = {
                enabled: !!(rule.bufferType && rule.bufferValue !== null && rule.bufferValue !== undefined),
                type: rule.bufferType || 'distance',
                value: (rule.bufferValue !== null && rule.bufferValue !== undefined) ? rule.bufferValue : 0
              }

              // 恢复数据补全设置 - 确保所有字段都有值
              this.selfPatchSettings[tagId] = {
                enabled: !!(rule.completeRuleType && rule.completeRuleValue !== null && rule.completeRuleValue !== undefined),
                type: rule.completeRuleType || 'distance',
                value: (rule.completeRuleValue !== null && rule.completeRuleValue !== undefined) ? rule.completeRuleValue : 0
              }
            }
          })
        }

        // 更新分类选中状态
        this.$nextTick(() => {
          this.updateCategoryCheckedStatus()
          this.updateSelfCategoryCheckedStatus()
        })
      }
    },

    // JSON查看相关方法
    viewJsonData(jsonData, title) {
      try {
        // 如果是字符串，尝试解析为JSON
        if (typeof jsonData === 'string') {
          this.jsonData = JSON.parse(jsonData)
        } else {
          this.jsonData = jsonData
        }
        this.jsonDialogTitle = title
        this.jsonDialogVisible = true
      } catch (error) {
        console.error('JSON解析失败:', error)
        this.$message.error('JSON数据格式错误')
      }
    },

    closeJsonDialog() {
      this.jsonDialogVisible = false
      this.jsonData = null
      this.jsonDialogTitle = ''
    },

    copyJsonData() {
      try {
        const jsonString = JSON.stringify(this.jsonData, null, 2)
        navigator.clipboard.writeText(jsonString).then(() => {
          this.$message.success('JSON数据已复制到剪贴板')
        }).catch(() => {
          // 降级方案
          const textArea = document.createElement('textarea')
          textArea.value = jsonString
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
          this.$message.success('JSON数据已复制到剪贴板')
        })
      } catch (error) {
        this.$message.error('复制失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ltw-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
}

.ltw-search-container {
  margin-right: 16px;
  min-width: 200px;
  display: flex;
  align-items: center;
}

.button-group {
  display: flex;
}

.item-label {
  width: 60px;
}

/* 表单部分样式 */
.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;

  .form-item-flex {
    flex: 1;
    min-width: 300px;
    padding: 0 10px;
    margin-bottom: 18px;
  }
}

/* 静态筛选和自车筛选样式 */
.filtering-section {
  margin-top: 10px;
  margin-bottom: 20px;
}

.info-box {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 16px;
  min-height: 80px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background: #fafbfc;
}
.static-tag-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.self-tag-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.root-section {
  margin-bottom: 24px;
}

.root-tag-card {
  margin-bottom: 16px;
  border: 2px solid #409eff;
  border-radius: 8px;

  :deep(.el-card__header) {
    padding: 16px 20px;
    background-color: #409eff;//linear-gradient(135deg, #409eff 0%, #67c23a 100%);
    color: white;
    font-weight: 600;

    .card-header {
      .el-checkbox {
        color: white;

        .el-checkbox__label {
          color: white;
          font-weight: 600;
        }

        .el-checkbox__input.is-checked .el-checkbox__inner {
          background-color: white;
          border-color: white;

          &::after {
            border-color: #409eff;
          }
        }

        .el-checkbox__input.is-indeterminate .el-checkbox__inner {
          background-color: white;
          border-color: white;

          &::before {
            background-color: #409eff;
          }
        }
      }
    }
  }

  :deep(.el-card__body) {
    padding: 20px;
    background-color: #f8f9fa;
  }
}

.tag-card {
  margin-bottom: 8px;
  border-radius: 6px;
  overflow: hidden;

  :deep(.el-card__header) {
    padding: 12px 16px;
    //background-color: #f5f7fa;
  }

  :deep(.el-card__body) {
    padding: 16px;
    display: flex;
    flex-wrap: wrap;
    gap:35px;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
/* 自车标签网格样式已移除，现在使用分类卡片布局 */

.tag-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;

  .tag-checkbox {
    display: flex;
    align-items: center;
  }
}

.tag-buffer-settings {
  margin-left: 24px;
  margin-top: 8px;

  .tag-box {
    display: flex;
    align-items: center;
    margin-top: 8px;
    flex-wrap: wrap;
  }

  .el-radio-group {
    margin-right: 8px;
    //margin-bottom: 8px;
  }

  .number-input-wrapper {
    display: inline-block;

    :deep(.el-input-number) {
      width: 100px;
    }
  }

  .unit {
    color: #606266;
    font-size: 14px;
    margin-left: 4px;
  }
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
}

:deep(.el-input-number.el-input-number--small) {
  width: 100px !important;
}

/* 对话框样式调整 */
:deep(.data-mining-define-dialog) {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
  }

  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
  }

  .el-dialog__footer {
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
}

// JSON查看对话框样式
.json-dialog {
  .json-dialog-header {
    margin-bottom: 16px;

    .json-dialog-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
    }
  }

  .json-viewer-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 16px;
    background-color: #fafafa;

    .no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  }
}

// vue-json-viewer 样式覆盖
:deep(.jv-container) {
  .jv-code {
    padding: 0;
  }

  .jv-item {
    .jv-key {
      color: #e96900;
      font-weight: 600;
    }

    .jv-string {
      color: #42b883;
    }

    .jv-number {
      color: #1976d2;
    }

    .jv-boolean {
      color: #ff5722;
    }

    .jv-null {
      color: #9e9e9e;
    }
  }
}
</style>
