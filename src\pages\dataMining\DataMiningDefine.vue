<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <div class="item-label">名称：</div>
          <ltw-input :placeholder="$t('请输入名称')" v-model="queryParam.name" clearable @clear="refresh"> </ltw-input>
        </div>
        <div class="ltw-search-container ltw-tool-container">
          <div class="item-label">类型：</div>
          <el-select
            v-model="queryParam.type"
            clearable
            @clear="refresh"
            :placeholder="$t('请选择类型')"
            style="width: 100%"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="ltw-search-container ltw-tool-container">
          <div style="width: 140px">是否自动化：</div>
          <el-select
            v-model="queryParam.auto"
            clearable
            @clear="refresh"
            :placeholder="$t('请选择是否自动化')"
            style="width: 100%"
          >
            <el-option
              v-for="item in autoOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button type="primary"><ltw-icon icon-code="el-icon-search"></ltw-icon>查询</el-button>
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
        </div>
      </div>
      <el-table ref="tableRef" :data="pageData.records" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column prop="name" :label="$t('名称')" min-width="120"></el-table-column>
        <el-table-column prop="type" :label="$t('类型')" min-width="100">
          <template #default="scope">
            {{ getTypeLabel(scope.row.type) }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="workflowDefinitionName" :label="$t('关联工作流')" min-width="150"></el-table-column> -->
        <el-table-column prop="auto" :label="$t('是否自动化')" min-width="100">
          <template #default="scope">
            <el-tag :type="scope.row.auto === 1 ? 'success' : 'info'">
              {{ scope.row.auto === 1 ? $t('是') : $t('否') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('创建时间')" min-width="160"></el-table-column>
        <el-table-column :label="$t('操作')" fixed="right" width="150">
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button :type="item.buttonStyleType" @click="executeButtonMethod(item, scope.row)">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>

      <!-- 表单对话框 -->
      <el-dialog v-model="dialogVisible" :title="dialogTitle" width="900px" destroy-on-close @closed="dialogClosed">
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
          <el-form-item :label="$t('名称')" prop="name">
            <ltw-input v-model="formData.name" :disabled="formReadonly"></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('类型')" prop="type">
            <el-select v-model="formData.type" :disabled="formReadonly" style="width: 100%">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item :label="$t('关联工作流')" prop="workflowDefinitionId">
            <el-select
              v-model="formData.workflowDefinitionId"
              :disabled="formReadonly"
              filterable
              style="width: 100%"
              @change="handleWorkflowChange"
            >
              <el-option
                v-for="item in workflowDefinitionList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item :label="$t('是否自动化')" prop="auto">
            <el-radio-group v-model="formData.auto" :disabled="formReadonly">
              <el-radio :label="1">{{ $t('是') }}</el-radio>
              <el-radio :label="0">{{ $t('否') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('是否使用默认规则')" prop="defaultRuleIds">
            <el-radio-group v-model="formData.defaultRuleIds" :disabled="formReadonly">
              <el-radio :label="1">{{ $t('是') }}</el-radio>
              <el-radio :label="0">{{ $t('否') }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 静态筛选 -->
          <div v-if="formData.defaultRuleIds === 1" class="filtering-section">

            <!-- 静态标签输入框区域 -->
            <el-form-item label="静态标签（支持多选）" :disabled="formReadonly">
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                <div style="flex: 1"></div>
                <el-button type="danger" size="small" @click="resetStaticTags" :disabled="formReadonly">重置</el-button>
              </div>
            </el-form-item>

            <div class="info-box">
              <div style="flex: 1; min-height: 40px">
                <el-tag
                  v-for="tag in selectedStaticTags"
                  :key="tag.id"
                  closable
                  :disabled="formReadonly"
                  @close="removeStaticTag(tag)"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ tagLabelWithBuffer(tag) }}
                </el-tag>
                <span v-if="selectedStaticTags.length === 0" style="color: #bbb">请选择标签</span>
              </div>
            </div>

            <!-- 标签分类卡片 -->
            <div class="static-tag-cards">
            <el-card v-for="cat in staticTagCategories" :key="cat.type" class="tag-card" shadow="never" :disabled="formReadonly">
              <template #header>
                <div class="card-header">
                  <el-checkbox
                    v-model="categoryCheckedMap[cat.type]"
                    :indeterminate="categoryCheckedStates[cat.type]?.indeterminate"
                    @change="(val) => handleCategoryChange(cat, val)"
                    :disabled="formReadonly"
                  >
                    {{ cat.name }}
                  </el-checkbox>
                </div>
              </template>

              <div v-for="tag in cat.tags" :key="tag.id" class="tag-item">
                <div class="tag-checkbox">
                  <el-checkbox v-model="checkedTagIds" :label="String(tag.id)" :disabled="formReadonly">
                    {{ tag.name }}
                  </el-checkbox>
                </div>
                <div v-if="isTagChecked(tag)" class="tag-buffer-settings">
                  <div>
                    <el-checkbox
                      v-model="bufferSettings[String(tag.id)].enabled"
                      :disabled="formReadonly"
                    >匹配缓冲区</el-checkbox>
                  </div>
                  <div class="tag-box" v-if="bufferSettings[String(tag.id)]?.enabled">
                    <el-radio-group
                      v-model="bufferSettings[String(tag.id)].type"
                      size="small"
                      @change="(val) => handleBufferTypeChange(String(tag.id), val)"
                      :disabled="formReadonly"
                    >
                      <el-radio-button label="distance">距离</el-radio-button>
                      <el-radio-button label="lane">车道</el-radio-button>
                    </el-radio-group>
                    <div class="number-input-wrapper">
                      <el-input-number
                        v-model="bufferSettings[String(tag.id)].value"
                        :min="0"
                        :max="bufferSettings[String(tag.id)].type === 'distance' ? 1000 : 10"
                        size="small"
                        @change="(val) => handleBufferValueChange(String(tag.id), val)"
                        :disabled="formReadonly"
                      ></el-input-number>
                      <span class="unit">{{ bufferSettings[String(tag.id)].type === 'distance' ? 'm' : '个' }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
          </div>

          <!-- 自车筛选 -->
          <div v-if="formData.defaultRuleIds === 1" class="filtering-section">
            <h3>自车筛选</h3>
            <!-- 上方标签输入框区域 -->
            <el-form-item label="自车标签（支持多选）" :disabled="formReadonly">
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                <div style="flex: 1"></div>
                <el-button type="danger" size="small" @click="resetSelfTags" :disabled="formReadonly">重置</el-button>
              </div>
            </el-form-item>

            <div class="info-box">
              <div style="flex: 1; min-height: 40px">
                <el-tag
                  v-for="tag in getSelfSelectedTags()"
                  :key="tag.id"
                  closable
                  :disabled="formReadonly"
                  @close="removeSelfTag(tag)"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ selfTagLabelWithSettings(tag) }}
                </el-tag>
                <span v-if="getSelfSelectedTags().length === 0" style="color: #bbb">请选择标签</span>
              </div>
            </div>

            <!-- 标签选择区域 -->
            <el-card class="tag-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <el-checkbox
                    v-model="selfAllChecked"
                    :indeterminate="selfIndeterminate"
                    @change="handleSelfCheckAllChange"
                    :disabled="formReadonly"
                  >
                    全选
                  </el-checkbox>
                </div>
              </template>

              <div class="self-tag-list-grid">
                <div v-for="tag in getAllTags()" :key="tag.id" class="self-tag-item">
                  <el-checkbox v-model="selfCheckedTagIds" :label="String(tag.id)" :disabled="formReadonly">
                    {{ tag.name }}
                  </el-checkbox>

                  <div v-if="isSelfTagChecked(tag)" class="tag-buffer-settings">
                    <!-- 匹配缓冲区设置 -->
                    <div>
                      <el-checkbox
                        v-model="selfBufferSettings[String(tag.id)].enabled"
                        :disabled="formReadonly"
                      >匹配缓冲区</el-checkbox>
                    </div>
                    <div class="tag-box" v-if="selfBufferSettings[String(tag.id)]?.enabled">
                      <el-radio-group
                        v-model="selfBufferSettings[String(tag.id)].type"
                        size="small"
                        :disabled="formReadonly"
                      >
                        <el-radio-button label="distance">距离</el-radio-button>
                        <el-radio-button label="lane">车道</el-radio-button>
                      </el-radio-group>
                      <div class="number-input-wrapper">
                        <el-input-number
                          v-model="selfBufferSettings[String(tag.id)].value"
                          :min="0"
                          :max="selfBufferSettings[String(tag.id)].type === 'distance' ? 1000 : 10"
                          size="small"
                          :disabled="formReadonly"
                        ></el-input-number>
                        <span class="unit">{{ selfBufferSettings[String(tag.id)].type === 'distance' ? 'm' : '个' }}</span>
                      </div>
                    </div>

                    <!-- 数据补全设置 -->
                    <div style="margin-top: 8px;">
                      <el-checkbox
                        v-model="selfPatchSettings[String(tag.id)].enabled"
                        :disabled="formReadonly"
                      >数据补全</el-checkbox>
                    </div>
                    <div class="tag-box" v-if="selfPatchSettings[String(tag.id)]?.enabled">
                      <el-radio-group
                        v-model="selfPatchSettings[String(tag.id)].type"
                        size="small"
                        :disabled="formReadonly"
                      >
                        <el-radio-button label="distance">距离</el-radio-button>
                        <el-radio-button label="time">时间</el-radio-button>
                      </el-radio-group>
                      <div class="number-input-wrapper">
                        <el-input-number
                          v-model="selfPatchSettings[String(tag.id)].value"
                          :min="0"
                          :max="1000"
                          size="small"
                          :disabled="formReadonly"
                        ></el-input-number>
                        <span class="unit">{{ selfPatchSettings[String(tag.id)].type === 'distance' ? 'm' : 's' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <el-form-item :label="$t('描述')" prop="description">
            <el-input v-model="formData.description" :disabled="formReadonly" type="textarea" :rows="3"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">{{ $t('取消') }}</el-button>
            <el-button type="primary" @click="saveForm" v-if="!formReadonly">{{ $t('确定') }}</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { showToast, showConfirmToast } from '@/plugins/util'
import {
  pageDataMiningDefine,
  getDataMiningDefine,
  saveDataMiningDefine,
  updateDataMiningDefine,
  deleteDataMiningDefine
} from '@/apis/dataMining/data_mining_define'
import { listDataMiningRule } from '@/apis/dataMining/data_mining_rule'

import BASE_CONSTANT from '@/plugins/constants/base-constant'

const defaultFormData = {
  name: '',
  type:'',
  workflowDefinitionId: '',
  workflowDefinitionName: '',
  auto: 0, // 修改为数字类型，默认为0（否）
  defaultRuleIds: 0,
  staticFiltering: [], // 静态筛选
  selfVehicleFiltering: [], // 自车筛选
  description: ''
}

export default {
  name: 'DataMiningDefine',
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0,
        records: []
      },
      queryParam: {
        current: 1,
        size: 10,
        name: '',
        type: '',
        auto: '' // 修改查询参数名
      },
      selectedData: [],
      dialogVisible: false,
      dialogTitle: '',
      dialogStatus: '',
      formData: Object.assign({}, defaultFormData),
      formRules: {
        name: [{ required: true, message: this.$t('请输入名称'), trigger: 'blur' }],
        type: [{ required: true, message: this.$t('请选择类型'), trigger: 'change' }],
       // workflowDefinitionId: [{ required: true, message: this.$t('请选择关联工作流'), trigger: 'change' }]
      },
      formReadonly: false,
      typeOptions: [
        { value: 0, label: 'Common' },
        { value: 1, label: 'ER' },
        // { value: 2, label: 'Defect' }
      ],
      workflowDefinitionList: [],
      dataMiningRuleList: [],
      autoOptions: [
        { value: 1, label: this.$t('是') },
        { value: 0, label: this.$t('否') }
      ],
      // 静态筛选相关
      staticTagCategories: [
        { type: 'text', name: '文字标签', tags: [] },
        { type: 'number', name: '数字标签', tags: [] },
        { type: 'symbol', name: '符号标签', tags: [] },
        { type: 'arrow', name: '箭头标签', tags: [] }
      ],
      selectedStaticTags: [], // 已选静态标签
      checkedTagIds: [], // 选中的标签ID
      bufferSettings: {}, // 缓冲区设置
      categoryCheckedMap: {}, // 分类选中状态

      // 自车筛选相关
      selfCheckedTagIds: [], // 自车选中的标签ID
      selfBufferSettings: {}, // 自车缓冲区设置
      selfPatchSettings: {}, // 自车数据补全设置
      selfAllChecked: false, // 自车全选状态
      selfIndeterminate: false // 自车半选状态
    }
  },
  created() {
    this.initFunctionList()
    this.initPermission()
    this.query()
    this.loadStaticTags() // 加载静态标签数据
  },
  computed: {
    // 计算每个类别的选中状态
    categoryCheckedStates() {
      const states = {}
      this.staticTagCategories.forEach(cat => {
        const allTagIds = cat.tags.map(tag => String(tag.id))
        const checkedCount = allTagIds.filter(id => this.checkedTagIds.includes(id)).length
        states[cat.type] = {
          checked: checkedCount === cat.tags.length && cat.tags.length > 0,
          indeterminate: checkedCount > 0 && checkedCount < cat.tags.length
        }
      })
      return states
    }
  },
  watch: {
    'formData.defaultRuleIds': {
      handler(newVal) {
        // 当选择使用默认规则时，添加静态筛选和自车筛选
        if (newVal === 1) {
          // 如果之前没有设置过，则设置默认值
          if (!this.formData.staticFiltering || this.formData.staticFiltering.length === 0) {
            // 默认选择前两个静态标签
            const defaultTags = this.staticTagCategories[0].tags.slice(0, 2)
            if (defaultTags.length > 0) {
              this.checkedTagIds = defaultTags.map(tag => String(tag.id))
            }
          }
          if (!this.formData.selfVehicleFiltering || this.formData.selfVehicleFiltering.length === 0) {
            // 默认选择第一个自车标签
            const allTags = this.getAllTags()
            if (allTags.length > 0) {
              this.selfCheckedTagIds = [String(allTags[0].id)]
            }
          }
        }
      },
      immediate: true
    },
    // 监听选中的静态标签变化
    checkedTagIds: {
      handler(newVal, oldVal) {
        if (!oldVal) return // 初始化时跳过

        // 初始化新选中标签的缓冲区设置
        newVal.forEach(id => {
          if (!this.bufferSettings[id]) {
            this.bufferSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
        })

        // 找出新增的标签
        const addedTags = newVal.filter(id => !oldVal.includes(id))

        // 找出移除的标签
        const removedTags = oldVal.filter(id => !newVal.includes(id))

        // 为新增的标签初始化缓冲区设置并添加到已选标签列表
        addedTags.forEach(tagId => {
          this.initBufferSetting(tagId)
          // 添加到已选标签列表
          const tag = this.findTagById(tagId)
          if (tag && !this.selectedStaticTags.some(t => t.id === tag.id)) {
            this.selectedStaticTags.push(tag)
          }
        })

        // 处理移除的标签
        removedTags.forEach(tagId => {
          // 从已选标签列表中移除
          this.selectedStaticTags = this.selectedStaticTags.filter(tag => String(tag.id) !== tagId)
          // 清除缓冲区设置
          delete this.bufferSettings[tagId]
        })

        // 更新表单数据
        this.formData.staticFiltering = this.selectedStaticTags.map(tag => tag.id)
      },
      deep: true
    },
    // 监听自车标签选中状态变化
    selfCheckedTagIds: {
      handler(newVal, oldVal) {
        // 初始化新选中标签的设置
        newVal.forEach(id => {
          if (!this.selfBufferSettings[id]) {
            this.selfBufferSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
          if (!this.selfPatchSettings[id]) {
            this.selfPatchSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
        })

        // 清理未选中标签的设置
        if (oldVal) {
          Object.keys(this.selfBufferSettings).forEach(id => {
            if (!newVal.includes(id)) {
              delete this.selfBufferSettings[id]
            }
          })
          Object.keys(this.selfPatchSettings).forEach(id => {
            if (!newVal.includes(id)) {
              delete this.selfPatchSettings[id]
            }
          })
        }

        // 更新全选状态
        const total = this.getAllTags().length
        this.selfAllChecked = newVal.length === total && total > 0
        this.selfIndeterminate = newVal.length > 0 && newVal.length < total

        // 更新表单数据
        this.formData.selfVehicleFiltering = this.getSelfSelectedTags().map(tag => tag.id)
      },
      deep: true
    }
  },
  methods: {
    initFunctionList() {
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
    },
    initPermission() {
      // 初始化权限
      this.dataOperatePermission = {
        add: true,
        edit: true,
        remove: true,
        view: true
      }
    },
    executeButtonMethod(button, row) {
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageDataMiningDefine(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
      this.formReadonly = false
      this.formData = Object.assign({}, defaultFormData)
      this.loadWorkflowDefinitions()
      this.loadDataMiningRules()
    },
    edit(row) {
      this.dialogTitle = this.$t('编辑')
      this.dialogStatus = 'edit'
      this.dialogVisible = true
      this.formReadonly = false
      getDataMiningDefine(row.id).then(res => {
        this.formData = res.data
        // 确保defaultRuleIds是数字
        if (this.formData.defaultRuleIds === undefined || this.formData.defaultRuleIds === null) {
          this.formData.defaultRuleIds = 0
        }
        // 确保静态筛选和自车筛选是数组
        if (!this.formData.staticFiltering) {
          this.formData.staticFiltering = []
        }
        if (!this.formData.selfVehicleFiltering) {
          this.formData.selfVehicleFiltering = []
        }
        this.loadWorkflowDefinitions()
        this.loadDataMiningRules()
      })
    },
    view(row) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      this.dialogVisible = true
      this.formReadonly = true
      getDataMiningDefine(row.id).then(res => {
        this.formData = res.data
        // 确保defaultRuleIds是数字
        if (this.formData.defaultRuleIds === undefined || this.formData.defaultRuleIds === null) {
          this.formData.defaultRuleIds = 0
        }
        // 确保静态筛选和自车筛选是数组
        if (!this.formData.staticFiltering) {
          this.formData.staticFiltering = []
        }
        if (!this.formData.selfVehicleFiltering) {
          this.formData.selfVehicleFiltering = []
        }
        this.loadWorkflowDefinitions()
        this.loadDataMiningRules()
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    handleWorkflowChange(value) {
      // 更新工作流名称
      const selectedWorkflow = this.workflowDefinitionList.find(item => item.id === value)
      if (selectedWorkflow) {
        this.formData.workflowDefinitionName = selectedWorkflow.name
      }
    },
    loadWorkflowDefinitions() {
      //   listWorkflowDefinition().then(res => {
      //     this.workflowDefinitionList = res.data
      //   })
    },
    loadDataMiningRules() {
      listDataMiningRule().then(res => {
        this.dataMiningRuleList = res.data
      })
    },
    saveForm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return

        const postData = { ...this.formData }

        if (this.dialogStatus === 'add') {
          debugger
          saveDataMiningDefine(postData).then(() => {
            showToast(this.$t('保存成功'))
            this.dialogVisible = false
            this.query()
          })
        } else if (this.dialogStatus === 'edit') {
          updateDataMiningDefine(postData).then(() => {
            showToast(this.$t('更新成功'))
            this.dialogVisible = false
            this.query()
          })
        }
      })
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    remove(param) {
        debugger
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteDataMiningDefine(param).then(() => {
          showToast(this.$t('删除成功'))
          this.query()
        })
      })
    },
    dialogClosed() {
      this.formData = Object.assign({}, defaultFormData)
      this.$refs.formRef?.resetFields()
    },
    getTypeLabel(type) {
      const option = this.typeOptions.find(item => item.value === type)
      return option ? option.label : ''
    },

    // 静态筛选相关方法
    loadStaticTags() {
      // 模拟获取静态标签数据
      const mockData = [
        { id: 1, name: '文字标签', type: 'text' },
        { id: 2, name: '数字标签', type: 'number' },
        { id: 3, name: '符号标签', type: 'symbol' },
        { id: 4, name: '箭头标签', type: 'arrow' },
        { id: 5, name: '文字标签2', type: 'text' },
        { id: 6, name: '数字标签2', type: 'number' },
        { id: 7, name: '符号标签2', type: 'symbol' },
        { id: 8, name: '箭头标签2', type: 'arrow' }
      ]

      this.staticTagCategories.forEach(cat => {
        cat.tags = mockData.filter(tag => tag.type === cat.type)
        this.categoryCheckedMap[cat.type] = false
      })
    },

    // 初始化标签的缓冲区设置
    initBufferSetting(tagId) {
      if (!this.bufferSettings[tagId]) {
        this.bufferSettings[tagId] = {
          enabled: false,
          type: 'distance',
          value: 0
        }
      }
    },

    // 更新缓冲区值
    handleBufferValueChange(tagId, value) {
      if (this.bufferSettings[tagId]) {
        this.bufferSettings[tagId].value = value
      }
    },

    // 更新缓冲区类型
    handleBufferTypeChange(tagId, type) {
      if (this.bufferSettings[tagId]) {
        this.bufferSettings[tagId].type = type
        // 重置值为0，避免切换类型时值超出范围
        this.bufferSettings[tagId].value = 0
      }
    },

    // 检查标签是否被选中
    isTagChecked(tag) {
      return this.checkedTagIds.includes(String(tag.id))
    },

    // 处理类别复选框变化
    handleCategoryChange(cat, checked) {
      const ids = cat.tags.map(tag => String(tag.id))
      if (checked) {
        // 选中分类下所有标签
        const newIds = ids.filter(id => !this.checkedTagIds.includes(id))
        this.checkedTagIds = [...this.checkedTagIds, ...newIds]
        // 初始化新选中标签的缓冲区设置和添加到已选标签列表
        newIds.forEach(id => {
          // 初始化缓冲区设置
          if (!this.bufferSettings[id]) {
            this.bufferSettings[id] = {
              enabled: false,
              type: 'distance',
              value: 0
            }
          }
          // 添加到已选标签列表
          const tag = this.findTagById(id)
          if (tag && !this.selectedStaticTags.some(t => t.id === tag.id)) {
            this.selectedStaticTags.push(tag)
          }
        })
      } else {
        // 取消选中分类下所有标签
        this.checkedTagIds = this.checkedTagIds.filter(id => !ids.includes(id))
        // 清除取消选中标签的缓冲区设置和已选标签
        ids.forEach(id => {
          delete this.bufferSettings[id]
          this.selectedStaticTags = this.selectedStaticTags.filter(tag => String(tag.id) !== id)
        })
      }
    },

    // 移除静态标签
    removeStaticTag(tag) {
      const tagId = String(tag.id)
      this.checkedTagIds = this.checkedTagIds.filter(id => id !== tagId)
      delete this.bufferSettings[tagId]
      this.selectedStaticTags = this.selectedStaticTags.filter(t => String(t.id) !== tagId)
    },

    // 重置静态标签
    resetStaticTags() {
      this.selectedStaticTags = []
      this.checkedTagIds = []
      this.bufferSettings = {}
    },

    // 查找标签
    findTagById(id) {
      let foundTag = null
      this.staticTagCategories.forEach(cat => {
        const tag = cat.tags.find(t => String(t.id) === id)
        if (tag) foundTag = tag
      })
      return foundTag
    },

    // 生成标签显示文本
    tagLabelWithBuffer(tag) {
      const tagId = String(tag.id)
      const parts = [tag.name]

      if (this.bufferSettings[tagId]?.enabled) {
        const buffer = this.bufferSettings[tagId]
        const unit = buffer.type === 'distance' ? 'm' : '个'
        parts.push(`匹配缓冲区(${buffer.type === 'distance' ? '距离' : '车道'}/${buffer.value}${unit})`)
      }

      return parts.join(' ')
    },

    // 自车筛选相关方法
    // 获取所有可选标签
    getAllTags() {
      const tags = []
      this.staticTagCategories.forEach(cat => {
        tags.push(...cat.tags)
      })
      return tags
    },

    // 获取已选自车标签
    getSelfSelectedTags() {
      return this.getAllTags().filter(tag => this.selfCheckedTagIds.includes(String(tag.id)))
    },

    // 生成自车标签显示文本
    selfTagLabelWithSettings(tag) {
      const tagId = String(tag.id)
      const parts = [tag.name]

      if (this.selfBufferSettings[tagId]?.enabled) {
        const buffer = this.selfBufferSettings[tagId]
        const unit = buffer.type === 'distance' ? 'm' : '个'
        parts.push(`匹配缓冲区(${buffer.type === 'distance' ? '距离' : '车道'}/${buffer.value}${unit})`)
      }

      if (this.selfPatchSettings[tagId]?.enabled) {
        const patch = this.selfPatchSettings[tagId]
        const unit = patch.type === 'distance' ? 'm' : 's'
        parts.push(`数据补全(${patch.type === 'distance' ? '距离' : '时间'}/${patch.value}${unit})`)
      }

      return parts.join(' ')
    },

    // 移除自车标签
    removeSelfTag(tag) {
      const tagId = String(tag.id)
      this.selfCheckedTagIds = this.selfCheckedTagIds.filter(id => id !== tagId)
    },

    // 重置自车标签
    resetSelfTags() {
      this.selfCheckedTagIds = []
      this.selfBufferSettings = {}
      this.selfPatchSettings = {}
      this.selfAllChecked = false
      this.selfIndeterminate = false
    },

    // 检查自车标签是否被选中
    isSelfTagChecked(tag) {
      return this.selfCheckedTagIds.includes(String(tag.id))
    },

    // 处理自车全选变化
    handleSelfCheckAllChange(val) {
      this.selfCheckedTagIds = val ? this.getAllTags().map(tag => String(tag.id)) : []
      this.selfIndeterminate = false
    }
  }
}
</script>

<style lang="scss" scoped>
.ltw-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
}

.ltw-search-container {
  margin-right: 16px;
  min-width: 200px;
  display: flex;
  align-items: center;
}

.button-group {
  display: flex;
}

.item-label {
  width: 60px;
}

/* 静态筛选和自车筛选样式 */
.filtering-section {
  margin-top: 20px;
  margin-bottom: 20px;

  h3 {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
  }
}

.info-box {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
  min-height: 80px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  background: #fafbfc;
}
.static-tag-cards{
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.tag-card {
  margin-bottom: 16px;
 min-width:180px;
  .card-header {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.self-tag-list-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px; /* 负边距，使子元素能够超出容器边界 */
}

.self-tag-item {
  flex: 0 0 33.3333%;
  box-sizing: border-box;
  padding: 12px 16px 12px 0;
  border-bottom: 1px solid #ebeef5;
  min-width: 0;
}

.tag-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;

  .tag-checkbox {
    display: flex;
    align-items: center;
  }
}

.tag-buffer-settings {
  margin-left: 24px;
  margin-top: 8px;

  .tag-box {
    display: flex;
    align-items: center;
    margin-top: 8px;
    flex-wrap: wrap;
  }

  .el-radio-group {
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .number-input-wrapper {
    display: inline-block;

    :deep(.el-input-number) {
      width: 100px;
    }
  }

  .unit {
    color: #606266;
    font-size: 14px;
    margin-left: 4px;
  }
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
}

:deep(.el-input-number.el-input-number--small) {
  width: 100px !important;
}

/* 对话框样式调整 */
:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
