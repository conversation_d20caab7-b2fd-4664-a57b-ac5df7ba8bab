<template>
  <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" class="custom-dialog">
    <div style="display: flex" class="dialog-body">
      <!-- 左边表单 -->
      <div style="flex: 1; padding-right: 20px">
        <div class="title">基础信息</div>
        <el-form
          :model="formData"
          :rules="leftRules"
          label-position="top"
          require-asterisk-position="right"
          ref="leftFormRef"
          label-width="100px"
        >
          <el-form-item :label="$t('标签')" prop="tagCode">
            <el-button
              style="margin-right: 5px"
              :disabled="!isFieldEditable('tagCode') || tagDistributeDrawerVisible"
              @click="tagDistribute"
            >
              <ltw-icon :icon-code="tagDistributeDrawerVisible ? 'el-icon-loading' : 'el-icon-plus'"></ltw-icon>
              {{ $t('选择') }}
            </el-button>
            <el-tag
              v-if="tagList?.length > 0"
              :type="checkTagType(tagList[0])"
              :closable="isFieldEditable('tagCode')"
              @close="handleRemoveTag"
            >
              <span v-if="mode === 'edit' || mode === 'addVersion' || mode === 'view'">
                {{ tagList[0].name }}
              </span>
              <span v-else> {{ tagList[0].groupName }}:{{ tagList[0].name }} </span>
            </el-tag>
            <radio-bs-tag-group-drawer
              :drawer-visible="tagDistributeDrawerVisible"
              :row-tag-list="rowTagList"
              :tag-id="tagId"
              @drawer-click="confirmDistributeTags"
            >
            </radio-bs-tag-group-drawer>
          </el-form-item>

          <el-form-item label="类型" prop="types">
            <el-cascader
              v-model="formData.types"
              :options="typeOptions"
              clearable
              style="width: 100%"
              :disabled="!isFieldEditable('types')"
            />
          </el-form-item>
          <div class="title">其他</div>
          <el-form-item :label="$t('描述')" prop="description">
            <ltw-input v-model="formData.description" :placeholder="$t('请输入')" :disabled="readonly"></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('维护人')" prop="maintainer">
            <el-select
              v-model="formData.maintainer"
              clearable
              filterable
              placeholder="请选择维护人"
              style="width: 100%"
              :disabled="readonly"
            >
              <el-option v-for="item in maintainerOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 分隔线 -->
      <el-divider direction="vertical" style="height: auto; margin: 0 10px" />

      <!-- 右边表单 -->
      <div style="flex: 1; padding-left: 20px">
        <div class="title">选择器配置</div>
        <div v-if="selectorFormConfig.length > 0">
          <el-form :model="formData" ref="rightFormRef" label-position="top" require-asterisk-position="right">
            <el-row :gutter="20">
              <template v-for="(group, groupIndex) in selectorFormConfig" :key="groupIndex">
                <template v-for="(item, itemIndex) in group.components" :key="itemIndex">
                  <el-col :span="12">
                    <el-form-item :label="item.label" :prop="item.label">
                      <!-- 输入框 -->
                      <template v-if="item.componentType === 'input'">
                        <!-- 字符串 -->
                        <el-input
                          :disabled="!isFieldEditable(item.property)"
                          v-if="item.propertyValueType === 'string'"
                          v-model="formData[item.property]"
                          :placeholder="`请输入${item.label}`"
                        />
                        <!-- 整数 -->
                        <el-input-number
                          :disabled="!isFieldEditable(item.property)"
                          v-else-if="item.propertyValueType === 'int'"
                          v-model="formData[item.property]"
                          :placeholder="`请输入${item.label}`"
                          style="width: 100%"
                        />
                        <!-- 浮点数 -->
                        <el-input-number
                          :disabled="!isFieldEditable(item.property)"
                          v-else-if="item.propertyValueType === 'float'"
                          v-model="formData[item.property]"
                          :precision="2"
                          :step="0.1"
                          :placeholder="`请输入${item.label}`"
                          style="width: 100%"
                        />
                      </template>

                      <!-- 下拉选择 -->
                      <el-select
                        filterable
                        clearable
                        collapse-tags
                        collapse-tags-tooltip
                        style="width: 100%"
                        v-else-if="item.componentType === 'select'"
                        v-model="formData[item.property]"
                        :placeholder="`请选择${item.label}`"
                        :multiple="item.multiple === 1"
                        :disabled="!isFieldEditable(item.property)"
                      >
                        <el-option
                          v-for="option in item.options || []"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        />
                      </el-select>

                      <!-- 级联选择 -->
                      <div v-else-if="item.componentType === 'cascader'">
                        <el-cascader
                          v-model="formData[item.property]"
                          :disabled="!isFieldEditable(item.property)"
                          size="small"
                          :placeholder="$t('请选择')"
                          :options="item.options"
                          clearable
                          filterable
                        />
                      </div>

                      <!-- tag多选 -->
                      <div v-else-if="item.componentType === 'tag'">
                        <el-button
                          style="margin-right: 5px"
                          @click="tagCodesDistribute"
                          :disabled="!isFieldEditable(item.property)"
                        >
                          <ltw-icon :icon-code="'el-icon-plus'"></ltw-icon>
                          {{ $t('选择') }}
                        </el-button>
                        <div style="max-height: 100px; overflow-y: auto">
                          <el-tag
                            v-for="(tag, index) in tagCodeList"
                            :key="index"
                            :type="checkTagType(tag)"
                            :closable="isFieldEditable(item.property)"
                            @close="handleCodeClose(tag)"
                            style="margin: 3px 5px"
                          >
                            {{ tag.name }}
                          </el-tag>
                        </div>
                        <bs-tag-group-drawer
                          :drawerVisible="tagsDistributeDrawerVisible"
                          :rowTagList="rowTagsList"
                          :matchFlag="true"
                          @drawerClick="drawerClick"
                        ></bs-tag-group-drawer>
                      </div>
                      <!-- 复选框组 -->
                      <el-checkbox-group
                        :disabled="!isFieldEditable(item.property)"
                        v-else-if="item.componentType === 'checkbox'"
                        v-model="formData[item.property]"
                      >
                        <el-checkbox v-for="option in item.options || []" :key="option.value" :label="option.value">
                          {{ option.label }}
                        </el-checkbox>
                      </el-checkbox-group>

                      <!-- 单选框组 -->
                      <el-radio-group
                        :disabled="!isFieldEditable(item.property)"
                        v-else-if="item.componentType === 'radio'"
                        v-model="formData[item.property]"
                      >
                        <el-radio v-for="option in item.options || []" :key="option.value" :label="option.value">
                          {{ option.label }}
                        </el-radio>
                      </el-radio-group>

                      <!-- 是否组件（1 是 / 0 否） -->
                      <el-radio-group
                        :disabled="!isFieldEditable(item.property)"
                        v-else-if="item.componentType === 'yesOrNo'"
                        v-model="formData[item.property]"
                      >
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                      </el-radio-group>

                      <!-- 区划级联 -->
                      <div v-else-if="item.componentType === 'canton'">
                        <el-cascader
                          size="small"
                          v-model="formData[item.property]"
                          :placeholder="$t('请选择行政区')"
                          :options="cantonTreeList"
                          :props="getCantonProps(item)"
                          clearable
                          filterable
                          :disabled="!isFieldEditable(item.property)"
                        />
                      </div>
                      <!-- 日期选择器 -->
                      <div v-else-if="item.componentType === 'date'">
                        <el-date-picker
                          v-model="formData[item.property]"
                          type="datetime"
                          :placeholder="`请选择${item.label}`"
                          style="width: 100%"
                          clearable
                          :disabled="!isFieldEditable(item.property)"
                        />
                      </div>
                    </el-form-item>
                  </el-col>
                </template>
              </template>
            </el-row>
          </el-form>
        </div>
        <div v-else>
          <el-empty :image-size="100" description="暂无配置项"></el-empty>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <span v-if="mode === 'view'">
        <el-button @click="dialogClosed">
          {{ $t('关闭') }}
        </el-button>
      </span>
      <span v-else>
        <el-button @click="dialogClosed">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ $t('确定') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  updateDmiRuleDefinitionVersion,
  updateDmiRuleDefinition,
  saveDmiRuleDefinition,
  saveDmiRuleDefinitionVersion,
  getDmiRuleSelectorConfig,
  getDmiRuleDefinition,
  tagUniqueCheckDmiRuleDefinition
} from '@/apis/dmi/dmi-rule-definition'
import RadioBsTagGroupDrawer from '@/components/basic/RadioBsTagGroupDrawer.vue'
import { checkTagType } from '@/plugins/util'
import { getSysCantonTree } from '@/apis/system/sys-canton'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { httpGet } from '@/plugins/http'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
export default {
  name: 'DmiRuleConfig',
  emits: ['confirm'],
  components: { RadioBsTagGroupDrawer, BsTagGroupDrawer },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      props: { multiple: true },
      mode: '',
      readonly: '',
      formData: {},
      leftRules: {
        tagCode: [{ required: true, message: '请选择标签名称', trigger: 'blur' }],
        types: [{ required: true, message: '请选择类型', trigger: 'change' }]
      },
      addInitVersionData: '',
      maintainerOptions: [],
      typeOptions: [],
      selectorFormConfig: [],
      tagDistributeDrawerVisible: false,
      tagsDistributeDrawerVisible: false,
      rowTagList: [],
      tagId: '',
      checkTagType,
      tagList: [],
      cantonTreeList: [],
      isTagCodes: false,
      // tag多选
      rowTagsList: [],
      tagCodeList: [],
      tagsList: [],
      versionEdit: false
    }
  },
  created() {
    this.listCantonTree()
  },
  watch: {
    'formData.types'(val) {
      if (val) {
        const selectedSubType = Array.isArray(val) ? val[val.length - 1] : val
        this.DmiRuleSelectorConfigList(selectedSubType)
      }
    }
  },
  methods: {
    show({ mode = 'create', row = null, maintainerOption = [], typeOption = [], versionEdit = false }) {
      this.tagCodeList = []
      this.mode = mode
      this.maintainerOptions = maintainerOption
      this.typeOptions = typeOption
      this.dialogVisible = true
      this.readonly = mode === 'view'
      this.versionEdit = versionEdit
      if (mode === 'create') {
        this.formData = {}
        this.dialogTitle = this.$t('新增')
        return
      }
      if (mode === 'addVersion') {
        this.dialogTitle = this.$t('新增')
        this.addInitVersionData = row
        this.formData = this.transformFormData(row || {})
        return
      }
      if (mode === 'edit' || mode === 'view') {
        this.dialogTitle = this.$t(mode === 'edit' ? '编辑' : '详情')
        this.getformData(row)
      }
    },
    getformData(row) {
      getDmiRuleDefinition(row.id).then(res => {
        this.$nextTick(() => {
          const data = res.data || {}
          this.formData = this.transformFormData(data)
        })
      })
    },
    transformFormData(data) {
      const clonedRow = {
        ...data,
        types: [data.type, data.subType, data.childType].filter(Boolean),
        ...data.selector
      }
      // 标签信息
      if (data.tagCode && data.tagName) {
        this.tagList = [{ code: data.tagCode, name: data.tagName }]
        this.tagId = data.tagId || ''
      }
      // 选择器配置
      this.selectorFormConfig = this.flattenSelectorFormConfig(data.selectorConfig)
      // 维护人映射
      if (data.maintainer) {
        const maintainerMap = this.maintainerOptions.find(opt => opt.label === data.maintainer)
        clonedRow.maintainer = maintainerMap?.value || ''
      }
      // 多选 tagCodes 处理
      if (data.selector?.tagCodes) {
        const codes = data.selector.tagCodes.split(',')
        if (Array.isArray(data.selector.tagOriginalData)) {
          this.tagCodeList = data.selector.tagOriginalData
        }
      } else {
        this.tagCodeList = []
      }
      return clonedRow
    },
    isFieldEditable(field) {
      if (this.mode === 'view') return false
      if ((this.mode === 'edit' || this.mode === 'addVersion') && ['types', 'tagCode'].includes(field)) {
        return false
      }
      return true
    },
    dialogClosed() {
      this.$refs.leftFormRef.resetFields()
      this.dialogVisible = false
      this.initForm()
    },
    initForm() {
      this.formData = {
        tagName: '',
        types: '',
        confirmEnable: false,
        description: '',
        maintainer: '',
        selectorType: '',
        params: ''
      }
      ;(this.tagList = []), (this.selectorFormConfig = [])
    },
    handleSubmit() {
      this.$refs.leftFormRef.validate(async leftValid => {
        if (!leftValid) return
        this.$refs.rightFormRef.validate(async rightValid => {
          if (!rightValid) return
          const tagCode = this.formData.tagCode
          if (this.mode === 'create') {
            const isUnique = await tagUniqueCheckDmiRuleDefinition(tagCode)
            if (!isUnique.data) {
              this.formData.confirmEnable = 0
              this.submitRequest()
              return
            }
          }
          this.$confirm('是否立即启用该规则？', '提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning'
          })
            .then(() => {
              this.formData.confirmEnable = 1
              this.submitRequest()
            })
            .catch(() => {
              this.formData.confirmEnable = 0
              this.submitRequest()
            })
        })
      })
    },
    submitRequest() {
      let request
      const [type, subType, childType] = this.formData.types || []
      const selectorObj = {}
      this.selectorFormConfig.forEach(group => {
        group.components.forEach(item => {
          const key = item.property
          const value = this.formData[key]
          if (value !== undefined) {
            selectorObj[key] = value
          }
          if (key === 'tagCodes') {
            selectorObj['tagOriginalData'] = this.tagCodeList
          }
        })
      })
      const queryParams = {
        id: this.formData.id,
        tagCode: this.formData.tagCode || '',
        type: type || '',
        subType: subType || '',
        childType: childType || '',
        description: this.formData.description || '',
        enable: this.formData.confirmEnable ? 1 : 0,
        maintainer: this.maintainerOptions.find(item => item.value === this.formData.maintainer)?.label || '',
        maintainerEmpId: Array.isArray(this.formData.maintainer)
          ? this.formData.maintainer.join(',')
          : this.formData.maintainer || '',
        selector: selectorObj
      }

      if (this.mode === 'create') {
        request = saveDmiRuleDefinition(queryParams)
      } else if (this.mode === 'addVersion') {
        const data = {
          tagCode: this.addInitVersionData.tagCode
        }
        request = saveDmiRuleDefinitionVersion(queryParams, data)
      } else if (this.mode === 'view') {
        this.handleCancel()
      } else if (this.mode === 'edit' && this.versionEdit) {
        request = updateDmiRuleDefinitionVersion(queryParams)
      } else {
        request = updateDmiRuleDefinition(queryParams)
      }
      request.then(() => {
        this.handleCancel()
        this.$emit('confirm')
      })
    },
    handleCancel() {
      this.$refs.leftFormRef.resetFields()
      this.$refs.rightFormRef.resetFields()
      this.dialogVisible = false
    },
    tagDistribute() {
      this.tagDistributeDrawerVisible = true
      this.rowTagList = this.tagList || []
    },
    confirmDistributeTags(data) {
      if (JSON.stringify(data?.tagItem) !== '{}') {
        this.tagId = data?.tagItem?.id
        this.tagList = [data?.tagItem]
        this.formData.tagCode = data?.tagItem.code
      } else {
        this.tagId = ''
        this.tagList = []
        this.formData.tagCode = ''
      }
      this.$nextTick(() => {
        this.$refs.leftFormRef.validateField('tagCode')
      })
      this.tagDistributeDrawerVisible = false
    },
    handleRemoveTag() {
      this.tagList = []
      this.tagId = ''
      this.formData.tagCode = ''
      this.rowTagList = []
      this.$nextTick(() => {
        this.$refs.leftFormRef.validateField('tagCode')
      })
    },
    getCantonProps(item) {
      return {
        value: 'locationArr',
        label: 'name',
        children: 'children',
        multiple: item.multiple === 1,
        checkStrictly: item.multiple !== 1,
        emitPath: false
      }
    },
    DmiRuleSelectorConfigList(subType) {
      getDmiRuleSelectorConfig(subType)
        .then(async res => {
          if (!res?.data) {
            this.selectorFormConfig = []
            return
          }
          const config = this.flattenSelectorFormConfig(res.data)
          await Promise.all(
            config.flatMap(group =>
              group.components.map(async item => {
                if (item.componentType === 'select') {
                  if (item.dataSource === 'dict' && item.dictTypeCode) {
                    return listSysDictionary({ typeCode: item.dictTypeCode })
                      .then(dictRes => {
                        item.options = (dictRes?.data || []).map(dict => ({
                          label: dict.name,
                          value: dict.code
                        }))
                      })
                      .catch(() => {
                        item.options = []
                      })
                  } else if (item.dataSource === 'api' && item.apiUrl) {
                    return httpGet({ url: item.apiUrl })
                      .then(apiRes => {
                        item.options = (apiRes?.data || []).map(apiItem => ({
                          label: apiItem.name || apiItem.label || apiItem.title,
                          value: apiItem.id || apiItem.value
                        }))
                      })
                      .catch(() => {
                        item.options = []
                      })
                  }
                } else if (item.componentType === 'cascader') {
                  if (item.dataSource === 'api' && item.apiUrl) {
                    return httpGet({ url: item.apiUrl })
                      .then(cascaderRes => {
                        item.options = this.formatCascaderOptions(cascaderRes?.data || [])
                      })
                      .catch(() => {
                        item.options = []
                      })
                  }
                }
              })
            )
          )

          this.selectorFormConfig = config
        })
        .catch(() => {
          this.selectorFormConfig = []
        })
    },
    formatCascaderOptions(data) {
      return data.map(item => ({
        label: item.name || item.label || item.title,
        value: item.code || item.id || item.value,
        children: item.children?.length ? this.formatCascaderOptions(item.children) : undefined
      }))
    },
    flattenSelectorFormConfig(rawData) {
      return rawData.map(group => {
        const newComponents = []

        group.components.forEach(item => {
          newComponents.push(item)
          if (Array.isArray(item.relatedComponents) && item.relatedComponents.length > 0) {
            newComponents.push(...item.relatedComponents)
          }
        })
        return {
          ...group,
          components: newComponents
        }
      })
    },

    listWfProcessType() {
      if (this.wfProcessTypeList?.length) return false
      listSysDictionary({ typeCode: 'wf_process_type' }).then(res => {
        this.wfProcessTypeList = res.data
      })
    },

    listCantonTree() {
      if (this.cantonTreeList?.length) return
      getSysCantonTree().then(res => {
        this.cantonTreeList = this.addLocationField(res.data)
      })
    },
    addLocationField(data) {
      return data.map(item => {
        item.locationArr = this.extractLocationArr(item)
        if (item.children?.length) {
          item.children = this.addLocationField(item.children)
        }
        return item
      })
    },
    extractLocationArr(item) {
      if (item.latitude !== undefined && item.longitude !== undefined) {
        return [item.code]
      }
      return []
    },
    tagCodesDistribute() {
      this.isTagCodes = true
      this.tagsDistributeDrawerVisible = true
      this.rowTagsList = this.tagCodeList || []
    },
    drawerClick(data) {
      if (this.isTagCodes) {
        this.confirmDistributeTagMultiCodes(data)
        return
      }
      this.confirmDistributeMultiTags(data)
    },
    confirmDistributeMultiTags(data) {
      this.formData.tagSingleMatch = data?.isOr?.value
      if (data && data.tagList && data.tagList.length) {
        this.tagsList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagList(tagGroup)
        })
      }
      if (this.tagsList?.length > 30) {
        showToast('只支持选择30个以内的标签，请去除非必要标签', 'warning')
        return
      }
      if (this.tagsList?.length > 0) {
        this.formData.tagIds = []
        this.tagsList.forEach(item => {
          this.formData.tagIds.push(item.id)
        })
        this.formData.tagIds = this.formData.tagIds.join(',')
      } else {
        this.formData.tagIds = undefined
        this.formData.tagSingleMatch = undefined
      }
      this.tagsDistributeDrawerVisible = false
    },
    confirmDistributeTagMultiCodes(data) {
      this.formData.tagSingleMatch = data?.isOr?.value
      if (data && data.tagList && data.tagList.length) {
        this.tagCodeList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagCodeList(tagGroup)
        })
      }
      if (this.tagCodeList?.length > 30) {
        showToast('只支持选择30个以内的标签，请去除非必要标签', 'warning')
        return
      }
      if (this.tagCodeList?.length > 0) {
        this.formData.tagCodes = []
        this.tagCodeList.forEach(item => {
          if (item.code) {
            this.formData.tagCodes.push(item.code)
          }
        })
        this.formData.tagOriginalData = this.tagCodeList
        this.formData.tagCodes = this.formData.tagCodes.join(',')
      } else {
        this.formData.tagCodes = undefined
        this.formData.tagSingleMatch = undefined
      }
      this.tagsDistributeDrawerVisible = false
    },
    saveCheckedTagList(group) {
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.tagsList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      }
    },
    saveCheckedTagCodeList(group) {
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.tagCodeList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagCodeList(subGroup)
        })
      }
    },
    handleClose(tag) {
      this.tagsList = this.tagsList.filter(item => {
        return item !== tag
      })
      this.rowTagList = this.tagsList
    },
    handleCodeClose(tag) {
      this.tagCodeList = this.tagCodeList.filter(item => {
        return item !== tag
      })
      this.rowTagList = this.tagCodeList
    }
  }
}
</script>
<style scoped lang="scss">
.title {
  font-size: 12px;
  font-weight: bold;
  margin: 10px 0;
}

.custom-dialog .dialog-body {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
  box-sizing: border-box;
}

.el-form-item {
  margin-bottom: 20px;
}

.tag-container {
  margin-top: 10px;

  .el-tag {
    margin: 0 3px;
  }
}
</style>
