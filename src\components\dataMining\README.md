# 挖掘结果可视化组件

## 概述

`MiningResultVisualization.vue` 是一个用于展示数据挖掘结果的可视化组件，支持图像数据和轨迹数据的同步播放。

## 功能特性

- 📷 **图像展示**: 支持显示每个时间点的图像数据
- 🗺️ **轨迹展示**: 支持显示车辆轨迹数据（预留地图组件接口）
- ⏯️ **播放控制**: 支持播放、暂停、上一帧、下一帧
- 🎚️ **进度控制**: 支持进度条拖动，实现图像和轨迹同步
- ⚡ **倍速播放**: 支持1x、2x倍速播放
- 🔍 **条件筛选**: 支持按摄像头、车辆、时间段筛选数据

## 使用方法

### 1. 在父组件中引入

```vue
<template>
  <div>
    <!-- 其他内容 -->
    
    <!-- 挖掘结果可视化组件 -->
    <mining-result-visualization
      v-if="visualizationVisible"
      :record-data="currentRecord"
      @back="closeVisualization"
    />
  </div>
</template>

<script>
import MiningResultVisualization from '@/components/dataMining/MiningResultVisualization.vue'

export default {
  components: {
    MiningResultVisualization
  },
  data() {
    return {
      visualizationVisible: false,
      currentRecord: null
    }
  },
  methods: {
    openVisualization(record) {
      this.currentRecord = record
      this.visualizationVisible = true
    },
    
    closeVisualization() {
      this.visualizationVisible = false
      this.currentRecord = null
    }
  }
}
</script>
```

### 2. Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| recordData | Object | {} | 挖掘定义记录数据，包含id、name等字段 |

### 3. Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| back | 返回事件 | - |

## API 接口

组件依赖以下API接口（位于 `src/apis/dataMining/mining_result_visualization.js`）：

### 获取摄像头列表
```javascript
getCameraList(params)
```

### 获取车辆列表
```javascript
getVehicleList(params)
```

### 获取时间轴数据
```javascript
getTimelineData({
  miningDefineId: string,
  cameraId: string,
  vehicleId: string,
  startTime: string,
  endTime: string
})
```

### 获取图像数据
```javascript
getImageData({
  miningDefineId: string,
  cameraId: string,
  startTime: string,
  endTime: string
})
```

### 获取轨迹数据
```javascript
getTrajectoryData({
  miningDefineId: string,
  vehicleId: string,
  startTime: string,
  endTime: string
})
```

## 数据格式

### 时间轴数据格式
```javascript
[
  {
    timestamp: "2024-01-01T10:00:00.000Z",
    imageUrl: "base64_string_or_url",
    geoData: {
      longitude: 116.4074,
      latitude: 39.9042
    }
  }
]
```

### 图像数据格式
```javascript
[
  {
    timestamp: "2024-01-01T10:00:00.000Z",
    imageUrl: "base64_string_or_url"
  }
]
```

### 轨迹数据格式
```javascript
[
  {
    timestamp: "2024-01-01T10:00:00.000Z",
    longitude: 116.4074,
    latitude: 39.9042
  }
]
```

## 键盘快捷键

- `空格键`: 播放/暂停
- `左箭头`: 上一帧
- `右箭头`: 下一帧

## 样式定制

组件使用了scoped样式，主要的CSS类名包括：

- `.mining-result-container`: 主容器
- `.header`: 头部区域
- `.select-box`: 选择器区域
- `.content-box`: 内容展示区域
- `.control-box`: 控制区域

## 注意事项

1. 组件会自动处理API调用失败的情况，使用模拟数据作为降级方案
2. 图像显示会自动适应画布大小
3. 轨迹展示功能预留了地图组件接口，需要根据实际地图组件进行集成
4. 组件支持响应式布局，适配不同屏幕尺寸

## 扩展开发

### 集成地图组件

在 `updateTrajectory` 方法中集成实际的地图组件：

```javascript
updateTrajectory(geoData) {
  // 调用地图组件更新轨迹显示
  if (this.$refs.mapComponent) {
    this.$refs.mapComponent.updateTrajectory(geoData)
  }
}
```

### 添加更多控制功能

可以在控制区域添加更多功能，如：
- 全屏播放
- 截图功能
- 数据导出
- 标注功能
