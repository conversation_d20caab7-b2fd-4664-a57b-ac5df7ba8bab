<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-search-toolbar">
        <!-- 筛选项区域（左侧） -->
        <div class="filter-area">
          <div class="filter-item">
            <span class="label-txt">{{ $t('标签名称') }}</span>
            <div class="tag-select-wrapper">
              <div class="tag-select-content" @click="tagCodesDistribute">
                <span v-if="!tagCodeList || tagCodeList.length === 0" class="tag-placeholder"> 请点击选择标签 </span>
                <el-tag
                  v-for="(tag, index) in tagCodeList?.slice(0, 1)"
                  :key="index"
                  :type="checkTagType(tag)"
                  closable
                  @close.stop="handleCodeClose(tag)"
                  style="margin-right: 3px"
                >
                  {{ tag.groupName }}:{{ tag.name }}
                </el-tag>
                <el-popover
                  placement="top"
                  width="300"
                  trigger="hover"
                  v-if="tagCodeList?.length > 1"
                  :key="tagCodeList.length"
                >
                  <template #reference>
                    <el-tag :key="'extra-tag-' + tagCodeList.length" type="info" style="margin-right: 3px">
                      +{{ tagCodeList.length - 1 }}
                    </el-tag>
                  </template>

                  <div style="display: flex; flex-wrap: wrap; gap: 4px">
                    <el-tag
                      closable
                      v-for="(tag, index) in tagCodeList.slice(1)"
                      :key="'tag-' + index"
                      :type="checkTagType(tag)"
                      style="margin: 2px"
                      @close.stop="handleCodeClose(tag)"
                    >
                      {{ tag.groupName }}:{{ tag.name }}
                    </el-tag>
                  </div>
                </el-popover>
                <bs-tag-group-drawer
                  :drawerVisible="tagDistributeDrawerVisible"
                  :rowTagList="rowTagList"
                  :matchFlag="true"
                  @drawerClick="drawerClick"
                />
              </div>
              <el-icon v-if="tagCodeList?.length" @click.stop="clearAllTags" class="clear-icon">
                <CircleClose />
              </el-icon>
            </div>
          </div>
          <div class="filter-item">
            <span class="label-txt">{{ $t('类型') }}</span>
            <el-cascader
              style="width: 100%"
              v-model="queryParam.subTypes"
              :options="typeOptions"
              :props="props"
              collapse-tags
              collapse-tags-tooltip
              clearable
              @change="
                val => {
                  if (!val || val.length === 0) handleClear('subTypes')
                }
              "
            />
          </div>
          <div class="filter-item">
            <span class="label-txt">{{ $t('启用状态') }}</span>
            <el-select
              v-model="queryParam.enable"
              clearable
              @clear="handleClear('enable')"
              placeholder="请选择启用状态"
              style="width: 300px"
            >
              <el-option v-for="item in enableOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="filter-item">
            <span class="label-txt">{{ $t('维护人') }}</span>
            <el-select
              v-model="queryParam.maintainerEmpId"
              clearable
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              placeholder="请选择维护人"
              style="width: 300px"
              @clear="handleClear('maintainerEmpId')"
              placement="top"
            >
              <el-option v-for="item in maintainerOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>
        <div class="button-area">
          <el-button type="primary" size="small" @click="refresh">
            <ltw-icon icon-code="el-icon-search" style="margin-right: 5px"></ltw-icon>
            {{ $t('筛选') }}
          </el-button>
          <el-button type="danger" size="small" @click="reset" style="margin-left: 8px">
            {{ $t('重置') }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary" style="margin-left: 8px">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" style="margin-left: 5px"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :key="item.id" v-for="item in batchingFunctionList" :command="item.buttonCode">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="primary" @click="openDmiRuleConfigDialog" style="margin-left: 8px">
            <ltw-icon icon-code="el-icon-plus" style="margin-right: 5px"></ltw-icon>
            {{ $t('新增') }}
          </el-button>
        </div>
      </div>
      <el-table
        :data="pageData.records"
        stripe
        @selection-change="handleSelectionChange"
        :row-key="getRowKeys"
        ref="tableRef"
        highlight-current-row
      >
        <el-table-column
          header-align="left"
          align="left"
          type="selection"
          width="55"
          :reserve-selection="true"
        ></el-table-column>
        <el-table-column header-align="left" align="left" prop="tagName" :label="$t('标签名称')"></el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('类型')">
          <template #default="scope">
            {{ [scope.row.type, scope.row.subType, scope.row.childTypeName].filter(Boolean).join('/') }}
          </template>
        </el-table-column>

        <el-table-column header-align="left" align="left" prop="version" :label="$t('当前版本')">
          <template #default="scope">
            <span>v{{ scope.row.version }}</span>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" prop="maintainer" :label="$t('维护人')"></el-table-column>
        <el-table-column header-align="left" align="left" prop="enable" :label="$t('是否启用')">
          <template #default="scope">
            <el-switch
              :value="scope.row.enable"
              :active-value="1"
              :inactive-value="0"
              inline-prompt
              @click.native="onEnableClick(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('操作')" min-width="180">
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button
                  v-if="!(scope.row.enable === 1 && (item.name === '编辑' || item.name === '删除'))"
                  :type="item.buttonStyleType"
                  size="mini"
                  @click="executeButtonMethod(item, scope.row)"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip effect="dark" content="版本管理" placement="top" :enterable="false">
                <el-button type="success" size="mini" @click="openHistoryVersion(scope.row)">
                  <ltw-icon icon-code="el-icon-wallet"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>
    <DmiRuleHistoryVersion ref="dmiRuleHistoryVersionRef" @confirm="query" />
    <DmiRuleConfig ref="dmiRuleConfigRef" @confirm="query" />
  </div>
</template>

<script>
import {
  saveDmiRuleDefinition,
  updateDmiRuleDefinition,
  updateDmiRuleDefinitionStatus,
  deleteDmiRuleDefinition,
  pageDmiRuleDefinition,
  getDmiRuleDefinition,
  getDmiRuleEmployees,
  getDmiRuleTreeTypeLevel
} from '@/apis/dmi/dmi-rule-definition'
import { checkTagType } from '@/plugins/util'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util'
import DmiRuleConfig from './dialog/DmiRuleConfig.vue'
import DmiRuleHistoryVersion from './dialog/DmiRuleHistoryVersion.vue'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'

const defaultFormData = {}
export default {
  name: 'DmiRuleDefinition',
  components: {
    BsTagGroupDrawer,
    DmiRuleHistoryVersion,
    DmiRuleConfig
  },
  data() {
    return {
      checkTagType: checkTagType,
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      tagDistributeDrawerVisible: false,
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10,
        enable: '',
        maintainerEmpId: '',
        subTypes: ''
      },
      tagOptions: [],
      typeOptions: [],
      typeOptionsThreeLevel: [],
      enableOptions: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ],
      maintainerOptions: [],
      versionOptions: [],
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentButton: {},
      rowTagList: [],
      isTagCodes: false,
      tagList: [],
      props: { multiple: true, emitPath: false, checkStrictly: true }
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.query()
    this.getMaintainerList()
    this.getDmiRuleTypeList(2)
    this.getDmiRuleTypeList(3)
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    executeButtonMethod(button, row) {
      const data = JSON.parse(JSON.stringify(row))
      const commonParams = {
        row: data,
        maintainerOption: this.maintainerOptions,
        typeOption: this.typeOptionsThreeLevel
      }
      if (button.name === '查看详情') {
        this.$refs.dmiRuleConfigRef.show({ ...commonParams, mode: 'view' })
      } else if (button.name === '编辑') {
        this.$refs.dmiRuleConfigRef.show({ ...commonParams, mode: 'edit' })
      } else {
        this.currentButton = {}
        this[button.buttonCode](data, button)
      }
    },
    async openDmiRuleConfigDialog() {
      this.$refs.dmiRuleConfigRef.show({
        mode: 'create',
        maintainerOption: this.maintainerOptions,
        typeOption: this.typeOptionsThreeLevel
      })
    },
    openHistoryVersion(row) {
      this.$refs.dmiRuleHistoryVersionRef.show(this.maintainerOptions, this.typeOptionsThreeLevel, row)
    },

    refresh() {
      this.$refs.tableRef.clearSelection()
      this.queryParam.current = 1
      this.query()
    },
    query() {
      const queryParams = {
        ...this.queryParam,
        subTypes: Array.isArray(this.queryParam.subTypes)
          ? this.queryParam.subTypes.map(item => (Array.isArray(item) ? item[item.length - 1] : item)).join(',')
          : '',
        tagCodes: this.tagCodeList?.map(tag => tag.code).join(','),
        maintainerEmpIds: Array.isArray(this.queryParam.maintainerEmpId)
          ? this.queryParam.maintainerEmpId.join(',')
          : this.queryParam.maintainerEmpId
      }
      if (queryParams.enable === '') {
        delete queryParams.enable
      }
      delete queryParams.maintainerEmpId
      pageDmiRuleDefinition(queryParams).then(res => {
        this.pageData = res.data
      })
    },
    reset() {
      this.queryParam = {
        current: 1,
        size: 10,
        maintainerEmpId: '',
        subTypes: ''
      }
      this.tagCodeList = []
      this.query()
    },
    handleClear(field) {
      if (field === 'maintainerEmpId') {
        this.queryParam.maintainerEmpId = ''
      } else if (field === 'subTypes') {
        this.queryParam.subTypes = ''
      } else if (field === 'enable') {
        this.queryParam.enable = ''
      }
      this.refresh()
    },
    getMaintainerList() {
      getDmiRuleEmployees().then(res => {
        const list = res.data || []
        this.maintainerOptions = list.map(item => ({
          label: item.name,
          value: item.id
        }))
      })
    },
    async getDmiRuleTypeList(level) {
      const res = await getDmiRuleTreeTypeLevel(level)
      const list = Array.isArray(res.data) ? res.data : []

      const buildOptions = items =>
        items.map(({ code, name, disabled = false, children = [] }) => ({
          value: code,
          label: name,
          disabled,
          ...(children.length ? { children: buildOptions(children) } : {})
        }))
      const result = buildOptions(list)
      if (level === 2) {
        this.typeOptions = result
      } else if (level === 3) {
        this.typeOptionsThreeLevel = result
      }
      return result
    },
    add() {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
    },
    onEnableClick(row) {
      const originalValue = row.enable
      const targetValue = originalValue === 1 ? 0 : 1
      this.$confirm(BASE_CONSTANT.CONFIRM_MSG, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateDmiRuleDefinitionStatus(row.id, targetValue).then(() => {
          row.enable = targetValue
          this.query()
          this.$message.success('修改成功')
        })
      })
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveDmiRuleDefinition(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateDmiRuleDefinition(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
      })
    },
    edit(row) {
      this.dialogTitle = this.$t('修改')
      this.dialogStatus = 'edit'
      getDmiRuleDefinition(row.id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    view(row) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      getDmiRuleDefinition(row.id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },

    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids }).then(() => {
        setTimeout(() => {
          this.refresh()
        }, 50)
      })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDmiRuleDefinition(param).then(() => {
          this.refresh()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    getRowKeys(row) {
      return row.id
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    tagCodesDistribute() {
      this.isTagCodes = true
      this.tagDistributeDrawerVisible = true
      this.rowTagList = this.tagCodeList || []
    },
    drawerClick(data) {
      if (this.isTagCodes) {
        this.confirmDistributeTagCodes(data)
        return
      }
      this.confirmDistributeTags(data)
    },
    confirmDistributeTags(data) {
      this.formData.tagSingleMatch = data?.isOr?.value
      if (data && data.tagList && data.tagList.length) {
        this.tagList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagList(tagGroup)
        })
      }
      if (this.tagList?.length > 30) {
        showToast('只支持选择30个以内的标签，请去除非必要标签', 'warning')
        return
      }
      if (this.tagList?.length > 0) {
        this.formData.tagIds = []
        this.tagList.forEach(item => {
          this.formData.tagIds.push(item.id)
        })
        this.formData.tagIds = this.formData.tagIds.join(',')
      } else {
        this.formData.tagIds = undefined
        this.formData.tagSingleMatch = undefined
      }
      this.tagDistributeDrawerVisible = false
    },
    confirmDistributeTagCodes(data) {
      this.formData.tagSingleMatch = data?.isOr?.value
      if (data && data.tagList && data.tagList.length) {
        this.tagCodeList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagCodeList(tagGroup)
        })
      }
      if (this.tagCodeList?.length > 30) {
        showToast('只支持选择30个以内的标签，请去除非必要标签', 'warning')
        return
      }
      if (this.tagCodeList?.length > 0) {
        this.formData.tagCodes = []
        this.tagCodeList.forEach(item => {
          if (item.code) {
            this.formData.tagCodes.push(item.code)
          }
        })
        this.formData.tagCodes = this.formData.tagCodes.join(',')
      } else {
        this.formData.tagCodes = undefined
        this.formData.tagSingleMatch = undefined
      }
      this.tagDistributeDrawerVisible = false
    },
    saveCheckedTagList(group) {
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.tagList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      }
    },
    saveCheckedTagCodeList(group) {
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.tagCodeList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagCodeList(subGroup)
        })
      }
    },
    handleCodeClose(tag) {
      this.tagCodeList = this.tagCodeList.filter(item => {
        return item !== tag
      })
      this.rowTagList = this.tagCodeList
    },
    clearAllTags() {
      this.tagCodeList = []
      this.rowTagList = []
      this.refresh()
    }
  }
}
</script>

<style scoped lang="scss">
.label-txt {
  margin-right: 5px;
  font-size: 12px;
  white-space: nowrap;
}

.filter-item {
  display: flex;
  align-items: center;
  width: 100%;
  flex: 1;
  min-width: 0;
}

.ltw-search-toolbar {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 40px;
}

.filter-area {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  flex: 1;
}

.tag-select {
  border: 1px solid #ebeef5;
  cursor: pointer;
  border-radius: 4px;
  padding: 0 10px;
  min-height: 24px;
  width: 300px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  overflow: hidden;
  gap: 4px;
}

.tag-placeholder {
  color: #c0c4cc;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
  display: inline-block;
}

.button-area {
  display: flex;
  align-items: center;
}

.tag-select-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 6px 8px;
  height: 24px;
  width: 300px;
}

.tag-select-content {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  align-items: center;
  cursor: pointer;
}

.clear-icon {
  color: #999;
  cursor: pointer;
  margin-right: 1px;
  font-size: 12px;
}
</style>
