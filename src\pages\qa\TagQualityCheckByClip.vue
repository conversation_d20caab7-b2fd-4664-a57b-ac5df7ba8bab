<template>
  <el-dialog
    v-model="visible"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    fullscreen
    class="tag-quality-check-dialog"
    destroy-on-close
    :ref="infoData?.id + 'tagQaRef'"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="info-header">
        <p class="title">{{ $t('tag质检') }}</p>
        <el-button type="danger" @click="close">
          关闭
        </el-button>
      </div>
    </template>
    <el-row class="info-container">
      <info-data v-model="infoData" class="description"></info-data>
      <div class="selector-panel">
        <div class="main-view">
          <el-radio-group v-model="videoModal" size="small" @change="handleVideoModelChange">
            <el-radio :label="true">{{ $t('均分模式') }}</el-radio>
            <el-radio :label="false">{{ $t('主视图模式') }}</el-radio>
          </el-radio-group>
        </div>
        <div class="main-view">
          <span class="title p-r">{{ $t('摄像头') }}</span>
          <el-select
            v-model="cameraSelected"
            size="small"
            filterable
            multiple
            collapse-tags
            collapse-tags-tooltip
            class="camera-container"
            @visible-change="handleCameraVisibleChange"
          >
            <el-option v-for="item in cameraList" :key="item" :label="item.code" :value="item.code"></el-option>
          </el-select>
        </div>
        <div class="main-view" v-show="!videoModal">
          <span class="title p-r">{{ $t('主视图') }}</span>
          <el-select v-model="mainCamera" @change="changeMainCamera()" size="small" filterable>
            <el-option v-for="item in cameraSelected" :key="item" :label="item" :value="item"></el-option>
          </el-select>
        </div>
        <div class="main-view">
          <span class="title p-r">{{ $t('压缩比例') }}</span>
          <!--          <el-input-number-->
          <!--            v-model="websocketParam.compressionFactor"-->
          <!--            @change="handleCompressionFactorChanged"-->
          <!--            size="small"-->
          <!--          ></el-input-number>-->
          <el-tag>{{ websocketParam.compressionFactor }}</el-tag>
        </div>
        <div class="main-view">
          <span class="title p-r-15">{{ $t('当前切片总帧数') }}</span>
          <template v-if="videoLoading">-</template>
          <template v-else>{{ Object.keys(currentDataPlayMap).length || 0 }}</template>
        </div>
        <div class="main-view">
          <span class="title p-r-15">{{ $t('当前切片位置') }}</span>
          {{ currentDurationIndex + 1 }}
        </div>
        <div class="main-view" v-if="checkedItem.trajectory?.length">
          <span class="title p-r">{{ $t('小球运动速度') }}</span>
          <el-select v-model="animationSpeed" size="small" class="play-speed-selector" style="width: 80px">
            <el-option
              v-for="item in animationSpeedOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="main-view">
          <span class="title p-r">{{ $t('明细编码') }}</span>
          {{checkedItem.code || '-'}}
        </div>
        <el-checkbox
          v-model="autoStartNextDetail"
          :label="$t('自动开始质检')"
          v-if="status !== 'view' || infoData.status !== 'finished'"
          @keydown.space.prevent
          tabindex="-1"
        ></el-checkbox>
        <div class="main-view btn-content" v-if="checkedItem.status === 'not_start' && status !== 'view'">
          <el-button size="small" type="primary" @click="startQualityCheck">{{ $t('开始质检') }}</el-button>
        </div>
        <div
          class="main-view btn-content"
          v-if="checkedItem.status === 'executing' && !['check', 'view'].includes(status)"
        >
          <el-button size="small" type="primary" @click="finishQualityCheck">{{ $t('结束质检') }}</el-button>
        </div>
        <div
          class="main-view btn-content"
          v-if="checkedItem.status === 'finished' && !['check', 'view'].includes(status)"
        >
          <el-button size="small" type="primary" @click="correctQualityCheck">{{ $t('修正质检') }}</el-button>
        </div>
        <div class="main-view btn-content" v-if="status === 'check'">
          <el-button size="small" type="primary" @click="passQualityCheck">{{ $t('通过') }}</el-button>
          <el-button size="small" type="primary" @click="rejectQualityCheck">{{ $t('驳回') }}</el-button>
        </div>
        <el-popover placement="top-start" :width="150" trigger="click" v-if="infoData.remark">
          <template #reference>
            <el-icon style="cursor: pointer; margin-left: 5px">
              <QuestionFilled />
            </el-icon>
          </template>
          <div style="font-size: 12px">{{ infoData.remark }}</div>
        </el-popover>
      </div>
    </el-row>
    <div class="content">
      <div class="left">
        <div class="top">
          <el-scrollbar height="100%">
            <template v-if="durationList?.length">
              <div class="duration-container" v-for="(item, index) in durationList" :key="item.id">
                <el-button
                  @click="handleClickDetail(item, index)"
                  size="small"
                  :class="{ 'is-active': item.id === checkedItem.id }"
                  style="width: 200px"
                >
                  {{ item.startTime }}
                  <el-divider direction="vertical" />
                  <el-link :type="getStatusType(item.status)" :underline="false">{{ item.statusName }}</el-link>
                </el-button>
              </div>
            </template>
          </el-scrollbar>
        </div>
        <div class="bottom">
          <div class="tip-container">
            <div class="shortcut-key" v-for="item in Object.keys(shortcutKeyMap)" :key="item">
              <div class="shortcut-key-button">{{ item }}</div>
              <div class="shortcut-key-text">{{ $t(shortcutKeyMap[item]) }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="main-content" v-loading="videoLoading">
        <div class="video-list">
          <div :class="videoModal ? 'default-video-row' : 'video-row'" v-if="cameraSelected?.length">
            <template v-if="videoModal">
              <div v-for="camera in cameraSelected" :key="camera">
                <div class="el-image" @dblclick="previewImage(camera)">
                  <div class="image-name" v-text="camera"></div>
                  <canvas :id="camera"></canvas>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="video-container">
                <div class="el-image" @dblclick="previewImage(mainCamera)">
                  <div class="image-name" v-text="mainCamera"></div>
                  <canvas :id="mainCamera"></canvas>
                </div>
              </div>
              <div class="video-container">
                <div v-for="camera in cameraSelected" :key="camera">
                  <div v-if="camera !== mainCamera" class="el-image" @dblclick="previewImage(camera)">
                    <div class="image-name" v-text="camera"></div>
                    <canvas :id="camera"></canvas>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div v-else class="no-data">暂无数据</div>
        </div>
        <div class="video-control">
          <div class="video-slider">
            <el-slider
              size="small"
              v-model="sliderNumber"
              :min="0"
              :max="sliderNumberMax"
              :marks="sliderMarkMap"
              :disabled="!sliderNumberMax"
              @change="handleSliderDragged()"
            />
            <el-tag
              style="margin: 0 10px"
              v-if="currentDataPlayMap[[measurementIndexMap[sliderNumber]]]?.dataList?.length"
            >
              <span v-if="measurementIndexMap[sliderNumber]" v-text="measurementIndexMap[sliderNumber]"></span>
              <ltw-icon v-else icon-class="is-loading" icon-code="el-icon-loading"></ltw-icon>
            </el-tag>
          </div>
          <div class="video-play">
            <ltw-icon @click="videoTurnLeft()" icon-code="el-icon-arrow-left" class="video-button"></ltw-icon>
            <ltw-icon @click="videoTurnRight()" icon-code="el-icon-arrow-right" class="video-button"></ltw-icon>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="r-l">
          <div class="top">
            <el-divider content-position="left"
              >{{ $t('clip标签') }}
              <el-popover placement="top-start" :width="150" trigger="click">
                <template #reference>
                  <el-icon style="cursor: pointer; margin-left: 5px">
                    <QuestionFilled />
                  </el-icon>
                </template>
                <div style="font-size: 12px">双击标签从当前clip标签面板移除标签</div>
              </el-popover>
            </el-divider>
            <el-scrollbar height="98%">
              <template v-if="copyCurrentDurationTagList?.length">
                <template v-for="item in copyCurrentDurationTagList" :key="item.code">
                  <el-tag :type="checkType(item)" @dblclick="deleteTag(item)">{{ item.name }}</el-tag>
                </template>
              </template>
            </el-scrollbar>
          </div>
          <div class="bottom">
            <el-divider content-position="left">{{ $t('当前帧标签') }}</el-divider>
            <el-scrollbar height="96%">
              <template v-if="currentDataPlayMap[measurementIndexMap[sliderNumber]]?.tags">
                <template v-for="item in currentDataPlayMap[measurementIndexMap[sliderNumber]]?.tags" :key="item.code">
                  <el-tag :type="checkType(item)">{{ item.name }}</el-tag>
                </template>
              </template>
            </el-scrollbar>
          </div>
        </div>
        <div class="r-r">
          <el-divider content-position="left"
            >{{ $t('质检标签') }}
            <el-popover placement="top-start" :width="150" trigger="click">
              <template #reference>
                <el-icon style="cursor: pointer; margin-left: 5px">
                  <QuestionFilled />
                </el-icon>
              </template>
              <div style="font-size: 12px">双击标签添加该标签</div>
            </el-popover>
          </el-divider>
          <el-scrollbar height="96%">
            <template v-for="item in groupedTagList" :key="item.groupCode">
              <el-link type="primary" class="group-name" :underline="false">{{ item.groupName }}</el-link>
              <template v-for="tag in item.tags">
                <el-tag
                  :type="checkType(tag)"
                  class="qa-tag"
                  :class="{ 'is-gray': isQaTag(tag) }"
                  @dblclick="addTag(tag)"
                  >{{ tag.name }}
                </el-tag>
              </template>
            </template>
          </el-scrollbar>
        </div>
      </div>
    </div>
    <preview-image :image-src="imgSrc" v-if="dialogImgVisible" @close-viewer="closeViewer"></preview-image>
  </el-dialog>
</template>
<script>
import { sendSock, closeWebSocket } from '@/plugins/socket/index2'
import util, { initWebSocket, checkTagType, checkType, debounce, showToast, throttle } from '@/plugins/util'
import InfoData from '@/components/qa/InfoData.vue'
import { listFtmVehicleModalitySelection } from '@/apis/fleet/ftm-vehicle-modality'
import {
  finishQaTagTaskDetail,
  listQaTagTaskDetail,
  startQaTagTaskDetail,
  getDetailBev,
  updateQaTagTaskDetail
} from '@/apis/qa/qa-tag-task-detail'
import LtwIcon from '@/components/base/LtwIcon.vue'
import GLB_CONFIG from '@/plugins/glb-constant'
import PreviewImage from '@/components/bia/biaAnonQaTask/PreviewImage.vue'
import { ElMessageBox } from 'element-plus'
import {batchStartQaTagTask, finishCheckQaTagTask, updateQaTagTask} from '@/apis/qa/qa-tag-task'
// import { r_0 } from '@/components/geography/r_0'
// import { r_1 } from '@/components/geography/r_1'
// import { r_3 } from '@/components/geography/r_3'
// import { r_4 } from '@/components/geography/r_4'
// import { r_5 } from '@/components/geography/r_5'
// import { r_6 } from '@/components/geography/r_6'
// import { r_7 } from '@/components/geography/r_7'

const defaultFormData = {}
export default {
  name: 'TagQualityCheckByClip',
  components: { PreviewImage, LtwIcon, InfoData },
  props: {
    tagQaLevel: {
      type: String,
      default: ''
    },
  },
  emits: ['closed'],
  data() {
    return {
      initWebSocket,
      checkTagType: checkTagType,
      checkType: checkType,
      visible: false,
      infoData: Object.assign({}, defaultFormData),
      cameraList: [],
      cameraSelected: ['FrontCam02', 'RearCam01', 'SurCam01', 'SurCam02', 'SurCam03', 'SurCam04'],
      mainCamera: 'FrontCam02',
      videoModal: false, //主视图模式
      checkedItem: Object.assign({}, defaultFormData),
      //websocket
      websocketUrl: '/websocket/qa/qa_tag_tasks',
      websocket: null,
      websocketParam: {
        compressionFactor: 2,
        tenantCode: this.$store.state.permission.currentUser.currentTenantCode
      },
      durationList: [],
      videoLoading: false, //loading，
      currentDataPlayMap: Object.assign({}, defaultFormData),
      measurementIndexMap: Object.assign({}, defaultFormData),
      //slider
      totalFragment: 0,
      sliderNumber: 0,
      sliderNumberMax: 0,
      nowDate: '',
      sliderMarkMap: {}, //进度条标记
      currentDurationIndex: 0,
      dataPlayMap: {},
      acceptDataPlayMap: {},
      acceptDurationIndex: 0,
      directLoading: true,
      status: '',
      currentRow: {},
      currentDurationTagList: [],
      copyCurrentDurationTagList: [],
      addTagList: [],
      deleteTagList: [],
      shortcutKeyMap: {
        A: '上一帧',
        D: '下一帧',
        SPACE: '结束质检'
      },
      autoStartNextDetail: true,
      bevTransform: null,
      animationId: null, // 保存 requestAnimationFrame 的 ID
      blueDotPosition: 0, // 蓝点在轨迹上的位置 (0-1 之间的百分比)
      isAnimating: false, // 动画是否正在运行
      pathLength: 0, // 路径总长度（像素）
      pathSegments: [], // 路径段信息
      animationSpeed: 50, // 沿路径每秒移动的像素数
      lastTimestamp: 0, // 上一帧的时间戳
      animationSpeedOptions: [
        { label: '0.5x', value: 25 },
        { label: '1x', value: 50 },
        { label: '1.5x', value: 75 },
        { label: '2x', value: 100 },
        { label: '3x', value: 150 }
      ],
      loadBevImage: true,
      imgSrc: null,
      dialogImgVisible: false,
      workBatchParam:{}
    }
  },
  watch: {},
  computed: {
    groupedTagList() {
      const groupedMap = {}
      this.infoData.tagList.forEach(tag => {
        if (!groupedMap[tag.groupCode]) {
          groupedMap[tag.groupCode] = {
            groupCode: tag.groupCode,
            groupName: tag.groupName,
            tags: []
          }
        }
        groupedMap[tag.groupCode].tags.push(tag)
      })
      return Object.values(groupedMap)
    }
  },
  created() {},
  mounted() {},
  unmounted() {
    closeWebSocket()
  },
  methods: {
    isQaTag(tag) {
      return this.copyCurrentDurationTagList.some(infoTag => infoTag.code === tag.code)
    },
    dialogOpened() {
      this.$nextTick(() => {
        let _this = this
        window.addEventListener('resize', debounce(_this.resizeCanvas, 300))
        window.addEventListener('keydown', _this.installKeyList)
      })
    },
    async show(data, status,workBatchParam) {
      this.loadBevImage = true
      this.visible = true
      this.autoStartNextDetail = true
      this.infoData = data
      this.infoData.tagCodeList = this.infoData.tags.split(',')
      if(workBatchParam && Object.keys(workBatchParam).length > 0){
        this.workBatchParam=workBatchParam
      }else{
        this.workBatchParam={}
      }
      if (!this.cameraList?.length) {
        await this.listCamera()
      }
      this.status = status
      if (!this.videoModal) {
        let index = this.cameraSelected.findIndex(item => item.code === this.mainCamera)
        if (index < 0) {
          this.websocketParam.modalities = [...this.cameraSelected, this.mainCamera].join(',')
        } else {
          this.websocketParam.modalities = [...this.cameraSelected].join(',')
        }
      } else {
        this.websocketParam.modalities = [...this.cameraSelected].join(',')
      }
      await this.listDuration()
      initWebSocket(this.websocketUrl)
    },
    listCamera() {
      // listFtmVehicleModalitySelection({ sensorType: 'camera' }).then(res => {
      //   this.cameraList = res.data.filter(item => item.code !== this.mainCamera)
      //   this.cameraList.unshift({ name: 'Bev', code: 'bev' })
      // })
      this.cameraList = [
        { name: 'Bev', code: 'bev' },
        { name: 'FrontCam02', code: 'FrontCam02' },
        { name: 'SurCam01', code: 'SurCam01' },
        { name: 'SurCam02', code: 'SurCam02' },
        { name: 'SurCam03', code: 'SurCam03' },
        { name: 'SurCam04', code: 'SurCam04' },
        { name: 'RearCam01', code: 'RearCam01' }
      ]
    },
    listDuration() {
      // this.infoData.code = 'tag_qa202407010201'
      let param = {}
      if (this.workBatchParam && Object.keys(this.workBatchParam).length > 0) {
        param = { ...this.workBatchParam }
      } else {
        let samplingCheck = this.status === 'check' ? true : false
        param = {
          taskCode: this.infoData.code,
          samplingCheck: samplingCheck
        }
      }
      listQaTagTaskDetail(param).then(res => {
        this.durationList = res.data
        if (!this.durationList?.length) {
          showToast('该任务暂无明细', 'warning')
          return
        }
        // this.durationList[0].bevMap = { bev: require('@/components/geography/bev3.png') }
        // this.durationList[0].trajectory = r_5
        // this.durationList[1].bevMap = { bev: require('@/components/geography/bev3.png') }
        // this.durationList[1].trajectory = r_6
        // this.durationList[2].bevMap = { bev: require('@/components/geography/bev2.png') }
        // this.durationList[2].trajectory = r_3
        // this.durationList[3].bevMap = { bev: require('@/components/geography/bev2.png') }
        // this.durationList[3].trajectory = r_4
        this.checkedItem = this.durationList[0]
        this.currentDurationIndex = 0
        this.dataPlayMap = {}
        let durationTagList = this.durationList[0]?.tagList.filter(item1 =>
          this.infoData.tagList.some(item2 => item1.code === item2.code && item1.tagCode === item2.tagCode)
        )
        this.currentDurationTagList = JSON.parse(JSON.stringify(durationTagList))
        this.copyCurrentDurationTagList = JSON.parse(JSON.stringify(durationTagList))
        if (this.durationList[0].status !== 'not_start' || this.status === 'view') {
          this.getPageData(this.checkedItem, this.currentDurationIndex)
        }
      })
    },
    getStatusType(status) {
      const map = {
        not_start: 'primary',
        executing: 'warning',
        rejected: 'danger'
      }
      return map[status] || 'success'
    },
    previewImage(camera) {
      let filePath = this.currentDataPlayMap[this.measurementIndexMap?.[this.sliderNumber]]?.filePathMap?.[camera] || ''
      let url =
        GLB_CONFIG.devUrl.serviceSiteRootUrl +
        '/data/frame_data/preview' +
        '?filePath=' +
        filePath +
        '&token=' +
        util.getToken()
      this.imgSrc = url
      this.dialogImgVisible = true
    },
    closeViewer() {
      this.dialogImgVisible = false
      this.imgSrc = ''
    },
    getCurrentDetailTaskData() {
      this.websocketParam.detailId = this.checkedItem.id
      this.getPlayResult(this.websocketParam, true, this.currentDurationIndex)
    },
    deletePreDurationPlayData() {
      if (this.currentDurationIndex > 0) {
        delete this.dataPlayMap[this.durationList[this.currentDurationIndex - 1].id]
      }
    },
    getPlayResult(param, loading, durationIndex) {
      this.acceptDurationIndex = durationIndex
      this.videoLoading = loading
      sendSock(
        param,
        res => {
          if (res.data.detailId === this.durationList[durationIndex].id) {
            this.parsePlayData(res.data, durationIndex, this.loadingFinish)
          } else {
            this.videoLoading = false
          }
        },
        err => {
          this.videoLoading = false
        }
      )
    },
    loadingFinish(resetDataPlay, durationIndex, parseAcceptData, total) {
      this.videoLoading = false
      if (resetDataPlay) {
        this.dataPlayMap[this.durationList[durationIndex].id] = {
          acceptAmountStatus: 'all',
          playData: parseAcceptData,
          total: total
        }
        if (durationIndex < this.durationList?.length - 1 && Object.keys(this.dataPlayMap).length < 5) {
          this.acceptDurationIndex = durationIndex + 1
          this.websocketParam.detailId = this.durationList[this.acceptDurationIndex].id
          this.getPlayResult(this.websocketParam, false, durationIndex + 1)
        }
        if (this.directLoading) {
          this.directLoading = false
          this.playVideo()
        }
        return
      }
      if (this.currentDurationIndex == durationIndex) {
        this.playVideo()
        let flag =
          this.acceptDurationIndex < this.durationList?.length - 1 &&
          Object.keys(this.dataPlayMap).length < 5 &&
          !this.dataPlayMap[this.durationList[this.acceptDurationIndex + 1].id] &&
          this.dataPlayMap[this.durationList[this.acceptDurationIndex].id]?.acceptAmountStatus == 'all'
        if (flag) {
          this.acceptDurationIndex = this.acceptDurationIndex + 1
          this.websocketParam.detailId = this.durationList[this.acceptDurationIndex].id
          this.getPlayResult(this.websocketParam, false, this.acceptDurationIndex)
        }
      }
    },
    playVideo() {
      let currentDataMap = this.dataPlayMap[this.checkedItem.id]
      this.currentDataPlayMap = JSON.parse(JSON.stringify(currentDataMap.playData))
      this.sliderNumberMax =
        Object.keys(currentDataMap.playData)?.length > 0 ? Object.keys(currentDataMap.playData)?.length - 1 : 0
      for (let index in Object.keys(this.currentDataPlayMap)) {
        this.measurementIndexMap[index] = Object.keys(this.currentDataPlayMap)[index]
      }
      this.resizeCanvas(true)
    },
    parseAcceptData(acceptData) {
      const tags = [],
        tagMap = {}
      for (let item of acceptData.measurementDataList) {
        if (item.tags?.length) {
          for (let tag of item.tags) {
            if (this.infoData.tagCodeList?.includes(tag.tagCode) && !tagMap[tag.tagCode]) {
              tags.push(tag)
              tagMap[tag.tagCode] = tag
            }
          }
        }
        this.dataPlayMap[acceptData.detailId].playData[item.measurement] = {
          dataList: item.dataList,
          tagMap: JSON.parse(JSON.stringify(tagMap)),
          tags: tags,
          filePathMap: item.filePathMap
        }
      }
    },
    parsePlayData(acceptData, durationIndex, callback) {
      let tagMap = {}
      let tags = []
      if (!this.dataPlayMap[acceptData.detailId]) {
        this.dataPlayMap[acceptData.detailId] = {}
      }
      if (!this.dataPlayMap[acceptData.detailId].playData) {
        this.dataPlayMap[acceptData.detailId].playData = {}
      }
      let acceptPlayDataAmount = []
      if (acceptData.total !== 0) {
        this.parseAcceptData(acceptData)
        acceptPlayDataAmount = Object.values(this.dataPlayMap[acceptData.detailId].playData).flat(Infinity)
      }
      if (acceptPlayDataAmount?.length === acceptData.total) {
        this.videoLoading = false
        callback(true, durationIndex, this.dataPlayMap[acceptData.detailId].playData, acceptData.total)
      }
    },
    installKeyList(e) {
      let _this = this
      switch (e.key) {
        case 'a' || 'A':
          _this.videoTurnLeft()
          break
        case 'd' || 'D':
          _this.videoTurnRight()
          break
        case ' ':
          if (this.checkedItem.status === 'executing' && this.status === 'quality_check') {
            _this.finishQualityCheck()
            break
          }
      }
    },
    handleClickDetail(data, index) {
      if (this.currentDurationIndex === index) return
      if (
        this.durationList[this.currentDurationIndex]?.status === 'executing' &&
        this.status !== 'view' &&
        data.status !== 'finished'
      )
        return showToast('请先完成当前切片的检查', 'warning')
      this.getPageData(data, index)
    },
    getDetailBev(data, index) {
      return getDetailBev(data.clipId).then(res => {
        data.trajectory = res.data.trajectory || []
        data.bevMap = res.data.bevMap || {}
        if (!data.bevMap || Object.keys(data.bevMap).length === 0) {
          this.loadBevImage = false
        }
      })
    },
    async getPageData(data, index) {
      if (!data?.trajectory?.length && this.cameraSelected.includes('bev') && this.loadBevImage) {
        await this.getDetailBev(data, index)
      }
      this.blueDotPosition = 0
      this.pathLength = 0
      this.pathSegments = []
      // 如果动画已经在运行，停止后重新启动以应用新轨迹
      if (this.isAnimating) {
        this.stopAnimation()
        // this.startAnimation()
      }
      this.videoLoading = false
      this.checkedItem = data
      this.currentDurationIndex = index
      this.resetSlider()
      let durationTagList = this.durationList[index]?.tagList.filter(item1 =>
        this.infoData.tagList.some(item2 => item1.code === item2.code && item1.tagCode === item2.tagCode)
      )
      this.currentDurationTagList = JSON.parse(JSON.stringify(durationTagList))
      this.copyCurrentDurationTagList = JSON.parse(JSON.stringify(durationTagList))
      this.$nextTick(() => {
        this.resizeCanvas(false)
      })
      if (this.checkedItem.status !== 'not_start' || this.status === 'view') {
        if (!this.dataPlayMap[this.checkedItem.id]) {
          this.dataPlayMap = {}
          this.directLoading = true
          this.getCurrentDetailTaskData()
          this.$nextTick(() => {
            this.resizeCanvas(true)
          })
          return
        }
        this.deletePreDurationPlayData()
        let currentDataMap = this.dataPlayMap[this.checkedItem.id]
        if (currentDataMap.acceptAmountStatus == 'all') {
          this.loadingFinish(false, this.currentDurationIndex)
          return
        }
        this.directLoading = true
        this.videoLoading = true
      }
    },
    resizeCanvas(cameraChange) {
      let videoNode = document.querySelector('.video-list')
      let canvasList = document.querySelectorAll('.el-image canvas')
      for (let i = 0, iLen = canvasList?.length; i < iLen; i++) {
        if (this.videoModal) {
          canvasList[i].height = this.getVideoHeight()
          canvasList[i].width = this.getDefaultVideoWidth()
        } else {
          if (canvasList[i].id === this.mainCamera) {
            canvasList[i].height = videoNode.offsetHeight - 2
            canvasList[i].width = videoNode.offsetWidth / 2 - 2
          } else {
            canvasList[i].height = this.getVideoHeight()
            canvasList[i].width = this.getVideoWidth()
          }
        }
      }
      if (cameraChange) {
        this.loadImage()
      }
    },
    getRoutePointsWithTransform(geomArr) {
      if (!this.bevTransform) return []
      const { scale, dx, dy } = this.bevTransform
      return geomArr?.map(([t, x, y]) => ({
        x: x * scale + dx,
        y: y * scale + dy
      }))
    },
    async drawRouteAndArrows(ctx, routePoints) {
      if (!routePoints.length) return
      ctx.save()
      ctx.strokeStyle = '#FF5733'
      ctx.lineWidth = 5
      ctx.beginPath()
      ctx.moveTo(routePoints[0].x, routePoints[0].y)
      for (let i = 1; i < routePoints.length; i++) {
        ctx.lineTo(routePoints[i].x, routePoints[i].y)
      }
      ctx.stroke()
      ctx.restore()
      let distSinceLast = 0
      for (let i = 1; i < routePoints.length; i++) {
        const prev = routePoints[i - 1]
        const curr = routePoints[i]
        const dx = curr.x - prev.x
        const dy = curr.y - prev.y
        const segLen = Math.sqrt(dx * dx + dy * dy)
        distSinceLast += segLen
        while (distSinceLast >= 20) {
          const ratio = (20 - (distSinceLast - segLen)) / segLen
          const tx = prev.x + dx * ratio
          const ty = prev.y + dy * ratio
          const angle = Math.atan2(dy, dx)
          await this.drawTextArrow(ctx, tx, ty, angle)
          distSinceLast -= 20
        }
      }
      await this.drawBlueDot(ctx, routePoints)
    },
    drawTextArrow(ctx, x, y, angle) {
      ctx.save()
      ctx.translate(x, y)
      ctx.rotate(angle)
      ctx.font = 'bold 6px Arial'
      ctx.fillStyle = '#FFFFFF'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillText('>', 0, 0)
      ctx.restore()
    },
    drawBlueDot(ctx, routePoints) {
      if (!routePoints.length) return
      // 如果路径信息尚未计算，先计算一下
      if (this.pathLength === 0) {
        this.calculatePathMetrics(routePoints)
      }
      // 计算当前位置对应的实际路径距离
      const targetDistance = this.blueDotPosition * this.pathLength
      // 找到蓝点所在的路径段
      let segmentIndex = 0
      for (let i = 0; i < this.pathSegments.length; i++) {
        if (targetDistance >= this.pathSegments[i].start && targetDistance <= this.pathSegments[i].end) {
          segmentIndex = i
          break
        }
      }
      const segment = this.pathSegments[segmentIndex]
      const [p1, p2] = segment.points
      // 计算在当前段中的比例
      const segmentLength = segment.end - segment.start
      const segmentPosition = (targetDistance - segment.start) / segmentLength
      // 计算蓝点的实际坐标
      const x = p1.x + (p2.x - p1.x) * segmentPosition
      const y = p1.y + (p2.y - p1.y) * segmentPosition
      // 绘制蓝点
      ctx.save()
      ctx.beginPath()
      ctx.arc(x, y, 4, 0, Math.PI * 2)
      ctx.fillStyle = '#2196f3'
      ctx.shadowColor = '#90caf9'
      ctx.shadowBlur = 8
      ctx.fill()
      ctx.restore()
      this.startAnimation()
    },
    calculatePathMetrics(routePoints) {
      this.pathSegments = []
      let totalLength = 0
      for (let i = 1; i < routePoints.length; i++) {
        const prev = routePoints[i - 1]
        const curr = routePoints[i]
        const dx = curr.x - prev.x
        const dy = curr.y - prev.y
        const segmentLength = Math.sqrt(dx * dx + dy * dy)
        this.pathSegments.push({
          start: totalLength,
          end: totalLength + segmentLength,
          length: segmentLength,
          points: [prev, curr]
        })
        totalLength += segmentLength
      }
      this.pathLength = totalLength
      return totalLength
    },
    startAnimation() {
      if (this.isAnimating) return
      const canvas = document.querySelector('.el-image canvas#bev')
      if (!canvas) return
      const geom = this.durationList[this.currentDurationIndex]?.trajectory
      if (!geom?.length) return
      const routePoints = this.getRoutePointsWithTransform(geom)
      if (!routePoints.length) return
      // 计算路径长度
      this.calculatePathMetrics(routePoints)
      this.isAnimating = true
      this.lastTimestamp = performance.now()
      this.animateBlueDot(this.lastTimestamp)
    },
    // 停止动画
    stopAnimation() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
        this.animationId = null
      }
      this.isAnimating = false
    },
    // 动画循环函数
    animateBlueDot(timestamp) {
      if (!this.isAnimating) return
      // 计算时间差（秒）
      const deltaTimeSeconds = (timestamp - this.lastTimestamp) / 1000
      this.lastTimestamp = timestamp
      // 计算这一帧应该移动的距离（像素）
      const distanceToMove = this.animationSpeed * deltaTimeSeconds
      // 计当前在路径上的实际位置（像素）
      const currentDistance = this.blueDotPosition * this.pathLength
      // 计算新位置
      let newDistance = currentDistance + distanceToMove
      // 如果超过路径终点，循环到起点
      if (newDistance >= this.pathLength) {
        newDistance = newDistance % this.pathLength
      }
      // 更新蓝点位置（转换为 0-1 之间的比例）
      this.blueDotPosition = newDistance / this.pathLength
      // 重绘
      this.loadImage()
      // 请求下一帧
      this.animationId = requestAnimationFrame(this.animateBlueDot.bind(this))
    },
    async loadImage() {
      this.projectionLoading = false
      let fragment = this.currentDataPlayMap[this.measurementIndexMap[this.sliderNumber]]
      if (fragment?.dataList?.length) {
        let imageList = fragment?.dataList
        let imageMap = {}
        imageList.forEach(item => {
          imageMap[item.modality] = item
        })
        let canvasList = document.querySelectorAll('.el-image canvas')
        for (let i = 0, iLen = canvasList?.length; i < iLen; i++) {
          let context = canvasList[i].getContext('2d')
          let img = new Image()
          if (canvasList[i].id === 'bev') {
            if (this.durationList[this.currentDurationIndex]?.bevMap?.bev) {
              let filePath = this.durationList[this.currentDurationIndex].bevMap.bev
              img.src =
                GLB_CONFIG.devUrl.serviceSiteRootUrl +
                '/data/frame_data/preview' +
                '?filePath=' +
                filePath +
                '&token=' +
                util.getToken()
              // img.src = filePath
              await this.initImg(context, img, canvasList[i])
              const geom = this.durationList[this.currentDurationIndex].trajectory
              if (geom?.length) {
                const routePoints = await this.getRoutePointsWithTransform(geom)
                await this.drawRouteAndArrows(context, routePoints)
              }
            } else {
              context.clearRect(0, 0, canvasList[i].width, canvasList[i].height)
            }
          } else {
            if (imageMap[canvasList[i].id]?.content) {
              img.src = 'data:image/jpeg;base64,' + imageMap[canvasList[i].id]?.content
              await this.initImg(context, img, canvasList[i])
            } else {
              context.clearRect(0, 0, canvasList[i].width, canvasList[i].height)
            }
          }
        }
      } else {
        let canvasList = document.querySelectorAll('.el-image canvas')
        for (let i = 0, iLen = canvasList?.length; i < iLen; i++) {
          let context = canvasList[i].getContext('2d')
          let img = new Image()
          context.clearRect(0, 0, canvasList[i].width, canvasList[i].height)
        }
      }
    },
    initImg(context, img, canvasDom) {
      return new Promise(resolve => {
        img.onload = () => {
          context.clearRect(0, 0, canvasDom.width, canvasDom.height)
          let canvasHeight, canvasWidth, dx, dy, scale
          if (canvasDom.width / canvasDom.height < img.width / img.height) {
            canvasHeight = (canvasDom.width / img.width) * img.height
            canvasWidth = canvasDom.width
            dx = 0
            dy = (canvasDom.height - canvasHeight) / 2
            scale = canvasWidth / img.width
          } else {
            canvasWidth = (canvasDom.height / img.height) * img.width
            canvasHeight = canvasDom.height
            dx = (canvasDom.width - canvasWidth) / 2
            dy = 0
            scale = canvasHeight / img.height
          }
          if (canvasDom.id === 'bev') {
            this.bevTransform = { scale, dx, dy }
          }
          this.drawImg(context, img, dx, dy, canvasWidth, canvasHeight)
          resolve()
        }
        img.onerror = () => {
          context.clearRect(0, 0, canvasDom.width, canvasDom.height)
          resolve()
        }
      })
    },
    drawImg(context, img, dx, dy, width, height) {
      context.drawImage(img, dx, dy, width, height)
    },
    handleVideoModelChange(val) {
      this.mainCamera = val ? '' : this.cameraSelected[0]
      this.stopAnimation() // 停止旧动画
      this.blueDotPosition = 0
      this.pathLength = 0
      this.pathSegments = []
      this.bevTransform = null
      this.$nextTick(async () => {
        await this.resizeCanvas()
        await this.loadImage() // loadImage结束后，canvas和bevTransform都已更新
        // this.startAnimation()   // 轨迹/蓝球位置必然使用了最新的canvas坐标和transform
      })
    },
    handleCameraVisibleChange(value) {
      if (value) {
        this.lastCameraSelected = this.cameraSelected
        return
      }
      let flag = false
      for (let camera of this.cameraSelected) {
        if (!this.lastCameraSelected?.length) {
          this.handleCameraSelectedChanged()
          return
        }
        for (let lastCamera of this.lastCameraSelected) {
          if (camera !== lastCamera) {
            this.handleCameraSelectedChanged()
            return
          }
        }
      }
    },
    handleSliderDragged: throttle(function () {
      let fragment = this.currentDataPlayMap[this.measurementIndexMap[this.sliderNumber]]
      if (fragment) {
        this.loadImage()
        return
      }
    }, 300),
    resetSlider() {
      this.sliderNumber = 0
      this.sliderNumberMax = 0
      this.sliderMarkMap = {}
      this.currentDataPlayMap = {}
      this.totalFragment = 0
    },
    async handleCameraSelectedChanged() {
      if (!this.cameraSelected?.length) {
        return
      }
      if (!this.checkedItem.trajectory?.length && this.cameraSelected.includes('bev') && this.loadBevImage) {
        await this.getDetailBev(this.checkedItem, this.currentDurationIndex)
      }
      this.mainCamera = this.cameraSelected[0]
      this.stopAnimation() // 停止旧动画
      this.blueDotPosition = 0
      this.pathLength = 0
      this.pathSegments = []
      this.bevTransform = null
      this.$nextTick(async () => {
        await this.resizeCanvas()
        await this.loadImage() // loadImage结束后，canvas和bevTransform都已更新
        // if(this.cameraSelected.includes('bev')){
        //   this.startAnimation()   // 轨迹/蓝球位置必然使用了最新的canvas坐标和transform
        // }   // 轨迹/蓝球位置必然使用了最新的canvas坐标和transform
      })
    },
    changeMainCamera(val) {
      this.stopAnimation() // 停止旧动画
      this.blueDotPosition = 0
      this.pathLength = 0
      this.pathSegments = []
      this.bevTransform = null
      this.$nextTick(async () => {
        await this.resizeCanvas()
        await this.loadImage() // loadImage结束后，canvas和bevTransform都已更新
        // if(this.cameraSelected.includes('bev')){
        //   this.startAnimation()   // 轨迹/蓝球位置必然使用了最新的canvas坐标和transform
        // }
      })
    },
    getVideoWidth() {
      let videoListWidth = document.querySelector('.video-list').offsetWidth / 2 - 10
      let cameraSelected = this.cameraSelected.filter(item => item !== this.mainCamera)
      let videoLength = cameraSelected.length
      if (videoLength) {
        if (videoLength === 1) {
          return videoListWidth
        } else {
          return Math.floor(videoListWidth / 2) - 2
        }
      } else {
        return videoListWidth
      }
    },
    getDefaultVideoWidth() {
      let videoListWidth = document.querySelector('.video-list').offsetWidth - 1
      let videoLength = this.cameraSelected?.length
      if (videoLength) {
        if (videoLength === 1) {
          return videoListWidth - 2
        } else if (videoLength >= 2 && videoLength <= 4) {
          return Math.floor(videoListWidth / 2) - 2
        } else if (videoLength >= 5 && videoLength <= 9) {
          return Math.floor(videoListWidth / 3) - 2
        } else {
          return Math.floor(videoListWidth / 4) - 2
        }
      } else {
        return videoListWidth
      }
    },
    getVideoHeight() {
      let videoNode = document.querySelector('.video-list')
      let cameraSelected = []
      cameraSelected = this.cameraSelected.filter(item => item !== this.mainCamera)
      let imageListLength = this.videoModal ? this.cameraSelected?.length : cameraSelected?.length
      if (videoNode) {
        let videoHeight = videoNode.offsetHeight - 1
        if (this.videoModal) {
          if (imageListLength >= 1 && imageListLength <= 2) {
            return videoHeight - 2
          } else if (imageListLength >= 3 && imageListLength <= 6) {
            return videoHeight / 2 - 2
          } else if (imageListLength >= 7 && imageListLength <= 9) {
            return videoHeight / 3 - 2
          } else if (imageListLength >= 10 && imageListLength <= 16) {
            return videoHeight / 4 - 2
          } else if (imageListLength >= 17 && imageListLength <= 20) {
            return videoHeight / 5 - 2
          } else {
            return videoHeight / Math.ceil(imageListLength / 4) - 5
          }
        } else {
          if (imageListLength >= 1 && imageListLength <= 2) {
            return videoHeight - 2
          } else if (imageListLength >= 3 && imageListLength <= 4) {
            return videoHeight / 2 - 2
          } else if (imageListLength >= 5 && imageListLength <= 6) {
            return videoHeight / 3 - 2
          } else if (imageListLength >= 7 && imageListLength <= 8) {
            return videoHeight / 4 - 2
          } else if (imageListLength >= 9 && imageListLength <= 10) {
            return videoHeight / 5 - 2
          } else {
            return videoHeight / Math.ceil(imageListLength / 2) - 2
          }
        }
      }
    },
    videoTurnLeft() {
      if (this.dialogImgVisible) return
      if (this.sliderNumberMax && this.sliderNumber >= 0) {
        if (this.sliderNumber === 0) {
          this.loadImage()
          return
        }
        this.sliderNumber--
        this.loadImage()
      }
    },
    videoTurnRight() {
      if (this.dialogImgVisible) return
      if (this.sliderNumberMax && this.sliderNumber < this.sliderNumberMax) {
        this.sliderNumber++
        this.loadImage()
      }
    },
    isTagAlreadyExist(tagList, tag) {
      if (!tagList?.length) return false
      return tagList.some(item => item.code === tag.code)
    },
    addTag(tag) {
      if (
        this.status === 'view' ||
        ['finished', 'not_start'].includes(this.durationList[this.currentDurationIndex].status)
      )
        return
      let tagInCurrentDurationTagList = this.isTagAlreadyExist(this.copyCurrentDurationTagList, tag)
      if (!tagInCurrentDurationTagList) {
        this.copyCurrentDurationTagList.unshift(tag)
      }
    },
    deleteTag(tag) {
      if (
        this.status === 'view' ||
        ['finished', 'not_start'].includes(this.durationList[this.currentDurationIndex].status)
      )
        return
      this.copyCurrentDurationTagList = this.copyCurrentDurationTagList?.filter(item => item.code !== tag.code)
    },
    startQualityCheck() {
      startQaTagTaskDetail(this.checkedItem.id).then(async res => {
        let index = this.durationList.findIndex(item => item.id === this.checkedItem.id)
        this.durationList[index].status = 'executing'
        this.durationList[index].statusName = '执行中'
        this.infoData.status = 'executing'
        this.infoData.statusName = '执行中'
        if (!this.checkedItem.trajectory?.length && this.currentDurationIndex === 0) {
          await this.getDetailBev(this.checkedItem, this.currentDurationIndex)
        }
        if (!this.dataPlayMap[this.checkedItem.id]) {
          this.dataPlayMap = {}
          this.directLoading = true
          this.getCurrentDetailTaskData()
          return
        }
        this.deletePreDurationPlayData()
        let currentDataMap = this.dataPlayMap[this.checkedItem.id]
        if (currentDataMap?.acceptAmountStatus == 'all') {
          this.loadingFinish(false, this.currentDurationIndex)
          return
        }
        this.directLoading = true
        this.videoLoading = true
      })
    },
    calculateAddAndDeleteTags() {
      this.addTagList = this.copyCurrentDurationTagList.filter(
        tag => !this.currentDurationTagList.some(currentTag => currentTag.code === tag.code)
      )
      this.deleteTagList = this.currentDurationTagList.filter(
        tag => !this.copyCurrentDurationTagList.some(currentTag => currentTag.code === tag.code)
      )
    },
    finishQualityCheck() {
      this.calculateAddAndDeleteTags()
      let addTagList = this.addTagList.map(item => item.code) || []
      let deleteTagList = this.deleteTagList.map(item => item.code) || []
      let detailId = this.durationList[this.currentDurationIndex].id
      let postData = {
        addTagList: addTagList,
        deleteTagList: deleteTagList
      }
      finishQaTagTaskDetail(detailId, postData).then(async res => {
        showToast('切片完成质检')
        this.addTagList = []
        this.deleteTagList = []
        this.infoData.finishCount++
        let copyData = JSON.parse(JSON.stringify(this.durationList[this.currentDurationIndex]))
        this.durationList[this.currentDurationIndex] = res.data
        this.durationList[this.currentDurationIndex].trajectory = copyData.trajectory
        this.durationList[this.currentDurationIndex].bevMap = copyData.bevMap
        if (this.workBatchParam && Object.keys(this.workBatchParam).length > 0 && this.currentDurationIndex === this.durationList?.length - 1 ) {
          this.startNextBatchTask()
        }
        if (this.currentDurationIndex < this.durationList?.length - 1) {
          this.currentDurationIndex++
          // this.checkedItem=this.durationList[this.currentDurationIndex]
          await this.getPageData(this.durationList[this.currentDurationIndex], this.currentDurationIndex)
          if (this.autoStartNextDetail && this.checkedItem.status === 'not_start') {
            this.startQualityCheck()
          }
        }
        if (this.currentDurationIndex === this.durationList?.length - 1) {
          this.infoData.status = 'finished'
          this.infoData.statusName = '完成'
          this.checkedItem = this.durationList[this.currentDurationIndex]
        }
      })
    },
    startNextBatchTask() {
      ElMessageBox.confirm('是否开启下一批次的作业?', 'Warning', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      })
        .then(() => {
          this.handleNextBatchTask()
        })
        .catch(() => {
          this.visible = false
          let _this = this
          window.removeEventListener('keydown', _this.installKeyList)
          window.removeEventListener('resize', debounce(_this.resizeCanvas, 300))
          this.initProps()
          this.stopAnimation()
          this.$emit('closed')
        })
    },
    handleNextBatchTask() {
      batchStartQaTagTask(this.workBatchParam).then(async res => {
        if (res.data.detailCount === 0) {
          showToast('您暂无可质检的作业,请认领任务后再次进入！', 'warning')
          this.dialogClosed()
          return
        }
        this.infoData = res.data
        res.data.finishCount = 0
        res.data.tags = res.data.tagList?.map(item => item.code).join(',')
        res.data.status = 'not_start'
        res.data.statusName = '未开始'
        await this.listDuration()
      })
    },
    correctQualityCheck() {
      updateQaTagTaskDetail({ id: this.checkedItem.id, status: 'executing' }).then(res => {
        this.infoData.finishCount--
        this.checkedItem.status = 'executing'
        this.checkedItem.statusName = '执行中'
      })
    },
    passQualityCheck() {
      finishCheckQaTagTask(this.infoData.id, { checkResult: 'accept' }).then(async res => {
        showToast('该任务已通过')
        this.dialogClosed()
      })
    },
    rejectQualityCheck() {
      ElMessageBox.prompt('请输入驳回原因', 'Tip', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputPattern: /^[\s\S]{1,600}$/, // 只做宽松基础限制，避免用户输太多
        inputErrorMessage: '最多输入600字符，请简明描述',
      })
        .then(({ value }) => {
          //驳回接口
          finishCheckQaTagTask(this.infoData.id, { checkResult: 'reject', remark: value }).then(async res => {
            showToast('该任务已被驳回')
            this.dialogClosed()
          })
        })
        .catch(() => {
          this.$message.info({
            message: '取消输入',
            type: 'info'
          })
        })
    },
    initProps() {
      this.sliderNumber = 0
      this.sliderNumberMax = 0
      this.currentDataPlayMap = this.dataPlayMap = {}
      this.cameraSelected = ['FrontCam02', 'RearCam01', 'SurCam01', 'SurCam02', 'SurCam03', 'SurCam04']
      this.lastCameraSelected = []
      this.mainCamera = 'FrontCam02'
      this.totalFragment = 0
      this.workBatchParam={}
      closeWebSocket()
    },
    dialogClosed() {
      this.visible = false
      let _this = this
      window.removeEventListener('keydown', _this.installKeyList)
      window.removeEventListener('resize', debounce(_this.resizeCanvas, 300))
      this.initProps()
      this.stopAnimation()
      this.$emit('closed')
    }
  }
}
</script>
<style lang="scss" scoped>
.p-r-15 {
  padding-right: 15px;
}
:deep(header.el-dialog__header){
  padding: 5px !important;
}

.info-header{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  .title {
    margin: 0 auto ;
    font-size: 12px;
  }
}
:deep(.el-dialog__body){
  padding-top: 0;
}
.info-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  .description {
    width: 100%;
    margin-bottom: 10px;
  }

  .selector-panel {
    display: flex;
    align-items: center;

    .main-view {
      margin-right: 10px;

      .camera-container {
        width: 250px;
      }

      .play-speed-selector {
        width: 50px;
      }

      .selected-tag-content {
        cursor: pointer;
      }
    }

    .btn-content {
      margin-left: 5px;
    }
  }
}

.content {
  height: calc(100vh - 160px);
  width: 100%;
  display: flex;
  flex-wrap: nowrap;

  .left {
    width: 200px;
    margin-right: 15px;
    height: 100%;
    border: 1px solid #eee;
    padding: 2px;

    .top {
      height: calc(100% - 50px);
    }

    .bottom {
      height: 25px;
      margin-top: 5px;

      .tip-container {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;

        .shortcut-key {
          display: flex;
          font-size: 12px;
          align-items: center;
          width: 50%;

          .shortcut-key-button {
            margin-right: 5px;
            padding: 2px 10px;
            background-color: #f0f0f0;
            border: 1px solid #d0d0d0;
            box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
            border-radius: 3px;
            font-family: 'Courier New', Courier, monospace;
            text-align: center;
            user-select: none;
          }

          &:last-child {
            width: 100%;
            margin-top: 5px;
          }
        }
      }
    }

    .is-active {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }

    .duration-container {
      margin: 0 0 10px 0;
      display: flex;
      //flex-direction: column;
    }
  }

  .main-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
  }

  .video-list {
    height: calc(100% - 40px);
    font-size: 0;

    .default-video-row {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      height: 100%;
      width: 100%;
      overflow: hidden;

      .el-image {
        height: 100%;
        width: 100%;
        position: relative;
        background: #000;
        border: 1px solid #fff;

        .image-name {
          position: absolute;
          top: 10px;
          left: 10px;
          font-size: 12px;
          color: #fff;
        }
      }
    }

    .video-row {
      display: flex;
      height: 100%;
      width: 100%;

      .video-container {
        width: 50%;
        height: 100%;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        overflow: hidden;

        .el-image {
          height: 100%;
          width: 100%;
          position: relative;
          background: #000;
          border: 1px solid #fff;
          cursor: pointer;

          .image-name {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 12px;
            color: #fff;
          }
        }
      }
    }

    .no-data {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      color: #b3b0b0;
      font-size: 12px;
      justify-content: center;
    }
  }

  .video-control {
    align-items: center;
    margin-top: 5px;

    .video-slider {
      display: flex;
    }

    .el-slider {
      width: calc(100% - 150px);
      margin-left: 30px;
    }

    .video-play {
      font-size: 24px;
      padding: 0 16px 0 10px;
      display: flex;
      justify-content: center;

      & > div {
        display: flex;
      }

      :deep(.el-icon) {
        cursor: pointer;
        margin: 0 10px;
        font-size: 16px;
      }
    }
  }

  .right {
    //width: 360px;
    height: 100%;
    padding: 2px;
    display: flex;

    .el-divider {
      margin: 15px 0;
      padding: 0 3px;
      font-size: 12px;
    }

    .r-l,
    .r-r {
      border: 1px solid #eee;
      padding: 2px;
      width: 180px;
      height: 100%;
    }

    .r-l {
      .top {
        height: 60%;
        margin-bottom: 10px;
        padding: 3px 0;
      }

      .bottom {
        // 优化高度，直接减去上下间距
        height: calc(40% - 30px);
        padding: 3px 0;
      }
    }

    .r-r {
      border-left: none;
      padding: 5px 2px;

      .tag-button-container {
        display: flex;
        justify-content: center;
      }

      .tag-divider {
        margin: 5px;
      }

      .is-gray {
        background-color: #e0e0e0 !important; /* 灰色背景 */
        color: #a0a0a0 !important; /* 灰色文字 */
        cursor: not-allowed; /* 禁止点击手势 */
        pointer-events: none; /* 禁止交互 */
      }
    }

    :deep(.el-tag) {
      display: flex;
      min-height: 20px;
      height: auto;
      width: 100%;
      margin: 3px 0;
      padding: 3px 0;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .el-tag__content {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center; // 多行文字居中对齐
      }
    }

    .group-name {
      display: flex;
    }
  }
}
</style>
