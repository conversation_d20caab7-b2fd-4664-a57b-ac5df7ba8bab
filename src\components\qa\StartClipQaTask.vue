<template>
  <el-dialog v-model="visible" width="400" @close="dialogClosed" :title="$t('明细规则')" draggable>
    <el-form :model="formData" :rules="formRules" label-width="120px" ref="formRef" style="width: 100%">
      <el-form-item :label="$t('明细数量')" prop="limit">
        <el-input-number
            :min="1"
            :max="200"
            v-model="formData.limit"
            @blur="handleInputNumberBlur"
        ></el-input-number
        >&nbsp;&nbsp;
      </el-form-item>
      <el-form-item :label="$t('标签分类')" prop="tagClassificationId">
        <el-radio-group v-model="formData.tagClassificationId">
          <el-radio v-for="item in tagClassificationList" :value="item.id" :label="item.id">{{item.name}}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogClosed">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="confirm">{{ $t('确认') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>

const defaultForm = {
  limit: 100,
  tagClassificationId: '0c1d4a78e9374a5894ebeffb82cec836'
}
export default {
  name: 'StartClipQaTask',
  props: {},
  emits: ['confirm'],
  data() {
    return {
      formData: Object.assign({}, defaultForm),
      formRules: {
        limit: [{ required: true, message: this.$t('请填写这一批次需要质检的明细数量'), trigger: 'blur' }],
        tagClassificationId: [{ required: true, message: this.$t('请选择质检的标签分类'), trigger: 'change' }]
      },
      visible: false,
      tagClassificationList: [
        {
          accessRoleId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          accessRoleName: '张慧君',
          accessRoleType: 'emp',
          accessRoleTypeName: '个人',
          acquisitionType: 'parking',
          code: 'parking_tag_4',
          id: '0c1d4a78e9374a5894ebeffb82cec836',
          name: 'Parking Tag 补打分组4',
          ownerEmpId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          ownerEmpName: 'Terry.ZHANG',
          tagAmount: 8,
          updatable: true
        },
        {
          accessRoleId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          accessRoleName: '张慧君',
          accessRoleType: 'emp',
          accessRoleTypeName: '个人',
          acquisitionType: 'parking',
          code: 'parking_tag_3',
          id: 'e92c3598e25e4209a19bc3819bb0f232',
          name: 'Parking Tag 补打分组3',
          ownerEmpId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          ownerEmpName: 'Terry.ZHANG',
          tagAmount: 25,
          updatable: true
        },
        {
          accessRoleId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          accessRoleName: '张慧君',
          accessRoleType: 'emp',
          accessRoleTypeName: '个人',
          acquisitionType: 'parking',
          code: 'parking_tag_2',
          id: '18877f7b95a2494b9cfa2127b588d54b',
          name: 'Parking Tag 补打分组2',
          ownerEmpId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          ownerEmpName: 'Terry.ZHANG',
          tagAmount: 19,
          updatable: true
        },
        {
          accessRoleId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          accessRoleName: '张慧君',
          accessRoleType: 'emp',
          accessRoleTypeName: '个人',
          acquisitionType: 'parking',
          code: 'parking_tag_1',
          id: 'd0edbaf52ad64da7826dd9b85da23398',
          name: 'Pakring Tag 补打分组1',
          ownerEmpId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          ownerEmpName: 'Terry.ZHANG',
          tagAmount: 21,
          updatable: true
        }
      ]
    }
  },
  created() {},
  methods: {
    show(row) {
      this.visible = true
    },
    handleInputNumberBlur(field) {
      if (this.formData[field] === null || this.formData[field] === undefined || this.formData[field] === '') {
        this.formData[field] = 0
      }
    },
    dialogClosed() {
      this.$refs.formRef.resetFields()
      this.visible = false
    },
    confirm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        this.visible = false
        this.$emit('confirm',this.formData)
      })
    }
  }
}
</script>
