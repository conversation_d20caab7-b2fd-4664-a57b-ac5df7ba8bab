<template>
  <el-dialog v-model="visible" width="400" @close="dialogClosed" :title="$t('抽检规则')" draggable>
    <el-form :model="formData" :rules="formRules" label-width="120px" ref="formRef" style="width: 100%">
      <el-form-item :label="$t('员工任务比例')" prop="empTaskRate">
        <el-input-number
          :min="1"
          :max="100"
          v-model="formData.empTaskRate"
          @blur="handleInputNumberBlur"
        ></el-input-number
        >&nbsp;&nbsp;%
      </el-form-item>
      <el-form-item :label="$t('任务明细比例')" prop="taskDetailRate">
        <el-input-number
          :min="1"
          :max="100"
          v-model="formData.taskDetailRate"
          @blur="handleInputNumberBlur"
        ></el-input-number
        >&nbsp;&nbsp;%
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogClosed">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="confirm">{{ $t('确认') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { samplingCheckTaskGroup } from '@/apis/qa/qa-tag-task-group'
import { showToast } from '@/plugins/util'

const defaultForm = {
  empTaskRate: 30,
  taskDetailRate: 30
}
export default {
  name: 'SamplingCheck',
  props: {},
  emits: ['confirm'],
  data() {
    return {
      formData: Object.assign({}, defaultForm),
      formRules: {
        empTaskRate: [{ required: true, message: this.$t('请填写员工任务比例'), trigger: 'blur' }],
        taskDetailRate: [{ required: true, message: this.$t('请填写员工任务比例'), trigger: 'blur' }]
      },
      visible: false,
      groupId: ''
    }
  },
  created() {},
  methods: {
    show(row) {
      this.groupId = row.id
      this.visible = true
    },
    handleInputNumberBlur(field) {
      if (this.formData[field] === null || this.formData[field] === undefined || this.formData[field] === '') {
        this.formData[field] = 0
      }
    },
    dialogClosed() {
      this.$refs.formRef.resetFields()
      this.visible = false
    },
    confirm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData=JSON.parse(JSON.stringify(this.formData))
        postData.empTaskRate=postData.empTaskRate/100
        postData.taskDetailRate=postData.taskDetailRate/100
        samplingCheckTaskGroup(this.groupId, postData).then(res => {
          showToast('抽检完成')
          this.$emit('confirm')
          this.visible = false
        })
      })
    }
  }
}
</script>
