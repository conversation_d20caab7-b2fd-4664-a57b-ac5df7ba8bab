import { httpGet } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

// export const queryDataBag = (params = {}, unloading = false) =>
//   httpGet({
//     url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/frame_data_bag/page',
//     params,
//     unloading
//   })
export const queryDataBag = (params = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/frame_data/page/measurement',
    params,
    unloading
  })
export const listDataBagMeasurement = (params = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/frame_data/list/measurement',
    params,
    unloading
  })
export const queryData = (params = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/frame_data/page',
    params,
    unloading
  })
export const listData = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/frame_data/list',
    params
  })
export const listDataBag = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/frame_data_bag/list',
    params
  })
export const dataBagStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/frame_data_bag/statistic',
    params,
    unloading
  })
export const dataStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/frame_data/statistic',
    params,
    unloading
  })
