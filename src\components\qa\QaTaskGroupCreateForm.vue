<template>
  <el-dialog
    v-model="visible"
    width="800px"
    @close="dialogClosed"
    :title="dialogTitle"
    draggable
    class="tag-quality-check-task-dialog"
  >
    <el-form :model="formData" :rules="formRules" label-width="80px" ref="formRef" style="width: 100%">
      <el-form-item :label="$t('数据来源')" prop="dataSource">
        <dictionary-selection
          v-model="formData.dataSource"
          dictionary-type="qa_tag_data_source"
          clearable
          filterable
          @change="handleDataSourceChange"
          :disabled="dialogStatus === 'edit'"
        ></dictionary-selection>
      </el-form-item>
      <el-form-item :label="$t('检查级别')" prop="level">
        <dictionary-selection
          v-model="formData.level"
          dictionary-type="qa_tag_level"
          clearable
          filterable
          :disabled="true"
        ></dictionary-selection>
      </el-form-item>
      <el-form-item :label="$t('数采类型')" prop="acquisitionType">
        <el-radio-group
          v-model="formData.acquisitionType"
          style="margin-right: 20px"
          @change="handleAcquisitionTypeChange"
        >
          <el-radio v-for="item in acquisitionTypeList" :label="item.code" :key="item.code" :id="item.code"
            >{{ $t(item.name) }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="formData.dataSource === 'vin_date'">
        <el-form-item :label="$t('车辆')" prop="vins">
          <bs-vehicle-selection
            :data="vehicleList"
            :auto-load="false"
            :multiple="true"
            modelCode="vin"
            v-model="formData.vins"
            ref="vehicleSelectionRef"
            clearable
            filterable
            @clear="formData.vins = undefined"
          ></bs-vehicle-selection>
        </el-form-item>
      </template>
      <el-form-item :label="$t('挑选类型')" prop="type">
        <dictionary-selection
            v-model="formData.type"
            dictionary-type="qa_tag_task_type"
            clearable
            filterable
        ></dictionary-selection>
      </el-form-item>
      <el-form-item :label="$t('时间')" prop="time" v-if="formData.dataSource !== 'dataset'">
        <el-date-picker
          v-model="formData.time"
          type="datetimerange"
          range-separator="To"
          :start-placeholder="$t('开始时间')"
          :end-placeholder="$t('结束时间')"
          value-format="YYYY-MM-DD HH:mm:ss"
          clearable
          teleported
          placement="top"
        />
      </el-form-item>
      <el-form-item :label="$t('项目')" prop="projectCodes" v-if="formData.dataSource === 'delivery_data'">
        <dictionary-selection
          v-model="formData.projectCodes"
          dictionary-type="delivery_project"
          clearable
          filterable
          multiple
        ></dictionary-selection>
      </el-form-item>
      <template v-if="formData.dataSource === 'dataset'">
        <el-row class="search-container">
          <ltw-input
            v-model="datasetQueryParam.name"
            clearable
            class="selector-container"
            :placeholder="$t('输入数据集名称')"
            @clear="refresh"
            @keyup.enter="refresh"
          ></ltw-input>
          <el-select v-model="datasetQueryParam.projectCodes" :placeholder="$t('请选择项目')" filterable>
            <el-option v-for="(item, index) in projectList" :key="index" :label="item.name" :value="item.code">
              {{ item.name }}
            </el-option>
          </el-select>
          <dictionary-selection
            v-model="datasetQueryParam.status"
            dictionary-type="dataset_status"
            clearable
            filterable
          ></dictionary-selection>
          <el-button @click="refresh" type="primary">
            {{ $t('筛选') }}
          </el-button>
        </el-row>
        <el-form-item :label="$t('数据集')" prop="datasetList" class="table-container">
          <el-table
            :data="pageData.records"
            stripe
            border
            row-key="id"
            ref="tableRef"
            size="small"
            @selection-change="handleSelectionChange"
            class="table-container"
          >
            <el-table-column
              header-align="left"
              align="left"
              type="selection"
              width="55"
              :reserve-selection="true"
            ></el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              v-for="item in tableCols"
              :prop="item.prop"
              :label="$t(`${item.name}`)"
            ></el-table-column>
          </el-table>
        </el-form-item>
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="datasetQueryParam.current"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="datasetQueryParam.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.total"
          small
          style="margin-left: 80px; margin-bottom: 10px"
        >
        </el-pagination>
      </template>
      <el-form-item :label="$t('标签类型')" prop="tagType">
        <el-radio-group v-model="tagType" @change="handleTagTypeChange">
          <el-radio value="tag" label="tag">{{ $t('标签') }}</el-radio>
          <el-radio value="classification" label="classification">{{ $t('标签分类') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t('标签')" class="ltw-filter-tag-container" prop="tagList" v-if="tagType === 'tag'">
        <div>
          <el-button @click="tagDistribute" class="ltw-filter-tag-add">
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
            {{ $t('新增') }}
          </el-button>
          <el-button @click="tagList = []" class="ltw-filter-tag-add">
            <ltw-icon icon-code="el-icon-close"></ltw-icon>
            {{ $t('重置') }}
          </el-button>
        </div>
        <div>
          <el-tag
            v-for="(tag, index) in tagList"
            :key="index"
            :type="checkTagType(tag)"
            closable
            @close="handleClose(tag)"
            style="margin: 3px 5px"
          >
            {{ tag.groupName }}:{{ tag.name }}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item :label="$t('标签分类')" class="ltw-filter-tag-container" prop="tagClassificationList" v-else>
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange"
          >{{ $t('全选') }}
        </el-checkbox>
        <el-checkbox-group v-model="formData.tagClassificationList" @change="handleClassificationSelectionChange">
          <el-checkbox v-for="item in classificationList" :key="item.code" :label="item.code"
            >{{ item.name }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="visible = false">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="confirm">{{ $t('确认') }}</el-button>
      </span>
    </template>
  </el-dialog>
  <bs-tag-group-drawer
    :drawerVisible="tagDistributeDrawerVisible"
    :rowTagList="rowTagList"
    :matchFlag="true"
    @drawerClick="drawerClick"
  ></bs-tag-group-drawer>
</template>
<script>
import { checkTagType, dateUtils } from '@/plugins/util'
import { listBsVehicle } from '@/apis/basic/bs-vehicle'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import BsVehicleSelection from '@/components/basic/BsVehicleSelection.vue'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
import { showToast } from '@/plugins/util'
import SysDictionary from '@/pages/system/SysDictionary.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import LtwIcon from '@/components/base/LtwIcon.vue'
import { saveQaTagTaskGroup, updateQaTagTaskGroup } from '@/apis/qa/qa-tag-task-group'
import { listBsTag } from '@/apis/basic/bs-tag'
import { pageDmDataset } from '@/apis/data/dm-dataset'
import DATA_SET_TYPE from '@/plugins/constants/data-set-type'
import { listDmProject } from '@/apis/dm/dm-project'
import { DATA_SET_STATUS } from '@/plugins/constants/data-dictionary'
import { pageCurrentUserBsTagClassification } from '@/apis/basic/bs-tag-classification'

const defaultFormData = { tagClassificationList: [] }
export default {
  components: {
    LtwIcon,
    DictionarySelection,
    SysDictionary,
    BsVehicleSelection,
    BsTagGroupDrawer
  },
  name: 'QaTaskGroupCreateForm',
  emits: ['confirm'],
  props: {},
  data() {
    return {
      DATA_SET_TYPE,
      checkTagType,
      visible: false,
      acquisitionTypeList: [],
      vehicleList: [],
      rowTagList: [],
      tagDistributeDrawerVisible: false,
      tagList: [],
      tableCols: [
        { name: '名称', prop: 'name' },
        { name: '项目', prop: 'projectCode' }
      ],
      datasetQueryParam: {
        size: 10,
        current: 1,
        type: DATA_SET_TYPE.LABELING,
        simpleQuery: true,
        splitTask: false
      },
      pageData: {
        total: 0
      },
      projectList: [],
      formData: Object.assign({}, defaultFormData),
      formRules: {
        acquisitionType: [{ required: true, message: this.$t('请选择采集类型'), trigger: 'change' }],
        type: [{ required: true, message: this.$t('请选择任务类型（删除/补打）'), trigger: 'change' }],
        time: [{ required: true, message: this.$t('请选择日期'), trigger: 'change' }]
        // tags: [{ required: true, message: this.$t('请输入tag编码（支持多选）'), trigger: 'blur' }],
      },
      dialogTitle: '',
      dialogStatus: '',
      tagType: 'tag',
      selectedDatasetIds: [],
      selectedClassificationIds: [],
      classificationPageData: {
        total: 0
      },
      classificationQueryParam: {
        size: 5,
        current: 1,
        accessRoleTypes: 'emp,org'
      },
      classificationTableCols: [
        { name: '编码', prop: 'code' },
        { name: '名称', prop: 'name' },
        { name: '标签', prop: 'tagAmount' }
      ],
      classificationList: [],
      checkAll: false,
      isIndeterminate: false
    }
  },
  watch: {},
  mounted() {},
  created() {},
  computed: {},
  methods: {
    async show(data) {
      this.checkAll = this.isIndeterminate = false
      this.dialogTitle = data.dialogTitle
      this.dialogStatus = data.dialogStatus
      this.datasetQueryParam.status = DATA_SET_STATUS.DELIVERED
      await this.listAcquisitionType()
      if (!data.formData?.acquisitionType) {
        data.formData.acquisitionType = 'parking'
      }
      this.classificationQueryParam.acquisitionType = data.formData.acquisitionType
      if (data.formData.projectCodeList?.length) {
        data.formData.projectCodes = data.formData.projectCodeList
        delete data.formData.projectCodeList
      }
      if (data.formData.startTime) {
        data.formData.time = [data.formData.startTime, data.formData.endTime]
      }
      if (data.formData.dataSource === 'vin_date') {
        await this.handleFrameFormData(this.dialogStatus, data.formData)
      } else {
        await this.handleClipFormData(this.dialogStatus, data.formData)
      }
      if (data.formData?.tagClassificationList?.length) {
        this.formData.tagClassificationList = data.formData?.tagClassificationList?.map(item => item.id)
        await this.listClassification()
        this.tagType = 'classification'
        this.tagList = []
      } else {
        this.selectedClassificationIds = []
        if (data.formData.tags?.length) {
          this.tagType = 'tag'
          await listBsTag({ codes: data.formData.tags }).then(res => {
            this.tagList = res.data
          })
        }
      }
      this.visible = true
    },
    async handleFrameFormData(status, formData) {
      await this.listVehicle()
      if (status === 'add') {
        this.formData = formData
        this.formData.level = 'frame'
        this.tagType = 'tag'
      } else {
        this.formData.vins = formData.vins?.split(',') || []
        this.formData.acquisitionType = formData.acquisitionType
        this.formData.time = [formData.startTime, formData.endTime]
        this.formData.type = formData.type
        this.formData.id = formData.id
        this.formData.dataSource = formData.dataSource
        this.formData.level = formData.level
        this.formData.tagClassificationList = formData.tagClassificationList || []
      }
    },
    async handleClipFormData(status, formData) {
      this.formData = formData
      if(this.formData.dataSource==='dataset'){
        await this.listProject()
        await this.refresh()
      }
      if (status === 'add') {
        this.formData.level = 'clip'
        this.tagType = 'tag'
      } else {
        this.selectedDatasetIds = this.formData.datasetList?.map(item => item.id) || []
      }
    },
    async handleDataSourceChange(val) {
      this.checkAll = this.isIndeterminate = false
      if (val.value === 'vin_date') {
        this.formData = {
          level: 'frame',
          acquisitionType: 'parking'
        }
        if (this.tagType === 'classification') {
          this.listClassification()
        }
        return
      }
      this.formData = {
        level: 'clip',
        acquisitionType: 'parking'
      }
      if(this.formData.dataSource==='dataset'){
        await this.listProject()
        await this.refresh()
      }
      if (this.tagType === 'classification') {
        this.listClassification()
      }
    },
    handleSelectionChange(val) {
      this.formData.datasetList = val
    },
    listProject() {
      return listDmProject({ asDraft: false, latest: true, acquisitionType: this.formData.acquisitionType }).then(
        res => {
          this.projectList = res.data
          if (this.projectList?.length) {
            this.datasetQueryParam.projectCodes = this.projectList[0].code
          }
        }
      )
    },
    refresh() {
      this.datasetQueryParam.current = 1
      this.queryDataset()
    },
    queryDataset() {
      if (!this.formData.acquisitionType) return showToast('请先选择采集类型', 'warning')
      pageDmDataset(this.datasetQueryParam).then(res => {
        this.pageData = res.data
        this.$nextTick(() => {
          this.updateSelection()
        })
      })
    },
    updateSelection() {
      const table = this.$refs.tableRef
      if (table) {
        this.pageData.records.forEach(row => {
          if (this.selectedDatasetIds?.includes(row.id)) {
            table.toggleRowSelection(row, true)
          }
        })
      }
    },
    handleSizeChange(value) {
      this.datasetQueryParam.current = 1
      this.datasetQueryParam.size = value
      this.queryDataset()
    },
    handleCurrentChange(value) {
      this.datasetQueryParam.current = value
      this.queryDataset()
    },
    async handleTagTypeChange(val) {
      if (val === 'tag') {
        delete this.classificationQueryParam?.name
        delete this.classificationQueryParam?.code
        this.formData.tagClassificationList = this.selectedClassificationIds = []
        this.checkAll = this.isIndeterminate = false
        this.$refs?.claTableRef?.clearSelection()
      } else {
        await this.listClassification()
        this.tagList = []
      }
    },
    listVehicle() {
      if (this.vehicleList?.length) return
      return listBsVehicle().then(res => {
        this.vehicleList = res.data
      })
    },
    listAcquisitionType() {
      if (this.acquisitionTypeList?.length) return
      return listSysDictionary({
        typeCode: 'data_acquisition_type'
      }).then(res => {
        this.acquisitionTypeList = res.data
      })
    },
    async handleAcquisitionTypeChange(val) {
      if (this.formData.dataSource !== 'vin_date') {
        this.checkAll = this.isIndeterminate = false
        delete this.datasetQueryParam.projectCodes
        delete this.formData.projectCodes
      }
      if (this.formData.dataSource === 'dataset') {
        await this.listProject()
        await this.refresh()
      }
      this.classificationQueryParam.acquisitionType = val
      if (this.tagType === 'classification') {
        this.formData.tagClassificationList = this.selectedDatasetIds = []
        this.listClassification()
      }
    },
    tagDistribute() {
      this.tagDistributeDrawerVisible = true
      this.rowTagList = this.tagList || []
    },
    drawerClick(data) {
      this.confirmDistributeTags(data)
    },
    confirmDistributeTags(data) {
      if (data && data.tagList && data.tagList.length) {
        this.tagList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagList(tagGroup)
        })
      }
      this.tagDistributeDrawerVisible = false
    },
    saveCheckedTagList(group) {
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.tagList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      }
    },
    handleClose(tag) {
      this.tagList = this.tagList.filter(item => {
        return item !== tag
      })
      this.rowTagList = this.tagList
    },
    updateCheckAllState() {
      const count = this.formData.tagClassificationList?.length || 0
      this.checkAll = count === this.classificationList.length
      this.isIndeterminate = count > 0 && count < this.classificationList.length
    },
    listClassification() {
      if (this.classificationList?.length) {
       this.updateCheckAllState()
        return
      }
      listSysDictionary({
        typeCode: 'qa_tag_classification'
      }).then(res => {
        this.classificationList = res.data
        this.updateCheckAllState()
      })
    },
    handleCheckAllChange(val) {
      this.isIndeterminate = false
      this.formData.tagClassificationList = val ? this.classificationList.map(item => item.code) : []
    },
    queryClassification() {
      pageCurrentUserBsTagClassification(this.classificationQueryParam).then(res => {
        this.classificationPageData = res.data
        this.$nextTick(() => {
          this.updateClassificationSelection()
        })
      })
    },
    updateClassificationSelection() {
      const table = this.$refs.claTableRef
      if (table) {
        this.classificationPageData.records.forEach(row => {
          if (this.selectedClassificationIds?.includes(row.id)) {
            table.toggleRowSelection(row, true)
          }
        })
      }
    },
    handleClassificationSizeChange(value) {
      this.classificationQueryParam.current = 1
      this.classificationQueryParam.size = value
      this.queryClassification()
    },
    handleClassificationSelectionChange(val) {
      // this.formData.tagClassificationList = val
      let count = val?.length
      this.checkAll = count === this.classificationList?.length
      this.isIndeterminate = count > 0 && count < this.classificationList?.length
    },
    handleClassificationCurrentChange(value) {
      this.classificationQueryParam.current = value
      this.queryClassification()
    },
    confirm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (
          (!this.tagList?.length && this.tagType === 'tag') ||
          (!this.formData?.tagClassificationList?.length && this.tagType === 'classification')
        ) {
          return showToast('请选择标签', 'warning')
        }
        let param = JSON.parse(JSON.stringify(this.formData))
        if (param.tagClassificationList?.length) {
          param.tagClassificationList = param.tagClassificationList?.map(id => ({ id }))
        }
        param.vins = param.vins?.length > 0 ? param.vins?.join(',') : undefined
        param.startTime = param.time && param.time[0]
        param.endTime = param.time && param.time[1]
        if(param.projectCodes?.length){
          param.projectCodes = param.projectCodes?.join(',')
        }
        delete param?.time
        if (this.tagList?.length > 0 && this.tagType === 'tag') {
          param.tags = this.tagList.map(item => item.code)
          param.tags = param.tags.join(',')
        } else {
          param.tags = undefined
          delete param.tagList
        }
        if (this.dialogStatus === 'add') {
          saveQaTagTaskGroup(param).then(() => {
            this.visible = false
            this.$emit('confirm')
          })
        }
        if (this.dialogStatus === 'edit') {
          updateQaTagTaskGroup(param).then(() => {
            this.visible = false
            this.$emit('confirm')
          })
        }
      })
    },
    dialogClosed() {
      this.initFormData()
      this.tagList = []
      this.selectedDatasetIds = []
      this.selectedClassificationIds = []
      this.$refs.tableRef?.clearSelection()
      this.$refs.claTableRef?.clearSelection()
      this.datasetQueryParam = {
        size: 10,
        current: 1,
        type: DATA_SET_TYPE.LABELING,
        simpleQuery: true,
        splitTask: false
      }
      this.classificationQueryParam = {
        size: 5,
        current: 1,
        accessRoleTypes: 'emp,org'
      }
      this.visible = false
    },
    initFormData() {
      this.$refs.formRef.resetFields()
      this.formData = {}
      this.$refs.tableRef?.clearSelection()
      this.$refs.claTableRef?.clearSelection()
    }
  }
}
</script>
<style lang="scss" scoped>
.search-container {
  left: 80px;
  margin-bottom: 10px;

  .el-input {
    width: 200px;
    margin-right: 10px;
  }

  .el-select {
    width: 200px;
    margin-right: 10px;
  }
}

.table-container {
  margin-top: 0;
}
</style>
