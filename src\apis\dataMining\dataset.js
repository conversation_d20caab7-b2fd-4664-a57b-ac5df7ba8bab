import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

/**
 * 获取数据集列表
 */
export function listDatasets(params = {}) {
  // 模拟数据，实际应该调用真实API
  return Promise.resolve({
    data: [
      {
        id: '1',
        name: '高速公路数据集',
        description: '包含高速公路场景的驾驶数据',
        createTime: '2024-01-15 10:30:00',
        size: '2.5GB',
        recordCount: 15000
      },
      {
        id: '2', 
        name: '城市道路数据集',
        description: '城市道路复杂交通场景数据',
        createTime: '2024-01-20 14:20:00',
        size: '3.8GB',
        recordCount: 22000
      },
      {
        id: '3',
        name: '停车场数据集', 
        description: '停车场内低速行驶数据',
        createTime: '2024-01-25 09:15:00',
        size: '1.2GB',
        recordCount: 8500
      },
      {
        id: '4',
        name: '恶劣天气数据集',
        description: '雨雪天气条件下的驾驶数据',
        createTime: '2024-02-01 16:45:00', 
        size: '4.1GB',
        recordCount: 18000
      },
      {
        id: '5',
        name: '夜间驾驶数据集',
        description: '夜间和低光照条件驾驶数据',
        createTime: '2024-02-05 11:30:00',
        size: '2.9GB', 
        recordCount: 12000
      }
    ]
  })
  
  // 真实API调用示例：
  // return request({
  //   url: '/api/datasets',
  //   method: 'get',
  //   params
  // })
}

/**
 * 获取数据集详情
 */
export function getDatasetDetail(id) {
  return httpGet({
    url: `/api/datasets/${id}`,
    method: 'get'
  })
}

/**
 * 创建数据集
 */
export function createDataset(data) {
  return httpPost({
    url: '/api/datasets',
    method: 'post',
    data
  })
}

/**
 * 更新数据集
 */
export function updateDataset(id, data) {
  return httpPut({
    url: `/api/datasets/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除数据集
 */
export function deleteDataset(id) {
  return httpDelete({
    url: `/api/datasets/${id}`,
    method: 'delete'
  })
}
