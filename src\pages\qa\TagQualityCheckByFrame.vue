<template>
  <el-dialog
    v-model="visible"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    fullscreen
    class="tag-quality-check-dialog"
    destroy-on-close
    :ref="infoData.id + 'tagQaRef'"
  >
    <el-row class="info-container">
      <p class="title">{{ $t('tag质检') }}</p>
      <info-data v-model="infoData" class="description"></info-data>
      <div class="selector-panel">
        <div class="main-view">
          <el-radio-group v-model="videoModal" size="small" @change="handleVideoModelChange">
            <el-radio :label="true">{{ $t('均分模式') }}</el-radio>
            <el-radio :label="false">{{ $t('主视图模式') }}</el-radio>
          </el-radio-group>
        </div>
        <div class="main-view">
          <span class="title p-r">{{ $t('摄像头') }}</span>
          <el-select
            v-model="cameraSelected"
            size="small"
            filterable
            multiple
            collapse-tags
            collapse-tags-tooltip
            class="camera-container"
            @visible-change="handleCameraVisibleChange"
          >
            <el-option v-for="item in cameraList" :key="item" :label="item.code" :value="item.code"></el-option>
          </el-select>
        </div>
        <div class="main-view" v-show="!videoModal">
          <span class="title p-r">{{ $t('主视图') }}</span>
          <el-select v-model="mainCamera" @change="changeMainCamera()" size="small" filterable>
            <el-option v-for="item in cameraList" :key="item" :label="item.code" :value="item.code"></el-option>
          </el-select>
        </div>
        <div class="main-view">
          <span class="title p-r">{{ $t('压缩比例') }}</span>
          <el-input-number
            v-model="websocketParam.compressionFactor"
            @change="handleCompressionFactorChanged"
            size="small"
          ></el-input-number>
        </div>
        <div class="main-view">
          <span class="title p-r-15">{{ $t('当前切片总帧数') }}</span>
          <template v-if="videoLoading">-</template>
          <template v-else>{{ Object.keys(currentDataPlayMap).length || 0 }}</template>
        </div>
        <div class="main-view" v-if="checkedItem.status === 'not_start' && status!=='view'">
          <el-button size="small" type="primary" @click="startQualityCheck">{{ $t('开始质检') }}</el-button>
        </div>
        <div class="main-view" v-if="checkedItem.status === 'executing' && !['view','check'].includes(status)">
          <el-button size="small" type="primary" @click="finishQualityCheck">{{ $t('结束质检') }}</el-button>
        </div>
      </div>
    </el-row>
    <div class="content">
      <div class="left">
        <el-scrollbar height="100%">
          <div class="duration-container" v-for="(item, index) in durationList" :key="item.id">
            <el-button
              @click="getPageData(item, index)"
              @dblclick="copy(item)"
              size="small"
              :class="{ 'is-active': item.id === checkedItem.id }"
              style="width: 200px"
            >
              {{ item.startTime }}
              <el-divider direction="vertical" />
              <el-link
                :type="item.status === 'not_start' ? 'primary' : item.status === 'executing' ? 'warning' : 'success'"
                :underline="false"
                >{{ item.statusName }}
              </el-link>
            </el-button>
          </div>
        </el-scrollbar>
      </div>
      <div class="main-content" v-loading="videoLoading">
        <div class="video-list">
          <div :class="videoModal ? 'default-video-row' : 'video-row'" v-if="cameraSelected?.length">
            <template v-if="videoModal">
              <div v-for="camera in cameraSelected" :key="camera">
                <div class="el-image">
                  <div class="image-name" v-text="camera"></div>
                  <canvas :id="camera"></canvas>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="video-container">
                <div class="el-image">
                  <div class="image-name" v-text="mainCamera"></div>
                  <canvas :id="mainCamera"></canvas>
                </div>
              </div>
              <div class="video-container">
                <div v-for="camera in cameraSelected" :key="camera">
                  <div v-if="camera !== mainCamera" class="el-image">
                    <div class="image-name" v-text="camera"></div>
                    <canvas :id="camera"></canvas>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div v-else class="no-data">暂无数据</div>
        </div>
        <div class="video-control">
          <div class="video-slider">
            <el-slider
              size="small"
              v-model="sliderNumber"
              :min="0"
              :max="sliderNumberMax"
              :marks="sliderMarkMap"
              :disabled="JSON.stringify(this.deletedTagMapMeasurementMap) !== '{}'"
              @change="handleSliderDragged()"
              v-if="sliderNumberMax"
            />
            <el-tag
              style="margin: 0 10px"
              v-if="currentDataPlayMap[[measurementIndexMap[sliderNumber]]]?.dataList?.length"
            >
              <span v-if="measurementIndexMap[sliderNumber]" v-text="measurementIndexMap[sliderNumber]"></span>
              <ltw-icon v-else icon-class="is-loading" icon-code="el-icon-loading"></ltw-icon>
            </el-tag>
          </div>
          <div class="video-play">
            <ltw-icon @click="videoTurnLeft()" icon-code="el-icon-arrow-left" class="video-button"></ltw-icon>
            <ltw-icon @click="videoTurnRight()" icon-code="el-icon-arrow-right" class="video-button"></ltw-icon>
          </div>
        </div>
      </div>
      <div class="right">
        <el-scrollbar height="100%">
          <template v-if="currentDataPlayMap[measurementIndexMap[sliderNumber]]?.tags">
            <template v-for="item in currentDataPlayMap[measurementIndexMap[sliderNumber]]?.tags" :key="item.code">
              <template v-if="status !== 'check'">
                <el-tag @dblclick="batchRemoveTags(item)" type="info" v-if="deletedTagMapMeasurementMap[item.tagCode]?.includes(measurementIndexMap[sliderNumber])">
                  <div class="tag-txt-content">
                    {{ item.name }}
                    <ltw-icon icon-code="el-icon-check" v-if="status!=='view' && durationList[currentDurationIndex].status!=='finished'"></ltw-icon>
                  </div>
                </el-tag>
                <el-tag @click="deleteTag(item)" :type="checkType(item)" v-else>
                  <div class="tag-txt-content">
                    {{ item.name }}
                    <ltw-icon icon-code="el-icon-delete" v-if="status!=='view' && durationList[currentDurationIndex].status!=='finished'"></ltw-icon>
                  </div>
                </el-tag>
              </template>
              <template v-else>
                <el-tag :type="checkType(item)">{{ item.name }} </el-tag>
              </template>
            </template>
          </template>
        </el-scrollbar>
      </div>
    </div>
    <invalid-reason ref="invalidReasonRef" @confirm="markConfirm"></invalid-reason>
  </el-dialog>
</template>
<script>
import { sendSock, createWebSocket, closeWebSocket } from '@/plugins/socket/index2'
import util, {
  initWebSocket,
  checkTagType,
  checkType,
  debounce,
  showConfirmToast,
  showToast,
  throttle
} from '@/plugins/util'
import InfoData from '@/components/qa/InfoData.vue'
import { listFtmVehicleModalitySelection } from '@/apis/fleet/ftm-vehicle-modality'
import {
  batchRemoveTag,
  finishQaTagTaskDetail,
  listQaTagTaskDetail,
  startQaTagTaskDetail
} from '@/apis/qa/qa-tag-task-detail'
import LtwIcon from '@/components/base/LtwIcon.vue'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import InvalidReason from '@/pages/sta/dialog/InvalidReason.vue'

const defaultFormData = {}
export default {
  name: 'TagQualityCheckByFrame',
  components: { InvalidReason, LtwIcon, InfoData },
  props: {
    tagQaLevel: {
      type: String,
      default: ''
    }
  },
  emits: ['closed'],
  data() {
    return {
      initWebSocket,
      checkTagType: checkTagType,
      checkType: checkType,
      visible: false,
      infoData: Object.assign({}, defaultFormData),
      cameraList: [],
      cameraSelected: ['RearCam01', 'SurCam01', 'SurCam02', 'SurCam03'],
      mainCamera: 'FrontCam02',
      videoModal: false, //主视图模式
      checkedItem: Object.assign({}, defaultFormData),
      //websocket
      websocketUrl: '/websocket/qa/qa_tag_tasks',
      websocket: null,
      websocketParam: {
        compressionFactor: 3,
        tenantCode: this.$store.state.permission.currentUser.currentTenantCode
      },
      durationList: [],
      videoLoading: false, //loading，
      currentDataPlayMap: Object.assign({}, defaultFormData),
      measurementIndexMap: Object.assign({}, defaultFormData),
      //slider
      totalFragment: 0,
      sliderNumber: 0,
      sliderNumberMax: 0,
      nowDate: '',
      sliderMarkMap: {}, //进度条标记
      deletedTagMapMeasurementMap: {},
      deletedMeasurementMapTagMap: {},
      preMeasurementMap: {},
      curMeasurementMap: {},
      nextMeasurementMap: {},
      currentDurationIndex: 0,
      dataPlayMap: {},
      acceptDataPlayMap: {},
      acceptDurationIndex: 0,
      directLoading: true,
      status: '',
      currentRow: {}
    }
  },
  watch: {},
  computed: {
    validateEvent() {
      // 返回一个函数，用于动态判断是否禁止拖动
      return (event, value) => {
        return JSON.stringify(this.deletedTagMapMeasurementMap) !== '{}' ? false : true
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    dialogOpened() {
      this.$nextTick(() => {
        let _this = this
        window.addEventListener('resize', debounce(_this.resizeCanvas, 300))
        window.addEventListener('keydown', _this.installKeyList)
      })
    },
    async show(data, status) {
      this.visible = true
      this.infoData = data
      this.infoData.tagCodeList = this.infoData.tags?.split(',') || []
      if (!this.cameraList?.length) {
        await this.listCamera()
      }
      this.status = status
      if (!this.videoModal) {
        let index = this.cameraSelected.findIndex(item => item.code === this.mainCamera)
        if (index < 0) {
          this.websocketParam.modalities = [...this.cameraSelected, this.mainCamera].join(',')
        } else {
          this.websocketParam.modalities = [...this.cameraSelected].join(',')
        }
      } else {
        this.websocketParam.modalities = [...this.cameraSelected].join(',')
      }
      await this.listDuration()
      initWebSocket(this.websocketUrl)
    },
    listCamera() {
      listFtmVehicleModalitySelection({ sensorType: 'camera' }).then(res => {
        this.cameraList = res.data.filter(item => item.code !== this.mainCamera)
      })
    },
    listDuration() {
      listQaTagTaskDetail({ taskCode: this.infoData.code }).then(res => {
        this.durationList = res.data
        if (!this.durationList?.length) {
          showToast('该任务暂无明细', 'warning')
          return
        }
        this.checkedItem = this.durationList[0]
        this.currentDurationIndex = 0
        this.dataPlayMap = {}
        if (this.durationList[0].status !== 'not_start' || this.status==='view') {
          this.getPageData(this.checkedItem, this.currentDurationIndex)
        }
      })
      //
    },
    getCurrentDetailTaskData() {
      this.websocketParam.detailId = this.checkedItem.id
      this.getPlayResult(this.websocketParam, true, this.currentDurationIndex)
    },
    deletePreDurationPlayData() {
      if (this.currentDurationIndex > 0) {
        delete this.dataPlayMap[this.durationList[this.currentDurationIndex - 1].id]
      }
    },
    startQualityCheck() {
      startQaTagTaskDetail(this.checkedItem.id).then(res => {
        let index = this.durationList.findIndex(item => item.id === this.checkedItem.id)
        this.durationList[index].status = 'executing'
        this.durationList[index].statusName = '执行中'
        if (!this.dataPlayMap[this.checkedItem.id]) {
          this.dataPlayMap = {}
          this.directLoading = true
          this.getCurrentDetailTaskData()
          return
        }
        this.deletePreDurationPlayData()
        let currentDataMap = this.dataPlayMap[this.checkedItem.id]
        if (currentDataMap.acceptAmountStatus == 'all') {
          this.loadingFinish(false, this.currentDurationIndex)
          return
        }
        this.directLoading = true
        this.videoLoading = true
      })
    },
    finishQualityCheck() {
      if (JSON.stringify(this.deletedTagMapMeasurementMap) !== '{}') {
        return showToast('仍有预删除的标签', 'warning')
      }
      this.$refs.invalidReasonRef.show(this.checkedItem)
    },
    markConfirm() {
      finishQaTagTaskDetail(this.checkedItem.id).then(res => {
        let index = this.durationList.findIndex(item => item.id === this.checkedItem.id)
        this.durationList[index].status = 'finished'
        this.durationList[index].statusName = '完成'
        this.infoData.finishCount += 1
      })
    },
    getPlayResult(param, loading, durationIndex) {
      this.acceptDurationIndex = durationIndex
      //console.log('开始发送信息', { ...param })
      this.videoLoading = loading
      sendSock(
        param,
        res => {
          //console.log('接受到的数据：', res.data)
          if (res.data.detailId === this.durationList[durationIndex].id) {
            this.parsePlayData(res.data, durationIndex, this.loadingFinish)
          }else{
            this.videoLoading = false
          }
        },
        err => {
          this.videoLoading = false
        }
      )
    },
    loadingFinish(resetDataPlay, durationIndex, parseAcceptData, total) {
      this.videoLoading = false
      if (resetDataPlay) {
        this.dataPlayMap[this.durationList[durationIndex].id] = {
          acceptAmountStatus: 'all',
          playData: parseAcceptData,
          total: total
        }
        if (durationIndex < this.durationList?.length - 1 && Object.keys(this.dataPlayMap).length < 5) {
          this.acceptDurationIndex = durationIndex + 1
          this.websocketParam.detailId = this.durationList[this.acceptDurationIndex].id
          this.getPlayResult(this.websocketParam, false, durationIndex + 1)
        }
        if (this.directLoading) {
          this.directLoading = false
          this.playVideo()
        }
        return
      }
      if (this.currentDurationIndex == durationIndex) {
        this.playVideo()
        let flag =
          this.acceptDurationIndex < this.durationList?.length - 1 &&
          Object.keys(this.dataPlayMap).length < 5 &&
          !this.dataPlayMap[this.durationList[this.acceptDurationIndex + 1].id] &&
          this.dataPlayMap[this.durationList[this.acceptDurationIndex].id]?.acceptAmountStatus == 'all'
        if (flag) {
          this.acceptDurationIndex = this.acceptDurationIndex + 1
          this.websocketParam.detailId = this.durationList[this.acceptDurationIndex].id
          this.getPlayResult(this.websocketParam, false, this.acceptDurationIndex)
        }
      }
    },
    playVideo() {
      let currentDataMap = this.dataPlayMap[this.checkedItem.id]
      this.currentDataPlayMap = currentDataMap.playData
      this.sliderNumberMax =
        Object.keys(currentDataMap.playData)?.length > 0 ? Object.keys(currentDataMap.playData)?.length - 1 : 0
      for (let index in Object.keys(this.currentDataPlayMap)) {
        this.measurementIndexMap[index] = Object.keys(this.currentDataPlayMap)[index]
      }
      this.resizeCanvas(true)
    },
    parseAcceptData(acceptData) {
      const tags = [],
        tagMap = {}
      for (let item of acceptData.measurementDataList) {
        if(item.tags?.length){
          for (let tag of item.tags) {
            if (this.infoData.tagCodeList.includes(tag.tagCode) && !tagMap[tag.tagCode]) {
              tags.push(tag)
              tagMap[tag.tagCode] = tag
            }
          }
        }
        this.dataPlayMap[acceptData.detailId].playData[item.measurement] = {
          dataList: item.dataList,
          tagMap: JSON.parse(JSON.stringify(tagMap)),
          tags: tags
        }
      }
    },
    parsePlayData(acceptData, durationIndex, callback) {
      let tagMap = {}
      let tags = []
      if (!this.dataPlayMap[acceptData.detailId]) {
        this.dataPlayMap[acceptData.detailId] = {}
      }
      if (!this.dataPlayMap[acceptData.detailId].playData) {
        this.dataPlayMap[acceptData.detailId].playData = {}
      }
      let acceptPlayDataAmount = []
      if (acceptData.total !== 0) {
        this.parseAcceptData(acceptData)
        acceptPlayDataAmount = Object.values(this.dataPlayMap[acceptData.detailId].playData).flat(Infinity)
      }
      if (acceptPlayDataAmount?.length === acceptData.total) {
        this.videoLoading = false
        callback(true, durationIndex, this.dataPlayMap[acceptData.detailId].playData, acceptData.total)
      }
    },
    initDataMap() {
      this.preMeasurementMap = {}
      this.curMeasurementMap = {}
    },
    installKeyList(e) {
      let _this = this
      switch (e.key) {
        case 'a' || 'A':
          _this.videoTurnLeft()
          break
        case 'd' || 'D':
          _this.videoTurnRight()
          break
      }
    },
    copy(data) {
      let transfer = document.createElement('input')
      document.body.appendChild(transfer)
      transfer.value = data.startTime
      transfer.focus()
      transfer.select()
      if (document.execCommand('copy')) {
        document.execCommand('copy')
      }
      transfer.blur()
      showToast('复制成功', 'success')
      document.body.removeChild(transfer)
    },
    getPageData(data, index) {
      this.videoLoading = false
      this.checkedItem = data
      this.currentDurationIndex = index
      this.initDataMap()
      this.resetSlider()
      this.deletedTagMapMeasurementMap = {}
      this.deletedMeasurementMapTagMap = {}
      if (this.checkedItem.status !== 'not_start' || this.status==='view') {
        if (!this.dataPlayMap[this.checkedItem.id]) {
          this.dataPlayMap = {}
          this.directLoading = true
          this.getCurrentDetailTaskData()
          this.resizeCanvas(true)
          return
        }
        this.deletePreDurationPlayData()
        let currentDataMap = this.dataPlayMap[this.checkedItem.id]
        if (currentDataMap.acceptAmountStatus == 'all') {
          this.loadingFinish(false, this.currentDurationIndex)
          return
        }
        this.directLoading = true
        this.videoLoading = true
      }
    },
    deleteValuesOfMap(object, prop) {
      return Object.fromEntries(
        Object.entries(object).map(([key, values]) => [key, values.filter(value => value !== prop)])
      )
    },
    deleteTag(item) {
      if (this.checkedItem.status === 'finished' || this.status==='view') return
      let currentMeasurement = this.measurementIndexMap[this.sliderNumber]
      this.curMeasurementMap[item.tagCode] = currentMeasurement
      if (this.deletedTagMapMeasurementMap[item.tagCode]?.length) {
        this.deletedTagMapMeasurementMap[item.tagCode].push(currentMeasurement)
      } else {
        this.deletedTagMapMeasurementMap[item.tagCode] = [currentMeasurement]
      }
      if (this.deletedMeasurementMapTagMap[currentMeasurement]?.length) {
        this.deletedMeasurementMapTagMap[currentMeasurement].push(item.tagCode)
      } else {
        this.deletedMeasurementMapTagMap[currentMeasurement] = [item.tagCode]
      }
    },
    batchRemoveTags(item) {
      if (this.checkedItem.status === 'finished' || this.status==='view') return
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        let measurementList = this.deletedTagMapMeasurementMap[item.tagCode]
        if (!measurementList?.length) {
          return showToast('无预删除的标签', 'warning')
        }
        let param = {
          vin: this.infoData.vin,
          tagCode: item.tagCode,
          measurementList: measurementList,
          operation: 'delete'
        }
        batchRemoveTag(this.checkedItem.id, param).then(res => {
          for(let time of measurementList) {
            delete this.currentDataPlayMap[time].tagMap[item.tagCode]
            this.currentDataPlayMap[time].tags = Object.values(this.currentDataPlayMap[time].tagMap).flat(Infinity)
          }
          delete this.deletedTagMapMeasurementMap[item.tagCode]
          this.deletedMeasurementMapTagMap = this.deleteValuesOfMap(this.deletedMeasurementMapTagMap, item.tagCode)
          delete this.preMeasurementMap[item.tagCode]
          delete this.curMeasurementMap[item.tagCode]
        })
      })
    },
    resizeCanvas(cameraChange) {
      let videoNode = document.querySelector('.video-list')
      let canvasList = document.querySelectorAll('.el-image canvas')
      for (let i = 0, iLen = canvasList?.length; i < iLen; i++) {
        if (this.videoModal) {
          canvasList[i].height = this.getVideoHeight()
          canvasList[i].width = this.getDefaultVideoWidth()
        } else {
          if (canvasList[i].id === this.mainCamera) {
            canvasList[i].height = videoNode.offsetHeight - 2
            canvasList[i].width = videoNode.offsetWidth / 2 - 2
          } else {
            canvasList[i].height = this.getVideoHeight()
            canvasList[i].width = this.getVideoWidth()
          }
        }
      }
      if (cameraChange) {
        this.loadImage()
      }
    },
    loadImage() {
      this.projectionLoading = false
      let fragment = this.currentDataPlayMap[this.measurementIndexMap[this.sliderNumber]]
      if (fragment?.dataList?.length) {
        let imageList = fragment?.dataList
        let imageMap = {}
        imageList.forEach(item => {
          imageMap[item.modality] = item
        })
        let canvasList = document.querySelectorAll('.el-image canvas')
        for (let i = 0, iLen = canvasList?.length; i < iLen; i++) {
          let context = canvasList[i].getContext('2d')
          let img = new Image()
          if (imageMap[canvasList[i].id]?.content) {
            img.src = 'data:image/jpeg;base64,' + imageMap[canvasList[i].id]?.content
            this.initImg(context, img, canvasList[i])
          } else {
            context.clearRect(0, 0, canvasList[i].width, canvasList[i].height)
          }
        }
      } else {
        let canvasList = document.querySelectorAll('.el-image canvas')
        for (let i = 0, iLen = canvasList?.length; i < iLen; i++) {
          let context = canvasList[i].getContext('2d')
          let img = new Image()
          context.clearRect(0, 0, canvasList[i].width, canvasList[i].height)
        }
      }
    },
    initImg(context, img, canvasDom) {
      img.onload = () => {
        context.clearRect(0, 0, canvasDom.width, canvasDom.height)
        let canvasHeight, canvasWidth
        if (canvasDom.width / canvasDom.height < img.width / img.height) {
          canvasHeight = (canvasDom.width / img.width) * img.height
          canvasWidth = canvasDom.width
          this.drawImg(context, img, 0, (canvasDom.height - canvasHeight) / 2, canvasWidth, canvasHeight)
        } else {
          canvasWidth = (canvasDom.height / img.height) * img.width
          canvasHeight = canvasDom.height
          this.drawImg(context, img, (canvasDom.width - canvasWidth) / 2, 0, canvasWidth, canvasHeight)
        }
      }
      img.onerror = () => {
        context.clearRect(0, 0, canvasDom.width, canvasDom.height)
      }
    },
    drawImg(context, img, dx, dy, width, height) {
      context.drawImage(img, dx, dy, width, height)
    },
    handleVideoModelChange(val) {
      this.mainCamera = val ? '' : 'FrontCam02'
      this.$nextTick(() => {
        setTimeout(() => {
          this.resizeCanvas(true)
        }, 100) // 延迟执行：确保 DOM 渲染完成
      })
    },
    handleCameraVisibleChange(value) {
      if (value) {
        this.lastCameraSelected = this.cameraSelected
        return
      }
      let flag = false
      for (let camera of this.cameraSelected) {
        if (!this.lastCameraSelected?.length) {
          this.handleCameraSelectedChanged()
          return
        }
        for (let lastCamera of this.lastCameraSelected) {
          if (camera !== lastCamera) {
            this.handleCameraSelectedChanged()
            return
          }
        }
      }
    },
    handleSliderDragged: throttle(function () {
      if (JSON.stringify(this.deletedTagMapMeasurementMap) !== '{}') {
        showToast('存在预删除的标签，不支持拖拽', 'warning')
        return
      }
      let fragment = this.currentDataPlayMap[this.measurementIndexMap[this.sliderNumber]]
      if (fragment) {
        this.loadImage()
        return
      }
    }, 300),
    resetSlider() {
      this.sliderNumber = 0
      this.sliderNumberMax = 0
      this.sliderMarkMap = {}
      this.currentDataPlayMap = {}
      this.totalFragment = 0
    },
    handleCameraSelectedChanged() {
      if (!this.cameraSelected?.length) {
        return
      }
      this.initDataMap()
      this.resetSlider()
      if (!this.videoModal) {
        let index = this.cameraSelected.findIndex(item => item.code === this.mainCamera)
        if (index < 0) {
          this.websocketParam.modalities = [...this.cameraSelected, this.mainCamera].join(',')
        } else {
          this.websocketParam.modalities = [...this.cameraSelected].join(',')
        }
      } else {
        this.websocketParam.modalities = [...this.cameraSelected].join(',')
      }
      this.dataPlayMap = {}
      // await this.getPlayResult(this.websocketParam, true, this.currentDurationIndex)
      this.getPageData(this.durationList[this.currentDurationIndex],this.currentDurationIndex)
    },
    changeMainCamera(val) {
      this.$nextTick(() => {
        this.resizeCanvas(true)
      })
    },
    handleCompressionFactorChanged() {
      this.initDataMap()
      this.resetSlider()
      this.videoLoading = false
      if (this.checkedItem.status !== 'not_start') {
        this.dataPlayMap = {}
        this.getPlayResult(this.websocketParam, true, this.currentDurationIndex)
      }
    },
    getVideoWidth() {
      let videoListWidth = document.querySelector('.video-list').offsetWidth / 2 - 10
      let cameraSelected = this.cameraSelected.filter(item => item !== this.mainCamera)
      let videoLength = cameraSelected.length
      if (videoLength) {
        if(videoLength===1){
          return videoListWidth
        } else {
          return Math.floor((videoListWidth) / 2) - 2
        }
      } else {
        return videoListWidth
      }
    },
    getDefaultVideoWidth() {
      let videoListWidth = document.querySelector('.video-list').offsetWidth - 1
      let videoLength = this.cameraSelected?.length
      if (videoLength) {
        if (videoLength === 1) {
          return videoListWidth - 2
        } else if (videoLength >= 2 && videoLength <= 4) {
          return Math.floor(videoListWidth / 2) - 2
        } else if (videoLength >= 5 && videoLength <= 9) {
          return Math.floor(videoListWidth / 3) - 2
        } else {
          return Math.floor(videoListWidth / 4) - 2
        }
      } else {
        return videoListWidth
      }
    },
    getVideoHeight() {
      let videoNode = document.querySelector('.video-list')
      let cameraSelected = []
      cameraSelected = this.cameraSelected.filter(item => item.code !== this.mainCamera)
      let imageListLength = this.videoModal ? this.cameraSelected?.length : cameraSelected?.length
      if (videoNode) {
        let videoHeight = videoNode.offsetHeight - 1
        if (this.videoModal) {
          if (imageListLength >= 1 && imageListLength <= 2) {
            return videoHeight - 2
          } else if (imageListLength >= 3 && imageListLength <= 6) {
            return videoHeight / 2 - 2
          } else if (imageListLength >= 7 && imageListLength <= 9) {
            return videoHeight / 3 - 2
          } else if (imageListLength >= 10 && imageListLength <= 16) {
            return videoHeight / 4 - 2
          } else if (imageListLength >= 17 && imageListLength <= 20) {
            return videoHeight / 5 - 2
          } else {
            return videoHeight / Math.ceil(imageListLength / 4) - 5
          }
        } else {
          if (imageListLength >= 1 && imageListLength <= 2) {
            return videoHeight - 2
          } else if (imageListLength >= 3 && imageListLength <= 4) {
            return videoHeight / 2 - 2
          } else if (imageListLength >= 5 && imageListLength <= 6) {
            return videoHeight / 3 - 2
          } else if (imageListLength >= 7 && imageListLength <= 8) {
            return videoHeight / 4 - 2
          } else if (imageListLength >= 9 && imageListLength <= 10) {
            return videoHeight / 5 - 2
          } else {
            return videoHeight / Math.ceil(imageListLength / 2) - 2
          }
        }
      }
    },
    popLastValue(object) {
      return Object.fromEntries(Object.entries(object)?.map(([key, values]) => [key, values.slice(0, -1)]))
    },
    transformedObject(object) {
      return Object.fromEntries(
        Object.entries(object)?.flatMap(([key, values]) => Array.from(values).map(value => [value, key] || []))
      )
    },
    videoTurnLeft() {
      if (this.sliderNumberMax && this.sliderNumber >= 0) {
        if (this.sliderNumber === 0) {
          this.initDataMap()
          this.deletedTagMapMeasurementMap = {}
          this.deletedMeasurementMapTagMap = {}
          this.loadImage()
          return
        }
        if (JSON.stringify(this.deletedTagMapMeasurementMap) == '{}') {
          this.initDataMap()
          this.deletedMeasurementMapTagMap = {}
          this.sliderNumber--
          this.loadImage()
          return
        }
        this.sliderNumber--
        this.loadImage()
        this.curMeasurementMap = JSON.parse(JSON.stringify(this.preMeasurementMap))
        this.deletedTagMapMeasurementMap = this.popLastValue(this.deletedTagMapMeasurementMap)
        delete this.deletedMeasurementMapTagMap[this.measurementIndexMap[this.sliderNumber]]
        let preDeleteObj = this.deletedMeasurementMapTagMap[this.measurementIndexMap[this.sliderNumber - 1]]
        if (preDeleteObj?.length) {
          let obj = {}
          obj[this.measurementIndexMap[this.sliderNumber]] = preDeleteObj
          this.preMeasurementMap = this.transformedObject(obj)
        } else {
          this.preMeasurementMap = {}
        }
      }
    },
    videoTurnRight() {
      if (this.sliderNumberMax && this.sliderNumber < this.sliderNumberMax) {
        this.sliderNumber++
        this.loadImage()
        this.preMeasurementMap = JSON.parse(JSON.stringify(this.curMeasurementMap))
        let curMeasurement = this.measurementIndexMap[this.sliderNumber]
        let curFragment = this.currentDataPlayMap[curMeasurement]
        this.deletedMeasurementMapTagMap[curMeasurement] = []
        Object.keys(this.preMeasurementMap).forEach(item => {
          if (curFragment.tagMap[item]) {
            this.curMeasurementMap[item] = curMeasurement
            this.deletedTagMapMeasurementMap[item].push(curMeasurement)
            this.deletedMeasurementMapTagMap[curMeasurement].push(item)
          }
        })
      }
    },
    initProps() {
      this.sliderNumber = 0
      this.sliderNumberMax = 0
      this.currentDataPlayMap = {}
      this.cameraSelected = ['RearCam01', 'SurCam01', 'SurCam02', 'SurCam03']
      this.lastCameraSelected = []
      this.mainCamera = 'FrontCam02'
      this.totalFragment = 0
      this.deletedTagMapMeasurementMap = {}
      this.preMeasurementMap = {}
      this.curMeasurementMap = {}
      this.nextMeasurementMap = {}
      closeWebSocket()
    },
    dialogClosed() {
      this.visible = false
      let _this = this
      window.removeEventListener('keydown', _this.installKeyList)
      window.removeEventListener('resize', debounce(_this.resizeCanvas, 300))
      this.initProps()
      this.$emit('closed')
    }
  }
}
</script>
<style lang="scss" scoped>
.p-r-15 {
  padding-right: 15px;
}

.info-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  .title {
    margin: 0 auto 10px;
    font-size: 12px;
  }

  .description {
    width: 100%;
    margin-bottom: 10px;
  }

  .selector-panel {
    display: flex;

    .main-view {
      margin-right: 10px;

      .camera-container {
        width: 250px;
      }

      .play-speed-selector {
        width: 50px;
      }

      .selected-tag-content {
        cursor: pointer;
      }
    }
  }
}

.content {
  height: calc(100vh - 160px);
  width: 100%;
  display: flex;
  flex-wrap: nowrap;

  .left {
    width: 200px;
    margin-right: 15px;
    height: 100%;
    border: 1px solid #eee;
    padding: 2px;

    .is-active {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }

    .duration-container {
      margin: 0 0 10px 0;
      display: flex;
      //flex-direction: column;
    }
  }

  .main-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
  }

  .video-list {
    // padding: 10px;
    // flex-grow: 1;
    height: calc(100% - 40px);
    font-size: 0;

    .default-video-row {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      height: 100%;
      width: 100%;
      overflow: hidden;

      .el-image {
        height: 100%;
        width: 100%;
        position: relative;
        background: #000;
        border: 1px solid #fff;

        .image-name {
          position: absolute;
          top: 10px;
          left: 10px;
          font-size: 12px;
          color: #fff;
        }
      }
    }

    .video-row {
      display: flex;
      height: 100%;
      width: 100%;

      .video-container {
        width: 50%;
        height: 100%;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        overflow: hidden;

        .el-image {
          height: 100%;
          width: 100%;
          position: relative;
          background: #000;
          border: 1px solid #fff;

          .image-name {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 12px;
            color: #fff;
          }
        }
      }
    }

    .no-data {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      color: #b3b0b0;
      font-size: 12px;
      justify-content: center;
    }
  }

  .video-control {
    align-items: center;
    margin-top: 5px;

    .video-slider {
      display: flex;
    }

    .el-slider {
      width: calc(100% - 150px);
      margin-left: 30px;
    }

    .video-play {
      font-size: 24px;
      padding: 0 16px 0 10px;
      display: flex;
      justify-content: center;

      & > div {
        display: flex;
      }

      :deep(.el-icon) {
        cursor: pointer;
        margin: 0 10px;
        font-size: 16px;
      }
    }
  }

  .right {
    width: 250px;
    height: 100%;
    border: 1px solid #eee;
    padding: 2px;

    .el-tag {
      margin: 3px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .tag-txt-content {
        display: flex;
        height: 30px;
        align-items: center;
      }
    }
  }
}
</style>