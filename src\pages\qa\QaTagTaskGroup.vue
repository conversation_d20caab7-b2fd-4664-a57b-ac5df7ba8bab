<template>
  <div class="ltw-page-container">
    <el-card>
      <el-tabs v-model="queryParam.dataSource" @tab-click="handleTabClick">
        <el-tab-pane
          v-for="item in dataSourceList"
          :key="item.code"
          :label="item.name"
          :name="item.code"
          :id="item.name"
        ></el-tab-pane>
      </el-tabs>
      <div class="ltw-toolbar">
        <qa-tag-task-group-filter class="filter-panel" @filter="filter" ref="filterRef"></qa-tag-task-group-filter>
        <el-button
          :type="item.buttonStyleType"
          :key="item.id"
          v-for="item in outlineFunctionList"
          @click="executeButtonMethod(item)"
        >
          <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
          {{ $t(item.name) }}
        </el-button>
      </div>
      <el-table
        :data="pageData.records"
        stripe
        :row-key="row => row.id"
        :expand-row-keys="expandedRows"
        @expand-change="handleExpandChange"
        ref="tableRef"
      >
        <el-table-column type="expand">
          <template #default="props">
            <div class="task_content">
              <qa-task-table
                ref="taskTableRef"
                :id="props.row.id"
                :data-operate-permission="dataOperatePermission"
                :group-code="props.row.code"
                :data-source="props.row.dataSource"
                :tag-qa-level="props.row.level"
                @closed="closed"
              ></qa-task-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="code"
          :label="$t('编码')"
          width="150"
          show-tooltip-when-overflow
        >
        </el-table-column>
        <el-table-column header-align="left" align="left" prop="levelName" :label="$t('质检级别')"></el-table-column>
        <template v-if="queryParam.dataSource === 'vin_date'">
          <el-table-column
            header-align="left"
            align="left"
            prop="vins"
            :label="$t('车辆')"
            width="150"
            show-tooltip-when-overflow
          >
          </el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            prop="typeName"
            :label="$t('质检类型')"
            width="100"
            show-overflow-tooltip
          >
            <template #default="scope">
              <el-tag :type="scope.row.type === 'check' ? 'success' : 'warning'">{{ scope.row.typeName }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column header-align="center" align="center" :label="$t('时间')" width="350">
            <template #default="scope">
              <template v-if="scope.row.startTime">
                <el-tag>{{ scope.row.startTime }}</el-tag>
                <span>&nbsp;&nbsp;<el-link>-</el-link>&nbsp;&nbsp;</span>
                <el-tag>{{ scope.row.endTime }}</el-tag>
              </template>
            </template>
          </el-table-column>
        </template>
        <el-table-column
          header-align="left"
          align="left"
          prop="datasetNames"
          :label="$t('数据集数量')"
          v-if="queryParam.dataSource === 'dataset'"
        >
          <template #default="scope">
            {{ numUtils.numFormat(scope.row.datasetCount) }}
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          :label="$t('时间')"
          v-if="queryParam.dataSource === 'delivery_data'"
          width="330"
        >
          <template #default="scope">
            <el-tag>{{ scope.row.startTime }}</el-tag>
            -
            <el-tag>{{ scope.row.endTime }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="acquisitionType"
          :label="$t('采集类型')"
          width="120"
          show-tooltip-when-overflow
        >
        </el-table-column>
        <el-table-column header-align="center" align="center" prop="duration" :label="$t('总时长')" width="100">
          <template #default="scope">
            <template v-if="scope.row.duration">
              <el-tag>{{ formatDuration(scope.row.duration) }}</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" prop="measurementTime" :label="$t('标签')" width="80">
          <template #default="scope">
            <el-popover placement="bottom" trigger="click" :width="400">
              <el-tag :type="checkTagType(item)" v-for="item in scope.row.tagList" :key="item.code" style="margin: 3px"
                >{{ item.name }}
              </el-tag>
              <template #reference>
                <el-link type="primary" :underline="false">
                  {{ scope.row.tagList?.length }}
                </el-link>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="deleteCount"
          :label="$t('删除标签量')"
          width="120"
        ></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="addCount"
          :label="$t('新增标签量')"
          width="120"
        ></el-table-column>
        <el-table-column header-align="center" align="center" :label="$t('任务进度')" width="100">
          <template #default="scope">
            <span class="finish_count">{{ scope.row.finishCount }}</span> /
            <span class="task_count">{{ scope.row.taskCount }}</span>
          </template>
        </el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="empName"
          :label="$t('创建人')"
          width="100"
        ></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="samplingCheckEmpName"
          :label="$t('抽检人')"
          width="100"
        ></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="statusName"
          :label="$t('状态')"
          width="120"
          fixed="right"
        >
          <template #default="scope">
            <el-tag
              :type="
                scope.row.status === QA_TAG_TASK_STATUS.FINISHED
                  ? 'success'
                  : scope.row.status === QA_TAG_TASK_STATUS.EXECUTING
                  ? 'warning'
                  : ''
              "
            >
              {{ scope.row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('操作')" min-width="200" fixed="right">
          <template #default="scope">
            <el-button-group>
              <template v-if="Object.keys(dataOperatePermission)?.length">
                <template v-for="item in Object.keys(dataOperatePermission)" :key="item">
                  <el-tooltip
                    effect="dark"
                    :content="$t(dataOperatePermission[item].name)"
                    placement="top"
                    :enterable="false"
                  >
                    <el-button
                      :type="dataOperatePermission[item].buttonStyleType"
                      @click="executeButtonMethod(dataOperatePermission[item], scope.row)"
                      size="small"
                      v-if="
                        Object.values(QA_TASK_GROUP_INLINE_FUNC).includes(item) &&
                        dataOperatePermission[item] &&
                        checkPermission(scope.row, item)
                      "
                    >
                      <ltw-icon :icon-code="dataOperatePermission[item].buttonIconCode"></ltw-icon>
                    </el-button>
                  </el-tooltip>
                </template>
              </template>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        v-model:current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        v-model:page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
      <qa-task-group-create-form ref="formRef" @confirm="refresh"></qa-task-group-create-form>
      <split-task ref="splitRef" @confirm="confirm"></split-task>
      <sampling-check ref="samplingCheckRef" @confirm="refresh"></sampling-check>
    </el-card>
  </div>
</template>

<script>
import {
  deleteQaTagTaskGroup,
  pageQaTagTaskGroup,
  getQaTagTaskGroup,
  splitTaskGroup,
  reCache
} from '@/apis/qa/qa-tag-task-group'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast, checkTagType, numUtils } from '@/plugins/util'
import QaTaskGroupCreateForm from '@/components/qa/QaTaskGroupCreateForm.vue'
import QaTaskTable from '@/components/qa/QaTaskTable.vue'
import SplitTask from '@/components/qa/SplitTask.vue'
import { QA_TAG_TASK_STATUS, QA_TASK_GROUP_INLINE_FUNC } from '@/plugins/constants/data-dictionary'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import SamplingCheck from '@/components/qa/SamplingCheck.vue'
import QaTagTaskGroupFilter from '@/components/filter/business/QaTagTaskGroupFilter.vue'

const defaultFormData = {}
export default {
  name: 'QaTagTaskGroup',
  components: {
    QaTagTaskGroupFilter,
    SamplingCheck,
    SplitTask,
    QaTaskTable,
    QaTaskGroupCreateForm
  },
  data() {
    return {
      checkTagType,
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      expandedRows: [],
      taskList: [],
      formData: Object.assign({}, defaultFormData),
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentButton: {},
      currentRow: {},
      dataSourceList: [],
      selectedDatasetIds: []
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.listDataSource()
  },
  computed: {
    numUtils() {
      return numUtils
    },
    QA_TASK_GROUP_INLINE_FUNC() {
      return QA_TASK_GROUP_INLINE_FUNC
    },
    QA_TAG_TASK_STATUS() {
      return QA_TAG_TASK_STATUS
    },
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    listDataSource() {
      listSysDictionary({
        typeCode: 'qa_tag_data_source'
      }).then(res => {
        this.dataSourceList = res.data
        this.queryParam.dataSource = res.data[0].code
        this.query()
      })
    },
    checkPermission(row, func) {
      if (func === QA_TASK_GROUP_INLINE_FUNC.SAMPLING_CHECK)
        return row.status === QA_TAG_TASK_STATUS.FINISHED && !row.samplingCheckEmpId
      if (func === QA_TASK_GROUP_INLINE_FUNC.RE_CACHE) return row.status === QA_TAG_TASK_STATUS.SPLITTING
      return row.status === QA_TAG_TASK_STATUS.NOT_START
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    handleExpandChange(row, expandedRows) {
      if (row.status === 'splitting') {
        this.expandedRows = []
        return showToast('该任务组还未分解完', 'warning')
      }
      const index = this.expandedRows.indexOf(row.id)
      if (index === -1) {
        this.expandedRows = [row.id]
        this.$nextTick(() => {
          this.$refs.taskTableRef?.refresh()
        })
      } else {
        this.expandedRows = []
      }
    },
    handleTabClick(tab, e) {
      this.$nextTick(() => {
        this.refresh()
      })
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.queryParam.current = 1
      this.expandedRows = []
      this.query()
    },
    query() {
      pageQaTagTaskGroup(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    filter(postData) {
      let size = this.queryParam.size
      let dataSource = this.queryParam.dataSource
      this.queryParam = {
        current: 1,
        size: size,
        dataSource: dataSource
      }
      this.queryParam = { ...this.queryParam, ...postData }
      this.query()
    },
    formatDuration(duration) {
      if (duration < 60) {
        return `${duration.toFixed(2)} s`
      } else if (duration < 3600) {
        return `${(duration / 60).toFixed(2)} min`
      } else {
        return `${(duration / 3600).toFixed(2)} h`
      }
    },
    add() {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.$refs.formRef.show({
        dialogTitle: this.dialogTitle,
        dialogStatus: this.dialogStatus,
        formData: {
          dataSource: this.queryParam.dataSource,
          acquisitionType: 'parking'
        }
      })
    },
    edit(row) {
      this.dialogTitle = this.$t('修改')
      this.dialogStatus = 'edit'
      getQaTagTaskGroup(row.id).then(res => {
        this.formData = res.data
        this.$refs.formRef.show({
          dialogTitle: this.dialogTitle,
          dialogStatus: this.dialogStatus,
          formData: this.formData
        })
      })
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteQaTagTaskGroup(param).then(() => {
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    sect(row) {
      this.currentRow = row
      this.$refs.splitRef.show(row)
    },
    reCache(row) {
      reCache(row.id).then(res => {
        showToast('该任务已重新缓存')
        this.query()
      })
    },
    confirm(data) {
      splitTaskGroup(this.currentRow.id, { tagSplitType: data }).then(res => {
        showToast('任务分解中', 'success')
        this.query()
      })
    },
    samplingCheck(row) {
      this.$refs.samplingCheckRef.show(row)
    },
    async closed() {
      await this.query()
      this.$refs.taskTableRef?.query()
    }
  }
}
</script>

<style scoped lang="scss">
.ltw-toolbar {
  .filter-panel {
    width: calc(100% - 80px);
  }
}

.button-group {
  .el-button {
    margin-right: 10px;
  }
}

.el-table {
  .finish_count {
    color: #67c23a;
  }

  .task_count {
    color: #409eff;
  }

  .task_content {
    width: 98%;
  }
}
</style>
