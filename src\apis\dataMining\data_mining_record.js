import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

// 分页查询数据挖掘记录
export function pageDataMiningRecord(data) {
  return httpPost({
    url: '/api/data-mining-record/page',
    method: 'post',
    data
  })
}

// 获取数据挖掘结果
export function getMiningResults(recordId) {
  return httpGet({
    url: `/api/data-mining-record/${recordId}/results`,
    method: 'get'
  })
}

// 从挖掘结果创建数据集
export function createDatasetFromMiningResult(data) {
  return httpPost({
    url: '/api/data-mining-record/create-dataset',
    method: 'post',
    data
  })
}

// 获取数据挖掘记录详情
export function getDataMiningRecord(id) {
  return httpGet({
    url: `/api/data-mining-record/${id}`,
    method: 'get'
  })
}