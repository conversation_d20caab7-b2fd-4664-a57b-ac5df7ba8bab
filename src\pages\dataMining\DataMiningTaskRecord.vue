<template>
    <div class="ltw-page-container">
      <!-- 数据挖掘发起页面 -->
      <data-mining-initiated
        v-if="showMiningInitiated"
        :selected-dataset="selectedDataset"
        @back="handleBackToRecord"
      />

      <!-- 任务记录页面 -->
      <el-card v-else>
        <el-tabs v-model="queryParam.type" @tab-click="handleTabClick" style="position: relative">
          <el-tab-pane v-for="item in tabTypeList" :key="item.code" :label="item.name" :name="item.code"></el-tab-pane>
        </el-tabs>

        <!-- 搜索工具栏 -->
        <el-form :inline="true" class="search-form" label-width="80px">
          <el-row :gutter="10">
            <el-col :xs="24" :sm="12" :md="6" :lg="6">
              <el-form-item :label="$t('名称')" prop="name">
                <ltw-input :placeholder="$t('请输入名称')" v-model="queryParam.taskDefName" clearable  />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6">
              <el-form-item :label="$t('编号')" prop="code">
                <ltw-input :placeholder="$t('请输入编号')" v-model="queryParam.code" clearable />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6">
              <el-form-item :label="$t('执行状态')" prop="status">
                <el-select v-model="queryParam.status" clearable  :placeholder="$t('请选择执行状态')">
                  <el-option v-for="item in statusOptions" :key="item.code" :label="item.name" :value="item.code"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6">
              <el-form-item :label="$t('确认状态')" prop="status" >
                <el-select v-model="queryParam.confirmStatus" clearable  :placeholder="$t('请选择确认状态')">
                  <el-option v-for="item in confirmStatusOptions" :key="item.code" :label="item.name" :value="item.code"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6">
              <el-form-item :label="$t('发起人')" prop="creatorEmpName" >
                <employee-selection
              v-model="queryParam.creatorEmpId"
              clearable
              @change="employeeChange"
              :auto-load="false"
              :data="employeeList"
              class="input-content"
            />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6">
              <el-form-item :label="$t('仅看自己')" prop="oneSelf">
                <el-select v-model="queryParam.oneself" clearable :placeholder="$t('仅看自己')">
                  <el-option :label="$t('是')" value="1"></el-option>
                  <el-option :label="$t('否')" value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- DEFECT类型特有的事件ID搜索 -->
            <el-col :xs="24" :sm="12" :md="6" :lg="6" v-if="queryParam.type === '2'">
              <el-form-item :label="$t('事件ID')" prop="incidentId">
                <ltw-input :placeholder="$t('请输入事件ID')" v-model="queryParam.incidentId" clearable  />
              </el-form-item>
            </el-col>

          </el-row>
          <el-row style="height:20px;" >
                <div class="button-group">
                  <el-button @click="reset" ><ltw-icon icon-code="el-icon-refresh"></ltw-icon>重置</el-button>
                  <el-button type="primary" @click="query"><ltw-icon icon-code="el-icon-search"></ltw-icon>查询</el-button>
                  <el-button type="success" @click="openInitiateDialog"><ltw-icon icon-code="el-icon-plus"></ltw-icon>发起任务</el-button>
                  <!-- <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button> -->
                </div>

            </el-row>
        </el-form>

        <!-- 表格 -->
        <el-table :data="pageData.records" stripe row-key="id" ref="tableRef" highlight-current-row>
          <el-table-column prop="taskDefName" :label="$t('名称')" min-width="150"></el-table-column>
          <el-table-column prop="code" :label="$t('编号')" min-width="120"></el-table-column>
          <el-table-column prop="incidentId" :label="$t('事件ID')" min-width="120" v-if="queryParam.type === '2'"></el-table-column>
          <!-- <el-table-column prop="workflowName" :label="$t('工作流')" min-width="120"></el-table-column> -->
          <el-table-column prop="resources" :label="$t('数据源')" min-width="120">
            <template #default="scope">
              <el-link type="primary" v-if="scope.row.resources" @click="viewJsonData(scope.row.resources, '数据源')">
               查看
              </el-link>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="rules" :label="$t('规则')" min-width="120">
            <template #default="scope">
              <el-link type="primary" v-if="scope.row.rules" @click="viewJsonData(scope.row.rules, '规则')">
                查看
              </el-link>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="results" :label="$t('结果')" min-width="120">
            <template #default="scope">
              <el-link type="primary" v-if="scope.row.results" @click="viewJsonData(scope.row.results, '结果')">
                查看
              </el-link>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="resultsNum" :label="$t('结果数量')" min-width="100"></el-table-column>
          <el-table-column prop="creatorEmpName" :label="$t('发起人')" min-width="100"></el-table-column>
          <el-table-column prop="startTime" :label="$t('开始时间')" min-width="160"></el-table-column>
          <el-table-column prop="endTime" :label="$t('结束时间')" min-width="160"></el-table-column>
          <el-table-column prop="status" :label="$t('执行状态')" min-width="180" fixed="right">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">{{ getStatusLabel(scope.row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="confirmStatus" :label="$t('确认状态')" min-width="180" fixed="right">
            <template #default="scope">
              <el-tag :type="getConfirmStatusType(scope.row.confirmStatus)">{{ getConfirmStatusLabel(scope.row.confirmStatus) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remark" :label="$t('备注')" min-width="160"></el-table-column>
          <!-- <el-table-column :label="$t('操作')" min-width="200" fixed="right">
            <template #default="scope">
              <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button :type="item.buttonStyleType" @click="executeButtonMethod(item, scope.row)">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
            </template>
          </el-table-column> -->
        </el-table>

        <!-- 分页 -->
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParam.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParam.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.total"
        >
        </el-pagination>
      </el-card>

      <!-- 挖掘结果展示对话框 -->
      <el-dialog
        v-model="resultDialogVisible"
        :title="$t('挖掘结果')"
        width="80%"
        destroy-on-close
      >
        <div v-if="currentRecord">
          <el-tabs v-model="resultActiveTab">
            <el-tab-pane :label="$t('数据列表')" name="dataList">
              <el-table :data="miningResults" stripe height="500px">
                <el-table-column v-for="col in resultColumns" :key="col.prop" :prop="col.prop" :label="col.label" min-width="120"></el-table-column>
                <el-table-column :label="$t('操作')" width="150" fixed="right">
                  <template #default="scope">
                    <el-button type="primary" size="small" @click="viewDataDetail(scope.row)">
                      <ltw-icon icon-code="el-icon-view"></ltw-icon>
                      {{ $t('查看') }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane :label="$t('数据片段')" name="dataFragment">
              <div class="fragment-container">
                <div class="video-container">
                  <h3>{{ $t('视频回放') }}</h3>
                  <div class="video-player">
                    <!-- 视频播放器组件 -->
                    <!-- <video-player v-if="selectedData" :data="selectedData"></video-player> -->
                    <div class="no-data">{{ $t('请选择数据') }}</div>
                  </div>
                </div>
                <div class="map-container">
                  <h3>{{ $t('地图轨迹') }}</h3>
                  <div class="map-view">
                    <!-- 地图轨迹组件 -->
                    <!-- <map-trajectory v-if="selectedData" :data="selectedData"></map-trajectory> -->
                    <div class="no-data">{{ $t('请选择数据') }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane :label="$t('抽样展示')" name="sampling">
              <div class="sampling-container">
                <el-empty v-if="!samplingData.length" :description="$t('暂无抽样数据')"></el-empty>
                <div v-else class="sampling-grid">
                  <div v-for="(item, index) in samplingData" :key="index" class="sampling-item">
                    <div class="sampling-preview" @click="viewSamplingDetail(item)">
                      <img :src="item.previewUrl" alt="Preview">
                    </div>
                    <div class="sampling-info">{{ item.name }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-dialog>

      <!-- 数据集生成对话框 -->
      <el-dialog
        v-model="datasetDialogVisible"
        :title="$t('生成数据集')"
        width="500px"
        destroy-on-close
      >
        <el-form :model="datasetForm" :rules="datasetRules" ref="datasetFormRef" label-width="100px">
          <el-form-item :label="$t('数据集名称')" prop="name">
            <ltw-input v-model="datasetForm.name"></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('描述')" prop="description">
            <el-input v-model="datasetForm.description" type="textarea" :rows="3"></el-input>
          </el-form-item>
          <el-form-item :label="$t('数据选择')">
            <el-radio-group v-model="datasetForm.dataSelection">
              <el-radio :label="'all'">{{ $t('全部数据') }}</el-radio>
              <el-radio :label="'sample'">{{ $t('抽样数据') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('抽样比例')" v-if="datasetForm.dataSelection === 'sample'">
            <el-slider v-model="datasetForm.sampleRatio" :min="1" :max="100" :format-tooltip="formatTooltip"></el-slider>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="datasetDialogVisible = false">{{ $t('取消') }}</el-button>
            <el-button type="primary" @click="createDataset">{{ $t('确定') }}</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 发起任务对话框 -->
      <el-dialog
        v-model="initiateDialogVisible"
        title="发起数据挖掘任务"
        width="500px"
        destroy-on-close
      >
        <el-form :model="initiateForm" :rules="initiateRules" ref="initiateFormRef" label-width="100px">
          <el-form-item label="选择数据集" prop="datasetId">
            <el-select
              v-model="initiateForm.datasetId"
              placeholder="请选择数据集"
              style="width: 100%"
              :loading="datasetLoading"
            >
              <el-option
                v-for="dataset in datasetOptions"
                :key="dataset.id"
                :label="dataset.name"
                :value="dataset.id"
              >
                <div style="display: flex; justify-content: space-between;">
                  <span>{{ dataset.name }}</span>
                  <span style="color: #8492a6; font-size: 13px;">{{ dataset.description }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="initiateDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="confirmInitiate">确定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- JSON查看对话框 -->
      <el-dialog
        v-model="jsonDialogVisible"
        :title="jsonDialogTitle"
        width="80%"
        destroy-on-close
        class="json-dialog"
      >
        <!-- <div class="json-dialog-header">
          <div class="json-dialog-actions">
            <el-button type="primary" size="small" @click="copyJsonData">
              <ltw-icon icon-code="el-icon-copy-document"></ltw-icon>
              复制JSON
            </el-button>
            <el-button size="small" @click="closeJsonDialog">
              关闭
            </el-button>
          </div>
        </div> -->

        <div class="json-viewer-container">
          <json-viewer
            v-if="jsonData"
            :value="jsonData"
            :expand-depth="3"
            copyable
            sort
            boxed
            theme="jv-light"
          ></json-viewer>
          <div v-else class="no-data">
            <el-empty description="暂无数据"></el-empty>
          </div>
        </div>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closeJsonDialog">关闭</el-button>
            <!-- <el-button type="primary" @click="copyJsonData">复制JSON</el-button> -->
          </span>
        </template>
      </el-dialog>



    </div>
  </template>

  <script>
  import { showToast } from '@/plugins/util'
  import { listSysDictionary } from '@/apis/system/sys-dictionary'
  import { pageDataMiningRecord, getMiningResults, createDatasetFromMiningResult } from '@/apis/dataMining/data_mining_record'
  //import { getUserInfo } from '@/apis/system/user'
  // import VideoPlayer from '@/components/player/VideoPlayer.vue'
  // import MapTrajectory from '@/components/map/MapTrajectory.vue'
  import JsonViewer from 'vue-json-viewer'
  import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
  import { listSysRoleEmployee } from '@/apis/system/sys-role-employee'
  import DataMiningInitiated from './DataMiningInitiated.vue'
  import { listDatasets } from '@/apis/dataMining/dataset'


  export default {
    name: 'DataMiningRecord',
    components: {
     // VideoPlayer,
     // MapTrajectory
     JsonViewer,
     EmployeeSelection,
     DataMiningInitiated
    },
    data() {
      return {
        inlineFunctionList: [],
        outlineFunctionList: [],
        dataOperatePermission: {},
        pageData: {
          total: 0,
          records: []
        },
        queryParam: {
          current: 1,
          size: 10,
          taskDefName: '', // 任务定义名称
          code: '',
          status: '',
          confirmStatus: '', // 确认状态
          creatorEmpId: '', // 发起人ID
          oneself: '', // 仅看自己
          incidentId: '',
          type: '0' // 默认显示常规数据挖掘
        },
        tabTypeList: [{code:'0', name: '常规数据挖掘'},{code:'2', name: 'DEFECT数据挖掘'}],
        statusOptions: [
          {code:'RUNNING_EXECUTION',name:'执行中'},
          {code:'SUCCESS',name:'执行成功'},
          {code:'FAILURE',name:'执行失败'}
        ],
        confirmStatusOptions:[{code:'0',name:'待确认'},{code:'1',name:'已确认'},{code:'2',name:'已驳回'}],
        currentUserId: '',

        // 结果展示相关
        resultDialogVisible: false,
        resultActiveTab: 'dataList',
        currentRecord: null,
        miningResults: [],
        resultColumns: [],
        selectedData: null,
        samplingData: [],

        // 数据集生成相关
        datasetDialogVisible: false,
        datasetForm: {
          name: '',
          description: '',
          dataSelection: 'all',
          sampleRatio: 20,
          recordId: ''
        },
        datasetRules: {
          name: [{ required: true, message: this.$t('请输入数据集名称'), trigger: 'blur' }]
        },

        // JSON查看对话框相关
        jsonDialogVisible: false,
        jsonDialogTitle: '',
        jsonData: null,
        employeeList: [],

        // 发起任务相关
        initiateDialogVisible: false,
        initiateForm: {
          datasetId: ''
        },
        initiateRules: {
          datasetId: [{ required: true, message: '请选择数据集', trigger: 'change' }]
        },
        datasetOptions: [],
        datasetLoading: false,

        // 数据挖掘发起页面相关
        showMiningInitiated: false,
        selectedDataset: null
      }
    },
    created() {
      this.initFunctionList()
      this.initPermission()
      //this.getStatusOptions()
    //  this.getCurrentUser()
      this.listSysRoleEmployee()
      this.query()
    },
    methods: {
    listSysRoleEmployee() {
      listSysRoleEmployee().then(res => {
        this.employeeList = res.data
      })
    },
    employeeChange(data) {
      //this.formData.reporterEmpName = data.node.name
    },
      initFunctionList() {
        this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      },
      initPermission() {
        // 初始化权限
        this.dataOperatePermission = {
          view: true,
          createDataset: true
        }
      },
      getStatusOptions() {
        listSysDictionary({
          typeCode: 'data_mining_status'
        }).then(res => {
          this.statusOptions = res.data.map(item => ({
            value: item.code,
            label: item.name
          }))
        })
      },

      handleTabClick() {
        this.$nextTick(()=>{
          this.reset()
        })
      },
      executeButtonMethod(button, row) {
        this[button.buttonCode](row, button)
      },
      refresh() {
        this.queryParam.current = 1
        this.query()
      },

      // 重置搜索条件
      reset() {
        // 保存当前的分页大小和类型
        const currentSize = this.queryParam.size
        const currentType = this.queryParam.type

        // 重置查询参数到初始状态
        this.queryParam = {
          current: 1,
          size: currentSize, // 保持当前分页大小
          taskDefinitionName: '', // 对应模板中的名称字段
          code: '',
          status: '',
          confirmStatus: '', // 确认状态
          creatorEmpId: '', // 发起人ID
          justSelf: '', // 仅看自己
          incidentId: '', // 事件ID（DEFECT类型特有）
          type: currentType // 保持当前选中的tab类型
        }

        // 重置后自动查询
        this.query()
      },
      query() {
        pageDataMiningRecord(this.queryParam).then(res => {
          this.pageData = res.data
        })
      },
      handleSizeChange(value) {
        this.queryParam.size = value
        this.query()
      },
      handleCurrentChange(value) {
        this.queryParam.current = value
        this.query()
      },
      getStatusType(status) {
        // 枚举值对应的标签类型
        const statusTypeMap = {
          'SUCCESS': 'success',           // 执行成功 - 成功色
          'RUNNING_EXECUTION': 'warning', // 执行中 - 警告色
          'FAILURE': 'danger'             // 执行失败 - 危险色
        }

        // 优先使用枚举值映射
        if (statusTypeMap[status]) {
          return statusTypeMap[status]
        }

        // 兼容原有的字符串状态
        const legacyStatusMap = {
          'pending': 'info',
          'processing': 'warning',
          'completed': 'success',
          'failed': 'danger'
        }

        return legacyStatusMap[status] || 'info'
      },
      getStatusLabel(status) {
        // 枚举值映射
        const statusMap = {
          'SUCCESS': '执行成功',
          'RUNNING_EXECUTION': '执行中',
          'FAILURE': '执行失败'
        }

        // 优先使用枚举值映射，如果没有找到则使用原有逻辑
        if (statusMap[status]) {
          return statusMap[status]
        }

        // 兼容原有的数字代码逻辑
        const option = this.statusOptions.find(item => item.code === status || item.name === status)
        return option ? option.name : status
      },

      getConfirmStatusLabel(confirmStatus) {

        // 确认状态映射
        const confirmStatusMap = {
          '0': '待确认',
          '1': '已确认',
          '2': '已驳回'
        }

        // 优先使用映射，如果没有找到则使用原有逻辑
        if (confirmStatusMap[confirmStatus]) {
          return confirmStatusMap[confirmStatus]
        }

        // 兼容原有逻辑
        const option = this.confirmStatusOptions.find(item => item.code === confirmStatus || item.name === confirmStatus)
        return option ? option.name : '待确认'
      },

      getConfirmStatusType(confirmStatus) {
        // 确认状态对应的标签类型
        const confirmStatusTypeMap = {
          '0': 'warning',  // 待确认 - 警告色
          '1': 'success',  // 确认 - 成功色
          '2': 'danger'    // 驳回 - 危险色
        }
        return confirmStatusTypeMap[confirmStatus] || 'warning'
      },
      viewDataset(datasetId) {
        // 跳转到数据集详情页
        this.$router.push({
          path: '/data/dataset/detail',
          query: { id: datasetId }
        })
      },
      viewResult(row) {
        this.currentRecord = row
        this.resultDialogVisible = true
        this.resultActiveTab = 'dataList'
        this.selectedData = null
        this.samplingData = []

        // 获取挖掘结果数据
        getMiningResults(row.id).then(res => {
          this.miningResults = res.data.records || []
          // 动态生成结果列
          if (this.miningResults.length > 0) {
            const firstItem = this.miningResults[0]
            this.resultColumns = Object.keys(firstItem)
              .filter(key => !['id', 'operations'].includes(key))
              .map(key => ({
                prop: key,
                label: this.$t(key)
              }))
          }

          // 获取抽样数据
          if (res.data.sampling) {
            this.samplingData = res.data.sampling
          }
        })
      },
      viewDataDetail(data) {
        this.selectedData = data
        this.resultActiveTab = 'dataFragment'
      },
      viewSamplingDetail(item) {
        this.selectedData = item.data
        this.resultActiveTab = 'dataFragment'
      },
      createDataset(row) {
        if (!row.id) {
          return
        }

        this.datasetForm = {
          name: `${row.name}_dataset_${new Date().getTime()}`,
          description: `Generated from mining task: ${row.name}`,
          dataSelection: 'all',
          sampleRatio: 20,
          recordId: row.id
        }

        this.datasetDialogVisible = true
      },
      formatTooltip(val) {
        return `${val}%`
      },
      createDataset() {
        this.$refs.datasetFormRef.validate(valid => {
          if (!valid) return

          const postData = {
            ...this.datasetForm,
            useAllData: this.datasetForm.dataSelection === 'all',
            sampleRatio: this.datasetForm.dataSelection === 'sample' ? this.datasetForm.sampleRatio / 100 : 1
          }

          createDatasetFromMiningResult(postData).then(res => {
            showToast(this.$t('数据集创建成功'))
            this.datasetDialogVisible = false

            // 跳转到新创建的数据集
            if (res.data && res.data.id) {
              this.$router.push({
                path: '/data/dataset/detail',
                query: { id: res.data.id }
              })
            }
          })
        })
      },

      // JSON查看相关方法
      viewJsonData(jsonData, title) {
        try {
          // 如果是字符串，尝试解析为JSON
          if (typeof jsonData === 'string') {
            this.jsonData = JSON.parse(jsonData)
          } else {
            this.jsonData = jsonData
          }
          this.jsonDialogTitle = title
          this.jsonDialogVisible = true
        } catch (error) {
          console.error('JSON解析失败:', error)
          this.$message.error('JSON数据格式错误')
        }
      },

      closeJsonDialog() {
        this.jsonDialogVisible = false
        this.jsonData = null
        this.jsonDialogTitle = ''
      },

      copyJsonData() {
        try {
          const jsonString = JSON.stringify(this.jsonData, null, 2)
          navigator.clipboard.writeText(jsonString).then(() => {
            this.$message.success('JSON数据已复制到剪贴板')
          }).catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea')
            textArea.value = jsonString
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            this.$message.success('JSON数据已复制到剪贴板')
          })
        } catch (error) {
          this.$message.error('复制失败')
        }
      },

      // 发起任务相关方法
      openInitiateDialog() {
        this.initiateDialogVisible = true
        this.loadDatasets()
      },

      async loadDatasets() {
        this.datasetLoading = true
        try {
          const res = await listDatasets()
          this.datasetOptions = res.data || []
        } catch (error) {
          console.error('获取数据集列表失败:', error)
          this.$message.error('获取数据集列表失败')
        } finally {
          this.datasetLoading = false
        }
      },

      confirmInitiate() {
        this.$refs.initiateFormRef.validate(valid => {
          if (!valid) return

          // 找到选中的数据集
          this.selectedDataset = this.datasetOptions.find(
            dataset => dataset.id === this.initiateForm.datasetId
          )

          // 关闭选择对话框，打开发起页面
          this.initiateDialogVisible = false
          this.showMiningInitiated = true
        })
      },

      handleBackToRecord() {
        this.showMiningInitiated = false
        this.selectedDataset = null
        this.initiateForm.datasetId = ''
        // 刷新任务记录列表
        this.query()
      }
    }
  }
  </script>

  <style lang="scss" scoped>
  .search-form {
    margin-bottom: 15px;

    .el-form-item {
      margin-bottom: 10px;
      width: 100%;
    }
    .button-form-item >>>.el-form-item__content {

            display: flex;
            justify-content: flex-end;

     }


    .el-select, .ltw-input {
      width: 100%;
    }

    .button-group {
      display: flex;
      flex-wrap: wrap;
      position:absolute;
      right: 0;
      top: -5px;

      .el-button {
        margin-right: 10px;
        margin-bottom: 5px;
      }
    }
  }
  .ltw-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 16px;
  }

  .ltw-search-container {
    min-width: 200px;
  }

  .button-group {
    display: flex;
  }

  .fragment-container {
    display: flex;
    height: 500px;
    gap: 20px;
  }

  .video-container, .map-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 10px;
    background-color: #f5f7fa;
  }

  .video-player, .map-view {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
  }

  .no-data {
    color: #909399;
    font-size: 14px;
  }

  .sampling-container {
    height: 500px;
    overflow-y: auto;
  }

  .sampling-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    padding: 10px;
  }

  .sampling-item {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.3s;
    cursor: pointer;
  }

  .sampling-item:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
  }

  .sampling-preview {
    height: 150px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
  }

  .sampling-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .sampling-info {
    padding: 8px;
    font-size: 14px;
    color: #606266;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 响应式调整 */
  @media screen and (max-width: 768px) {
    .fragment-container {
      flex-direction: column;
      height: auto;
    }

    .video-container, .map-container {
      height: 300px;
    }
  }

  // JSON查看对话框样式
  .json-dialog {
    .json-dialog-header {
      margin-bottom: 16px;

      .json-dialog-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
      }
    }

    .json-viewer-container {
      max-height: 600px;
      overflow-y: auto;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 16px;
      background-color: #fafafa;

      .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
      }
    }
  }

  // vue-json-viewer 样式覆盖
  :deep(.jv-container) {
    .jv-code {
      padding: 0;
    }

    .jv-item {
      .jv-key {
        color: #e96900;
        font-weight: 600;
      }

      .jv-string {
        color: #42b883;
      }

      .jv-number {
        color: #1976d2;
      }

      .jv-boolean {
        color: #ff5722;
      }

      .jv-null {
        color: #9e9e9e;
      }
    }
  }


  </style>

