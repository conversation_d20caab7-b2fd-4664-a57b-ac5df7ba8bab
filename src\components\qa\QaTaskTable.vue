<template>
  <div class="task-detail">
    <p class="task-detail-header">{{ $t('任务明细') }}</p>
    <el-table :data="pageData.records" max-height="calc(100vh - 500px)">
      <el-table-column
        header-align="left"
        align="left"
        prop="code"
        :label="$t('编码')"
        width="150"
        show-tooltip-when-overflow
      >
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="acquisitionType"
        :label="$t('采集类型')"
        width="150"
        show-tooltip-when-overflow
      >
      </el-table-column>
      <template v-if="tagQaLevel === 'frame'">
        <el-table-column
          header-align="center"
          align="center"
          prop="vin"
          :label="$t('车辆')"
          width="100"
          show-tooltip-when-overflow
        ></el-table-column>
        <el-table-column header-align="center" align="center" prop="measurementTime" :label="$t('时间')" width="350">
          <template #default="scope">
            <template v-if="scope.row.startTime">
              <el-tag>{{ scope.row.startTime }}</el-tag>
              <span>&nbsp;&nbsp;<el-link>-</el-link>&nbsp;&nbsp;</span>
              <el-tag>{{ scope.row.endTime }}</el-tag>
            </template>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        header-align="center"
        align="center"
        prop="datasetName"
        :label="$t('数据集')"
        width="150"
        show-tooltip-when-overflow
        v-if="dataSource === 'dataset'"
      ></el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        prop="startTime"
        :label="$t('交付日期')"
        width="150"
        v-if="dataSource === 'delivery_data'"
      >
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        prop="deleteCount"
        :label="$t('删除标签量')"
        width="100"
      ></el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        prop="addCount"
        :label="$t('新增标签量')"
        width="100"
      ></el-table-column>
      <el-table-column header-align="center" align="center" :label="$t('任务进度')" width="100">
        <template #default="scope">
          <span class="finish_count">{{ scope.row.finishCount }}</span> /
          <span class="task_count">{{ scope.row.detailCount }}</span>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" prop="duration" :label="$t('总时长')" width="100">
        <template #default="scope">
          <template v-if="scope.row.duration">
            <el-tag>{{ formatDuration(scope.row.duration) }}</el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        prop="empName"
        :label="$t('负责人')"
        width="100"
      ></el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        prop="samplingCheckEmpName"
        :label="$t('抽检人')"
        width="100"
      ></el-table-column>
      <el-table-column header-align="center" align="center" prop="statusName" :label="$t('状态')" min-width="100">
        <template #default="scope">
          <el-row class="status-col">
            <el-tag
              :type="
                scope.row.status === QA_TAG_TASK_STATUS.FINISHED
                  ? 'success'
                  : scope.row.status === QA_TAG_TASK_STATUS.EXECUTING
                  ? 'warning'
                  : ''
              "
            >
              {{ scope.row.statusName }}
            </el-tag>
            <el-popover
              placement="bottom"
              :width="200"
              trigger="hover"
              :content="scope.row.remark"
              v-if="scope.row.remark"
            >
              <template #reference>
                <ltw-icon :icon-code="dataOperatePermission['view'].buttonIconCode"></ltw-icon>
              </template>
            </el-popover>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" prop="statusName" :label="$t('标签')" min-width="100">
        <template #default="scope">
          <el-popover placement="bottom" trigger="click" :width="400">
            <!-- 主体内容 兜底至少渲染一个元素 -->
            <template #default>
              <template v-if="scope.row.tagList && scope.row.tagList.length">
                <el-tag
                  v-for="item in scope.row.tagList"
                  :type="checkTagType(item)"
                  :key="item.code"
                  style="margin: 3px"
                  >{{ item.name }}
                </el-tag>
              </template>
              <span v-else style="color: #999">No tag</span>
            </template>
            <template #reference>
              <el-row style="display: flex; justify-content: center; align-items: center">
                <span>
                  <el-link type="primary" :underline="false" v-if="scope.row.tagClassificationName">
                    {{ scope.row.tagClassificationName }}&nbsp;&nbsp;:&nbsp;&nbsp;
                  </el-link>
                  <el-link type="primary" :underline="false">{{ scope.row.tagList?.length || 0 }}</el-link>
                </span>
              </el-row>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" :label="$t('操作')" fixed="right" min-width="200">
        <template #default="scope">
          <template v-if="Object.keys(dataOperatePermission)?.length">
            <template v-for="item in Object.keys(dataOperatePermission)" :key="item">
              <el-button
                :type="dataOperatePermission[item].buttonStyleType"
                @click="executeButtonMethod(dataOperatePermission[item], scope.row)"
                size="small"
                v-if="
                  Object.values(QA_TASK_INLINE_FUNC).includes(item) &&
                  dataOperatePermission[item] &&
                  checkPermission(scope.row, item)
                "
              >
                {{ dataOperatePermission[item].name }}
              </el-button>
            </template>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      v-model:current-page="queryParam.current"
      :page-sizes="[5, 10, 20, 30]"
      v-model:page-size="queryParam.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pageData.total"
      small
    >
    </el-pagination>
    <tag-quality-check-by-frame
      ref="tagQaRef"
      @closed="closed"
      v-if="tagQaLevel === 'frame'"
    ></tag-quality-check-by-frame>
    <tag-quality-check-by-clip ref="tagQaClipRef" @closed="closed"></tag-quality-check-by-clip>
  </div>
</template>
<script>
import {
  exportExcel,
  pageQaTagTask,
  receiveQaTagTask,
  startCheckQaTagTask,
  releaseQaTagTask
} from '@/apis/qa/qa-tag-task'
import { checkTagType, showToast, downloadFile } from '@/plugins/util'
import TagQualityCheckByFrame from '@/pages/qa/TagQualityCheckByFrame.vue'
import { QA_TAG_TASK_STATUS, QA_TASK_INLINE_FUNC } from '@/plugins/constants/data-dictionary'
import { ElMessageBox } from 'element-plus'
import TagQualityCheckByClip from '@/pages/qa/TagQualityCheckByClip.vue'
import LtwIcon from '@/components/base/LtwIcon.vue'

export default {
  name: 'QaTaskTable',
  components: { LtwIcon, TagQualityCheckByClip, TagQualityCheckByFrame },
  emits: ['closed'],
  props: {
    taskGroupId: {
      type: String,
      default: ''
    },
    dataOperatePermission: {
      type: Object,
      default: {}
    },
    groupCode: {
      type: String,
      default: ''
    },
    tagQaLevel: {
      type: String,
      default: ''
    },
    dataSource: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      QA_TASK_INLINE_FUNC,
      QA_TAG_TASK_STATUS,
      checkTagType,
      queryParam: {
        current: 1,
        size: 10,
        excludeGroupStatus: 'not_start,splitting'
      },
      pageData: {
        records: [],
        total: 0
      },
      currentUser: this.$store.state.permission.currentUser
    }
  },
  watch: {
    groupCode(val) {
      this.queryParam.groupCode = val
    }
  },
  methods: {
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    checkPermission(row, item) {
      switch (item) {
        case QA_TASK_INLINE_FUNC.RECEIVE_TASK:
          return row.status === QA_TAG_TASK_STATUS.TO_BE_RECEIVED
        case QA_TASK_INLINE_FUNC.QUALITY_CHECK:
          return (
            row.empId === this.currentUser.empId &&
            [QA_TAG_TASK_STATUS.NOT_START, QA_TAG_TASK_STATUS.EXECUTING].includes(row.status)
          )
        case QA_TASK_INLINE_FUNC.EXPORT_FILE:
          return this.tagQaLevel === 'frame'
        case QA_TASK_INLINE_FUNC.START_CHECK:
          return row.status === QA_TAG_TASK_STATUS.TO_BE_CHECKED
        case QA_TASK_INLINE_FUNC.CHECK:
          return row.samplingCheckEmpId === this.currentUser.empId && row.status === QA_TAG_TASK_STATUS.CHECKING
        case QA_TASK_INLINE_FUNC.RELEASE:
          return row.empId === this.currentUser.empId && row.status === QA_TAG_TASK_STATUS.NOT_START
        default:
          return true
      }
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    refresh() {
      this.queryParam.current = 1
      this.query()
    },
    query() {
      this.queryParam.groupCode = this.groupCode
      pageQaTagTask(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    formatDuration(duration) {
      if (duration < 60) {
        return `${duration.toFixed(2)} s`
      } else if (duration < 3600) {
        return `${(duration / 60).toFixed(2)} min`
      } else {
        return `${(duration / 3600).toFixed(2)} h`
      }
    },
    receiveTask(row) {
      receiveQaTagTask(row.id).then(res => {
        showToast('认领成功', 'success')
        let index = this.pageData.records.findIndex(item => item.id === row.id)
        if (index >= 0) {
          this.pageData.records[index] = res.data
        }
      })
    },
    handleTagQa(row, operation) {
      if (this.tagQaLevel === 'frame') {
        row.level = 'frame'
        row.levelName = '帧级别'
        this.$refs.tagQaRef.show(row, operation) // 调用帧级别的方法
      } else {
        row.level = 'clip'
        row.levelName = 'Clip级别'
        this.$refs.tagQaClipRef.show(row, operation) // 调用 Clip 级别的方法
      }
    },
    view(row) {
      this.handleTagQa(row, 'view')
    },
    qualityCheck(row) {
      this.handleTagQa(row, 'quality_check')
    },
    startCheck(row) {
      startCheckQaTagTask(row.id).then(res => {
        row.status = 'checking'
        row.statusName = '检查中'
        this.handleTagQa(row, 'check')
      })
    },
    check(row) {
      this.handleTagQa(row, 'check')
    },
    release(row) {
      releaseQaTagTask(row.id).then(res => {
        showToast('释放成功')
        this.query()
      })
    },
    exportFile(row) {
      exportExcel({ taskCode: row.code }, 'blob').then(res => {
        if (res.type === 'application/json') {
          this.$message.warning({
            message: '暂无数据可导出!',
            type: 'warning'
          })
          return
        }
        ElMessageBox.prompt('请输入文件名', 'Tip', {
          confirmButtonText: '确认',
          cancelButtonText: '取消'
        })
          .then(({ value }) => {
            downloadFile(res, value)
          })
          .catch(() => {
            this.$message.info({
              message: '取消输入',
              type: 'info'
            })
          })
      })
    },
    closed() {
      // this.query()
      this.$emit('closed')
    }
  }
}
</script>
<style lang="scss" scoped>
.status-col {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: center;

  .ltw-icon {
    margin-left: 5px;
    cursor: pointer;
    color: #409eff;
  }
}

.task-detail {
  width: 100%;
  display: flex;
  flex-direction: column;
  //border: 1px solid #eee;

  .task-detail-header {
    width: 98%;
    display: flex;
    justify-content: flex-start;
    color: #409eff;
    font-size: 12px;
    margin: 5px auto;
  }

  .el-table {
    width: 98%;
    display: flex;
    justify-content: center;
    margin: 5px auto;

    :deep(.el-table__inner-wrapper) {
      width: 100%;
    }

    .finish_count {
      color: #67c23a;
    }

    .task_count {
      color: #409eff;
    }
  }
}
</style>
