<template>
  <el-dialog :title="$t('历史版本')" v-model="dialogVisible" width="60%" @close="dialogClosed">
    <div style="display: flex; align-items: center; justify-content: end">
      <el-button type="primary" @click="addVersion" style="margin-right: 10px">
        <ltw-icon icon-code="el-icon-plus" style="margin-right: 5px"></ltw-icon>
        {{ $t('新增') }}
      </el-button>

      <el-dropdown @command="handleCommand" class="batch-operate-btn">
        <el-button type="primary">
          批量操作
          <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="batchRemove">
              <ltw-icon icon-code="el-icon-delete"></ltw-icon> {{ $t('批量删除') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-table
      :data="tableData.records"
      stripe
      @selection-change="handleSelectionChange"
      :row-key="getRowKeys"
      ref="tableRef"
      highlight-current-row
      style="min-height: 300px; max-height: 600px; overflow-y: auto"
    >
      <el-table-column
        header-align="left"
        align="left"
        type="selection"
        width="55"
        :reserve-selection="true"
        :selectable="row => row.latest !== rowData.latest"
      ></el-table-column>
      <el-table-column header-align="left" align="left" prop="tagName" :label="$t('标签名称')"></el-table-column>
      <el-table-column header-align="left" align="left" prop="version" :label="$t('历史版本')">
        <template #default="scope">
          <span v-if="rowData.latest === scope.row.latest">
            <el-tag type="success"> 当前版本v{{ scope.row.version }}</el-tag>
          </span>
          <span v-else>v{{ scope.row.version }}</span>
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" prop="description" :label="$t('描述')"></el-table-column>
      <el-table-column header-align="left" align="left" prop="maintainer" :label="$t('操作人')"></el-table-column>
      <el-table-column header-align="left" align="left" prop="updateTime" :label="$t('操作时间')"></el-table-column>
      <el-table-column header-align="left" align="left" :label="$t('操作')" min-width="180">
        <template #default="scope">
          <el-button-group>
            <el-tooltip
              v-if="rowData.latest !== scope.row.latest"
              effect="dark"
              content="切换到该版本"
              placement="top"
              :enterable="false"
            >
              <el-button type="success" size="mini" @click="switchVersion(scope.row)">
                <ltw-icon icon-code="el-icon-open"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip
              v-if="rowData.latest !== scope.row.latest"
              effect="dark"
              content="编辑"
              placement="top"
              :enterable="false"
            >
              <el-button type="warning" size="mini" @click="editVersion(scope.row)">
                <ltw-icon icon-code="el-icon-edit"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip
              v-if="rowData.latest !== scope.row.latest"
              effect="dark"
              content="删除"
              placement="top"
              :enterable="false"
            >
              <el-button type="danger" size="mini" @click="singleRemove({ id: scope.row.id })">
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip effect="dark" content="查看详情" placement="top" :enterable="false">
              <el-button type="primary" size="mini" @click="viewVersion(scope.row)">
                <ltw-icon icon-code="el-icon-view"></ltw-icon>
              </el-button>
            </el-tooltip>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      size="mini"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryParam.current"
      :page-sizes="[5, 10, 20, 30]"
      :page-size="queryParam.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="tableData.total"
    >
    </el-pagination>
    <DmiRuleConfig ref="dmiRuleConfigRef" @confirm="updateAddVersion" />
  </el-dialog>
</template>

<script>
const defaultFormData = {}
import {
  pageDmiRuleDefinitionVersion,
  deleteDmiRuleDefinitionVersion,
  changeToDmiRuleDefinitionVersion,
  getDmiRuleDefinition,
  addInitDmiRuleDefinitionVersion
} from '@/apis/dmi/dmi-rule-definition'
import DmiRuleConfig from './DmiRuleConfig.vue'

export default {
  name: 'DmiRuleHistoryVersion',
  components: { DmiRuleConfig },
  emits: ['confirm'],
  props: {},
  data() {
    return {
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      checkedItem: Object.assign({}, defaultFormData),
      rowData: [],
      tableData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      selectedData: [],
      maintainerOptions: [],
      typeOptions: [],
      addInitData: []
    }
  },
  created() {},
  methods: {
    getRowData(row) {
      return getDmiRuleDefinition(row.id).then(res => {
        this.rowData = res.data
      })
    },
    getRowKeys(row) {
      return row.id
    },
    async show(maintainerOptions, typeOptions, row) {
      this.maintainerOptions = maintainerOptions
      this.typeOptions = typeOptions
      await this.getRowData(row)
      await this.query()
      this.dialogVisible = true
    },
    query() {
      const data = {
        ...this.queryParam,
        tagCode: this.rowData.tagCode
      }
      return pageDmiRuleDefinitionVersion(data).then(res => {
        this.tableData = res.data
      })
    },
    dialogClosed() {
      this.dialogVisible = false
      this.initForm()
    },

    initForm() {
      this.formData = Object.assign({}, defaultFormData)
      this.checkedItem = Object.assign({}, defaultFormData)
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    async addVersion() {
      const tagCode = this.rowData.tagCode
      await addInitDmiRuleDefinitionVersion(tagCode).then(res => {
        this.addInitData = res.data
      })
      const commonParams = {
        row: this.addInitData,
        maintainerOption: this.maintainerOptions,
        typeOption: this.typeOptions
      }
      this.$refs.dmiRuleConfigRef.show({ ...commonParams, mode: 'addVersion' })
    },
    updateAddVersion() {
      this.queryParam.current = 1
      this.query()
      this.$emit('confirm')
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        this.$message.warning({
          message: '请先选择数据再执行批量操作',
          type: 'warning'
        })
        return
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove({ id }) {
      this.remove({ id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      let msg = '此操作将永久删除选中数据，是否继续?'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteDmiRuleDefinitionVersion(param).then(() => {
            this.queryParam.current = 1
            this.query()
            this.$refs.tableRef.clearSelection()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    switchVersion(row) {
      const tagCode = this.rowData.tagCode
      const id = row.id
      const version = row.version
      this.$confirm(`确定要切换到版本 v${version} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          changeToDmiRuleDefinitionVersion(tagCode, id).then(() => {
            this.dialogVisible = false
            this.$emit('confirm')
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消切换'
          })
        })
    },
    viewVersion(row) {
      const commonParams = {
        row,
        maintainerOption: this.maintainerOptions,
        typeOption: this.typeOptions
      }
      this.$refs.dmiRuleConfigRef.show({ ...commonParams, mode: 'view' })
    },
    editVersion(row) {
      const commonParams = {
        row,
        maintainerOption: this.maintainerOptions,
        typeOption: this.typeOptions,
        versionEdit: true
      }
      this.$refs.dmiRuleConfigRef.show({ ...commonParams, mode: 'edit' })
    }
  }
}
</script>
