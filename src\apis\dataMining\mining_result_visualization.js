import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

/**
 * 获取摄像头列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getCameraList(params = {}) {
  return httpGet({
    url: '/api/mining-result/cameras',
    method: 'get',
    params
  })
}

/**
 * 获取车辆列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getVehicleList(params = {}) {
  return httpGet({
    url: '/api/mining-result/vehicles',
    method: 'get',
    params
  })
}

/**
 * 获取挖掘结果数据
 * @param {Object} params 查询参数
 * @param {string} params.miningDefineId 挖掘定义ID
 * @param {string} params.cameraId 摄像头ID
 * @param {string} params.vehicleId 车辆ID
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise}
 */
export function getMiningResultData(params) {
  return httpGet({
    url: '/api/mining-result/data',
    method: 'get',
    params
  })
}

/**
 * 获取图像数据
 * @param {Object} params 查询参数
 * @param {string} params.miningDefineId 挖掘定义ID
 * @param {string} params.cameraId 摄像头ID
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise}
 */
export function getImageData(params) {
  return httpGet({
    url: '/api/mining-result/images',
    method: 'get',
    params
  })
}

/**
 * 获取轨迹数据
 * @param {Object} params 查询参数
 * @param {string} params.miningDefineId 挖掘定义ID
 * @param {string} params.vehicleId 车辆ID
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise}
 */
export function getTrajectoryData(params) {
  return httpGet({
    url: '/api/mining-result/trajectory',
    method: 'get',
    params
  })
}

/**
 * 获取时间轴数据
 * @param {Object} params 查询参数
 * @param {string} params.miningDefineId 挖掘定义ID
 * @param {string} params.cameraId 摄像头ID
 * @param {string} params.vehicleId 车辆ID
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise}
 */
export function getTimelineData(params) {
  return httpGet({
    url: '/api/mining-result/timeline',
    method: 'get',
    params
  })
}
