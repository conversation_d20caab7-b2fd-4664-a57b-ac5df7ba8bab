import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

// 分页查询数据挖掘任务定义
export function pageDataMiningDefine(params = {}) {
  return httpGet({
    url: '/dmi/dmi_task_definitions/page',
    params
  })
}

// 获取数据挖掘任务定义详情
export function getDataMiningDefine(id) {
  return httpGet({
    url: `/dmi/dmi_task_definitions/${id}`,
    method: 'get'
  })
}

// 新增数据挖掘任务定义
export function saveDataMiningDefine(data) {
  return httpPost({
    url: '/dmi/dmi_task_definitions',
    method: 'post',
    data
  })
}

// 更新数据挖掘任务定义
export function updateDataMiningDefine(data) {
  return httpPut({
    url: '/dmi/dmi_task_definitions',
    method: 'put',
    data
  })
}

// 删除数据挖掘任务定义
export function deleteDataMiningDefine(data) {
  return httpDelete({
    url: '/dmi/dmi_task_definitions',
    method: 'delete',
    data
  })
}

export function getTagTreeData(type){
  return httpGet({
    url: `/dmi/dmi_rule_definitions/ruleTree/${type}`,
  })
}