import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

// 分页查询数据挖掘任务定义
export function pageDataMiningDefine(data) {
  return httpPost({
    url: '/api/data-mining-define/page',
    method: 'post',
    data
  })
}

// 获取数据挖掘任务定义详情
export function getDataMiningDefine(id) {
  return httpGet({
    url: `/api/data-mining-define/${id}`,
    method: 'get'
  })
}

// 新增数据挖掘任务定义
export function saveDataMiningDefine(data) {
  return httpPost({
    url: '/dmi/dmi_task_definitions',
    method: 'post',
    data
  })
}

// 更新数据挖掘任务定义
export function updateDataMiningDefine(data) {
  return httpPut({
    url: '/api/data-mining-define',
    method: 'put',
    data
  })
}

// 删除数据挖掘任务定义
export function deleteDataMiningDefine(data) {
  return httpDelete({
    url: '/api/data-mining-define',
    method: 'delete',
    data
  })
}