<template>
  <div :class="isFull ? 'map-overview' : 'map-dialog'">
    <!-- <div class="info">
      当前绘制面积(㎡)：<span class="num">{{ computeArea }}</span>
    </div> -->
    <div class="map-container">
      <div id="allmap" class="map-chart" />
      <div class="search-container" v-show="!isView">
        <el-cascader
            v-model="canton"
            :placeholder="$t('请选择行政区')"
            :options="cantonTreeList"
            :props="cantonListProps"
            clearable
            filterable
            @change="listParkingLotByCanton"
          />
        <ltw-input
          :placeholder="$t('请输入路口名称')"
          clearable
          v-model="place"
          @keyup.enter="handleAddressLocation"
          class="input-button"
        >
          <template #append>
            <el-button @click="handleAddressLocation">
              <ltw-icon icon-code="el-icon-search"></ltw-icon>
            </el-button>
          </template>
        </ltw-input>
       
      </div>
      <div id="toolControl">
        <div :class="isView ? 'disable-btn' : ''">
          <div class="toolItem" id="delete" title="删除" @click="handleDel"></div>
          <div class="toolItem active" id="polygon" title="多边形"></div>
          <div class="toolItem" id="rectangle" title="矩形"></div>
          <div class="toolItem" id="circle" title="圆形"></div>
        </div>
        <div class="toolItem" :id="isFull ? 'smallscreen' : 'fullscreen'" @click="handleFullScreen"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { TxMap, addMarker, getGeoCoderByAddress, isLatitudeLongitude,calculateCirclePoint,calculateRectanglePoints,latLngPoint} from '@/plugins/map/TxMap'
import { showToast } from '@/plugins/util'
import { getSysCantonTree } from '@/apis/system/sys-canton'
import { jsonp } from 'vue-jsonp'
import md5 from 'js-md5'
import GLB_CONFIG from '@/plugins/glb-constant'

let globalMap
let editor
let polygon
let rectangle
let circle
let globalCenterMark
var activeType = 'polygon' // 激活的图形编辑类型
export default {
  name: 'TXMapInterscetion',
  emits: ['polygonPoint'],
  data() {
    return {
      map: '',
      mapId: 'allmap',
      positionList: [],
      convertor: '',
      centerPoint: '',
      place: '',
      isFull: false,
      computeArea: 0,
      canton:'',
      cantonTreeList:[],
      cantonListProps: {
        value: 'locationArr',
        label: 'name',
        checkStrictly: true,
        emitPath: false
      },
    }
  },
  props: {
    isView: {
      type: Boolean,
      defalut: false
    },
    region: {
      type: Object
    },
    area: {
      type: Number,
      default: 0
    }
  },
  mounted(){
    this.show()
    this.listCantonTree()
  },
  watch: {
    region(newVal, oldVal) {
      this.place = ''
    },
    area(newVal, oldVal) {
      this.computeArea = newVal.toFixed(2)
    }
  },
  methods: {
    listCantonTree() {
      if (this.cantonTreeList?.length) return
      getSysCantonTree().then(res => {
        this.cantonTreeList = this.addLocationField(res.data)
      })
    },
    addLocationField(data) {
      return data.map(item => {
        item.locationArr = this.extractLocationArr(item)
        if (item.children?.length) {
          item.children = this.addLocationField(item.children)
        }
        return item
      })
    },
    extractLocationArr(item) {
      if (item.latitude !== undefined && item.longitude !== undefined) {
        return [[item.latitude, item.longitude], item.nameLink, item.code]
      }
      return []
    },
    async listParkingLotByCanton() {
      if(!this.canton) return
      let center = latLngPoint(this.canton[0])
      this.changeCenterPoint(center)
    },
    show(data) {
      if (data) {
        let list = JSON.parse(JSON.stringify(data?.polygonPoint || []))
        this.positionList = list
        this.centerPoint = {
          latitude: data.centerPoint[1],
          longitude: data.centerPoint[0]
        }
      }
      
      TxMap.init().then(TMap => {
        this.initMap()
        if (this.isView) {
          this.disableMap()
        }
        
        // 使用 nextTick 确保 DOM 已经渲染
        this.$nextTick(() => {
          const rectangleEl = document.getElementById('rectangle')
          const circleEl = document.getElementById('circle')
          const polygonEl = document.getElementById('polygon')
          
          if (rectangleEl) rectangleEl.className = 'toolItem'
          if (circleEl) circleEl.className = 'toolItem'
          if (polygonEl) polygonEl.className = 'toolItem active'
        })
      })
    },
    async getLocationFromPlace(place) {
      let joinPlace = place
      if (this.region) {
        const contanName = this.region.length > 0 ? this.region[this.region.length - 1].name : ''
        joinPlace = contanName.replace(/\//g, '') + place
      }
      const res = await getGeoCoderByAddress(joinPlace)
      if (res.status !== 0) {
        showToast(res.message, 'warning')
        return null
      }
      return res.result.location
    },
    async handleAddressLocation() {
      if (!this.place) return
      const isLatLng = isLatitudeLongitude(this.place)
      const centerPlace = isLatLng ? this.place.split(',').map(Number) : await this.getLocationFromPlace(this.place)
      if (centerPlace) {
        this.changeCenterPoint(centerPlace)
      }
    },
    initMap() {
      document.getElementById('toolControl').addEventListener('click', e => {
        var id = e.target.id
        if (id === 'polygon' || id === 'rectangle' || id === 'circle') {
          document.getElementById(activeType).className = 'toolItem'
          document.getElementById(id).className = 'toolItem active'
          activeType = id

          editor.setActiveOverlay(id)
        }
      })
      if (!globalMap) {
        //定义地图中心点坐标
        var center
        var zoom
        if (this.centerPoint) {
          center = new TMap.LatLng(this.centerPoint.latitude, this.centerPoint.longitude)
          zoom = 18.5
        } else {
          center = new TMap.LatLng(39.90812, 116.397484)
          zoom = 15.5
        }
        //定义map变量，调用 TMap.Map() 构造函数创建地图
        let map = new TMap.Map(document.getElementById(this.mapId || 'allmap'), {
          center: center, //设置地图中心点坐标
          zoom: zoom,//设置地图缩放级别
          // baseMap: {  // 设置卫星地图
          //   type: 'satellite'
          // }
          // pitch: 43.5,  //设置俯仰角
          // rotation: 45    //设置地图旋转角度
        })
        if (!map.getControl(TMap.constants.DEFAULT_CONTROL_ID.ZOOM)) { // 如果map上不存在该控件则直接返回
					return;
				}
				map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ZOOM);
       if(!map.getControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION)){
        return
       }
       map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION);
       

        globalMap = map
      }
      this.drawPolygon()
    },

    drawPolygon() {
      let simplePath = []

      if (this.positionList && this.positionList.length > 0) {
        simplePath = this.positionList.map(val => {
          return new TMap.LatLng(val.latitude, val.longitude)
        })
      }

      // 创建多边形 这里也可以直接放进editor里边创建
      if (simplePath.length > 0) {
        polygon = new TMap.MultiPolygon({
          map: globalMap,
          geometries: [
            {
              paths: simplePath
            }
          ]
        })
      } else {
        polygon = new TMap.MultiPolygon({
          map: globalMap
        })
        globalCenterMark = addMarker(globalMap)
      }

      rectangle = new TMap.MultiRectangle({
        map: globalMap
      })
      circle = new TMap.MultiCircle({
          map: globalMap,
        });

      // 编辑器工作模式
      const actionModeEnum = {
        DRAW: TMap.tools.constants.EDITOR_ACTION.DRAW, // 绘制模式，该模式下用户可绘制新图形
        INTERACT: TMap.tools.constants.EDITOR_ACTION.INTERACT // 交互模式，该模式下用户可选中图形进行删除、修改
      }
      const actionType = simplePath.length ? 'INTERACT' : 'DRAW'
      // 创建编辑器
      editor = new window.TMap.tools.GeometryEditor({
        // TMap.tools.GeometryEditor 文档地址：https://lbs.qq.com/webApi/javascriptGL/glDoc/glDocEditor
        map: globalMap, // 编辑器绑定的地图对象
        overlayList: [
          // 可编辑图层 文档地址：https://lbs.qq.com/webApi/javascriptGL/glDoc/glDocEditor#4
          {
            overlay: polygon,
            id: 'polygon'
          },
          {
            overlay: rectangle,
            id: 'rectangle'
          },
          {
            overlay: circle,
            id: 'circle'
          }
        ],
        actionMode: actionModeEnum[actionType],
        activeOverlayId: 'polygon', // 激活图层
        snappable: true, // 开启吸附
        selectable: true // 开启选中 多边形可以进行操作
      })
      editor.on('drawing', e => {
        if (polygon.geometries.length > 0 || rectangle.geometries.length > 0 || circle.geometries.length > 0) {
          showToast('仅允许绘制一个路口图形,若要重新绘制请先删除已有图形', 'warning')
          editor.setActionMode(actionModeEnum.INTERACT)
        }
      })

      // 监听绘制结束事件，获取绘制几何图形
      editor.on('draw_complete', geometry => {
        //id是图形创建时自动生成的唯一值
        const id = geometry.id
        if (globalCenterMark) {
          globalCenterMark.setMap(null)
        }
        
        let center;
        
        //获取到刚创建的多边形
        if (editor.getActiveOverlay().id === 'polygon') {
          const geo = polygon.geometries.filter(item => {
            return item.id === id
          })
          // 绘制完成后不允许绘制第二个多边形 修改为交互模式 允许修改
          editor.setActionMode(actionModeEnum.INTERACT)
          //多边形的点位数据(经纬度)
          this.computeArea = TMap.geometry.computeArea(geo[0].paths).toFixed(2)
          this.$emit('polygonPoint', geo[0].paths, this.computeArea)
          
          // 计算多边形中心点
          center = TMap.geometry.computeCentroid(geo[0].paths);
        }
        
        if (editor.getActiveOverlay().id === 'rectangle') {
          // 获取矩形顶点坐标
          var geo = rectangle.geometries.filter(function (item) {
            return item.id === id
          })
          editor.setActionMode(actionModeEnum.INTERACT)
          this.computeArea = TMap.geometry.computeArea(geo[0].paths).toFixed(2)
          this.$emit('polygonPoint', geo[0].paths, this.computeArea)
          
          // 获取矩形中心点
          center = geo[0].center || TMap.geometry.computeCentroid(geo[0].paths);
        }
        
        if (editor.getActiveOverlay().id === 'circle') {
          // 获取圆形顶点坐标
          var geo = circle.geometries.filter(function (item) {
            return item.id === id
          })
          editor.setActionMode(actionModeEnum.INTERACT)
          const path = calculateCirclePoint(geo[0].center, geo[0].radius)
          this.computeArea = TMap.geometry.computeArea(path).toFixed(2)
          this.$emit('polygonPoint', path, this.computeArea)
          
          // 圆形中心点
          center = geo[0].center;
        }
        
        // 获取中心点的行政区code
        if (center) {
           this.getDistrictByLocation(center);
        }
        
        // if (this.computeArea > 50000) {
        //   showToast('框选面积过大，请进行编辑', 'warning')
        // }
      })
      // 监听删除
      editor.on('delete_complete', evtResult => {
        //删除后修改模式为绘画
        editor.setActionMode(actionModeEnum.DRAW)
        //此处可以获取到被删除的多边形的信息
        // console.log('删除', evtResult.paths)
        this.computeArea = 0
        this.$emit('polygonPoint', evtResult.paths, this.computeArea)
      })
      // 编辑多边形
      editor.on('adjust_complete', evtResult => {
        // console.log('编辑', evtResult.paths)
        console.log(evtResult)
        let center;
        
        if(evtResult.center && evtResult.radius){
          // 圆形
          const path = calculateCirclePoint(evtResult.center, evtResult.radius)
          this.computeArea = TMap.geometry.computeArea(path).toFixed(2)
          console.log(this.computeArea)
          this.$emit('polygonPoint', path, this.computeArea)
         
        } else if(evtResult.center){
          // 矩形
          const points = calculateRectanglePoints(evtResult.center, evtResult.width, evtResult.height)
          console.log(points)
          this.computeArea = TMap.geometry.computeArea(points).toFixed(2)
          console.log(this.computeArea)
          this.$emit('polygonPoint', points, this.computeArea)
          center = evtResult.center; // 矩形中心点
        } else {
          // 多边形
          this.computeArea = TMap.geometry.computeArea(evtResult.paths).toFixed(2)
          console.log(this.computeArea)
          this.$emit('polygonPoint', evtResult.paths, this.computeArea)
          // 计算多边形中心点
          center = TMap.geometry.computeCentroid(evtResult.paths);
        }
        
        // 获取中心点的行政区code
        if (center) {
          this.getDistrictByLocation(center);
        }
        if (this.computeArea > 50000) {
          showToast('框选面积过大，请进行编辑', 'warning')
        }
      })
    },
    handleFullScreen() {
      this.isFull = !this.isFull
    },
    handleDel() {
      editor.delete()
      polygon.setGeometries([])
      rectangle.setGeometries([])
      circle.setGeometries([])
      editor.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW)
      showToast('删除成功', 'success')
      this.computeArea = 0
      this.$emit('polygonPoint', null, 0)
    },
    changeCenterPoint(data) {
      let center
      //设置地图的中心点
      if (globalMap) {
        if (globalCenterMark) {
          globalCenterMark.setMap(null)
        }
        if(Array.isArray(data)){
          center = new TMap.LatLng(data[0], data[1])
        }else{
          center = new TMap.LatLng(data.lat, data.lng)
        }
        globalMap.setCenter(center)
        globalCenterMark = addMarker(globalMap, { position: center })
        editor.setActionMode(TMap.tools.constants.EDITOR_ACTION.DRAW)
      }
    },
    disableMap() {
      editor.disable()
    },
    destroyMap() {
      if (globalMap) {
        globalMap.destroy()
        globalMap = undefined
        this.positionList = []
        this.centerPoint = ''
        this.computeArea = 0
        activeType = 'polygon'
        this.place = ''
      }
    },
    
    // 根据坐标获取行政区信息
    getDistrictByLocation(location) {
      // 使用已有的签名方法获取行政区信息
      let locationStr;
      if (typeof location === 'string') {
        locationStr = location;
      } else {
        // 如果是LatLng对象，转换为字符串格式
        locationStr = location.lat + ',' + location.lng;
      }
      
      // 使用md5计算签名
      let sig = md5(
        `/ws/geocoder/v1?callback=jsonpCallback&key=${GLB_CONFIG.TxMapKey}&location=${locationStr}&output=jsonp${GLB_CONFIG.TxMapSecretKey}`
      );
      
      // 构建请求参数
      let getData = {
        callbackQuery: 'callback', // 设置callback参数的key
        callbackName: 'jsonpCallback', // 设置callback参数的值
        key: GLB_CONFIG.TxMapKey,
        location: locationStr,
        output: 'jsonp',
        sig
      };
      
      // 使用jsonp发送请求
      jsonp('https://apis.map.qq.com/ws/geocoder/v1', getData)
        .then(res => {
          if (res.status === 0) {
            const adInfo = res.result.ad_info;
            const districtCode = adInfo.adcode;
            const addressName = res.result.address
            console.log('中心点行政区code:', districtCode,addressName);
            
            // 发送事件或更新数据
            this.$emit('updateDistrictCode', districtCode,addressName);
          } else {
            console.error('获取行政区失败:', res.message);
            showToast(res.message, 'warning');
          }
        })
        .catch(err => {
          console.error('获取行政区请求失败:', err);
          showToast('获取行政区信息失败', 'error');
        });
    },
  }
}
</script>

<style lang="scss" scoped>
.map-overview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999;
}
.map-dialog {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 390px !important;
  z-index: 99;
}
.map-overview,
.map-dialog {
  .info {
    padding: 0 10px;
    width: 100%;
    height: 25px;
    background-color: #ffffff;
    font-size: 16px;
    .num {
      color: blue;
    }
  }
  .map-container {
    position: relative;
    height: 100%;
  }
  .map-chart {
    position: relative;
    height: 100%;
    width: 100%;
    // z-index: 1;
    user-select: none;

    :deep(.logo-text) {
      display: none !important;
    }
  }

  .search-container {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 1001;
    height: 40px;
    padding: 6px;
    display: flex;
    align-items: center;
    background-color: #ffffff;
  }
  #toolControl {
    position: absolute;
    top: 10px;
//    left: 0px;
    right: 0px;
    margin: auto;
    width: 162px;
    z-index: 1001;
    cursor: pointer;
  }
  .disable-btn {
    opacity: 0.6; /* 使元素看起来像被禁用 */
    pointer-events: none;
  }

  .toolItem {
    width: 30px;
    height: 30px;
    float: left;
    margin: 1px 0;
    padding: 4px;
    border-radius: 3px;
    background-size: 30px 30px;
    background-position: 4px 4px;
    background-repeat: no-repeat;
    box-shadow: 0 1px 2px 0 #e4e7ef;
    background-color: #ffffff;
    border: 1px solid #ffffff;
  }

  .toolItem:hover {
    border-color: #789cff;
  }
  .active {
    border-color: #d5dff2;
    background-color: #d5dff2;
  }
  #delete {
    background-image: url('@/assets/images/delete.png');
    background-position: center center;
  }

  #polygon {
    background-image: url('@/assets/images/polygon.png');
    background-position: center center;
  }

  #rectangle {
    background-image: url('@/assets/images/rectangle.png');
    background-position: center center;
  }
  #circle {
      background-image: url('@/assets/images//circle.png');
      background-position: center center;
    }
  #fullscreen {
    background-image: url('@/assets/images/full-screen.png');
    background-position: center center;
    background-size: 25px 25px;
  }
  #smallscreen {
    background-image: url('@/assets/images/small-screen.png');
    background-position: center center;
    background-size: 25px 25px;
  }
  #circle {
      background-image: url('@/assets/images//circle.png');
      background-position: center center;
    }
}
</style>
