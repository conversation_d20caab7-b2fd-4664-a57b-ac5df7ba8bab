<template>
  <div class="data-card-wrapper" tabindex="0" ref="innerDataPageRef">
    <el-card class="data-card" v-show="!mapResultVisible">
      <el-page-header @back="goBack">
        <template #extra>
          <div :class="addToShoppingCartVisible ? 'flex items-center' : 'flex'">
            <div class="statistic-content">
              <div
                class="statistic-content-item frame-font"
                v-if="infoData?.selectionMode === 'sharding' && infoData?.projectRule?.selectionRuleObj"
              >
                <span class="f-s-12">{{ $t('帧间隔：') }}</span>
                <span class="f-s-12">{{ infoData?.projectRule?.selectionRuleObj?.frameSecond }}秒</span>
              </div>
              <div class="statistic-content-item frame-font" v-if="infoData.selectionMode === 'sharding'">
                <span class="f-s-12">{{ $t('有效分片条件：') }}</span>
                <span class="f-s-12"
                  >{{ infoData?.projectRule?.selectionRuleObj?.minShardFrameAmount }}帧 -
                  {{ infoData.projectRule?.selectionRuleObj?.maxShardFrameAmount }}帧</span
                >
              </div>
              <div class="statistic-content-item">
                <span class="f-s-12">{{ $t('数据数量：') }}</span>
                <span class="f-s-12">{{ statisticData?.dataAmount || 0 }}</span>
              </div>
              <div class="statistic-content-item">
                <span class="f-s-12">{{ $t('数据帧数量：') }}</span>
                <span class="f-s-12">{{ statisticData?.measurementAmount || 0 }}</span>
              </div>
            </div>
            <el-button type="primary" size="small" @click="handleInnerRefreshClick" class="refresh-button"
              >{{ $t('刷新') }}
            </el-button>
            <span class="f-s-12">{{ $t('列') }}</span>
            <el-select
              v-model="splitNum"
              placeholder="Select"
              size="small"
              class="column-content"
              @change="innerRefresh"
            >
              <el-option v-for="item in colNumList" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
        </template>
      </el-page-header>
      <data-shopping-cart
        ref="dataShoppingCartRef"
        :data-operate-permission="dataOperatePermission"
        :menu-type="DATA_SET_TYPE.LABELING"
        :dataset-id="datasetId"
        :data-type="datasetType"
        :scenario-list="infoData.scenarioList"
        :frame-sync="infoData.frameSync"
        :modality-list="infoData.mainViewDictList"
        :is-checking="finishCheckVisible || finishAcceptanceVisible"
        v-model="currentShoppingCartData.length"
        @card-data-restore="restore"
        @retention-confirm="retentionConfirm"
        @pre-retain="preRetain"
        @handle-drawer-closed="handleDrawerClosed"
        @clean-shopping-cart="clean"
        v-if="addToShoppingCartVisible"
      ></data-shopping-cart>
      <data-recycle-bin
        ref="dataRecycleBinRef"
        :data-operate-permission="dataOperatePermission"
        :dataset-id="datasetId"
        :scenario-list="infoData.scenarioList"
        :frame-sync="infoData.frameSync"
        :modality-list="infoData.mainViewDictList"
        :data-clip-id="infoData.selectionMode === 'sharding' ? datasetClipsList[activeClipIndex]?.id : ''"
        :is-whole-second="isWholeSecond"
        v-model="currentRecycleBinData.length"
        @card-data-restore="restore"
        @batch-remove="batchRemove"
        @handle-drawer-closed="handleDrawerClosed"
        @clean-recycle-bin="clean"
        v-if="addToShoppingCartVisible"
      ></data-recycle-bin>
      <el-divider style="margin: 10px 0" />
      <div class="ltw-page-container">
        <info-data-description v-model="infoData"></info-data-description>
        <div class="ltw-toolbar">
          <div class="middle-button">
            <el-button @click="filterButtonClick($event)" class="filter-button m-r-15" size="small">
              <ltw-icon icon-code="el-icon-filter"></ltw-icon>
              <span>{{ $t('筛选') }}</span>
            </el-button>
            <el-radio-group v-model="innerQueryParam.mutiSortField" @change="innerRefresh" class="m-r-15" size="small">
              <el-radio label="measurement asc">{{ $t('正序') }}</el-radio>
              <el-radio label="measurement desc">{{ $t('倒序') }}</el-radio>
            </el-radio-group>
            <template v-if="infoData.frameSync">
              <span>主视图</span>
              <labeled-dataset-modality-selection
                :data="infoData.mainViewDictList"
                v-model="modalitySelected"
                size="small"
                clearable
                filterable
                :auto-load="false"
              >
              </labeled-dataset-modality-selection>
            </template>
            <el-button
              :type="dataOperatePermission['switchWholeSecond'].buttonStyleType"
              @click="executeButtonMethod($event, dataOperatePermission['switchWholeSecond'])"
              v-if="
                dataOperatePermission['switchWholeSecond'] &&
                infoData?.projectRule?.selectionRuleObj?.frameSecond === 0.1
              "
              :class="{ 'is-whole-second': isWholeSecond === true }"
              style="margin-left: 12px"
            >
              <ltw-icon :icon-code="dataOperatePermission['switchWholeSecond'].buttonIconCode"></ltw-icon>
              {{ $t(dataOperatePermission['switchWholeSecond'].name) }}
            </el-button>
          </div>
          <div class="middle-button">
            <el-button type="warning" size="small" plain v-if="startUpdateTagFlag">
              <el-icon class="is-loading">
                <Loading />
              </el-icon>
              {{ $t('补打tag中') }}
            </el-button>
          </div>
          <div class="right-button">
            <template v-if="Object.keys(dataOperatePermission)?.length">
              <template v-for="item in Object.keys(dataOperatePermission)" :key="item">
                <el-button
                  :type="dataOperatePermission[item].buttonStyleType"
                  @click="executeButtonMethod($event, dataOperatePermission[item])"
                  v-if="
                    Object.values(DATASET_INNER_DATA_OUTLINE_FUNC).includes(item) &&
                    dataOperatePermission[item] &&
                    checkPermission(item)
                  "
                >
                  <ltw-icon :icon-code="dataOperatePermission[item].buttonIconCode"></ltw-icon>
                  {{ $t(dataOperatePermission[item].name) }}
                </el-button>
              </template>
            </template>
          </div>
        </div>
        <div class="container">
          <el-aside
            :class="!collapsed ? 'aside' : 'aside collapsed-aside'"
            v-if="infoData.selectionMode === 'sharding'"
          >
            <el-card class="sider-container">
              <div class="clip-button-content">
                <el-row>
                  <!--                <span class="status-txt">状态</span>-->
                  <dictionary-selection
                    v-model="clipQueryParam.status"
                    clearable
                    dictionaryType="dataset_data_clip_status"
                    :placeholder="$t('请选择状态')"
                    filterable
                    style="width: 115px"
                    size="small"
                    @change="clipRefresh"
                  />
                  <el-select
                    v-model="clipQueryParam.vin"
                    :placeholder="$t('请选择车辆')"
                    style="width: 115px"
                    @change="clipRefresh"
                    clearable
                    filterable
                  >
                    <el-option v-for="item in vehicleList" :key="item" :label="item" :value="item" />
                  </el-select>
                </el-row>
              </div>
              <el-row class="clip-button-container">
                <div>
                  <el-checkbox
                    v-model="checkAllClip"
                    :indeterminate="isClipIndeterminate"
                    @change="handleCheckAllClipChange"
                  >
                    {{ $t('全选') }}
                  </el-checkbox>
                  <el-popover placement="top-start" :width="150" trigger="click">
                    <template #reference>
                      <el-icon style="cursor: pointer; margin-left: 5px">
                        <QuestionFilled />
                      </el-icon>
                    </template>
                    <div class="tip-container">
                      <div class="shortcut-key" v-for="item in Object.keys(shortcutKeyMap)" :key="item">
                        <div class="shortcut-key-button">{{ item }}</div>
                        <div class="shortcut-key-text">{{ $t(shortcutKeyMap[item]) }}</div>
                      </div>
                    </div>
                  </el-popover>
                </div>
                <el-dropdown @command="handleCommand" class="batch-operate-btn" v-if="batchingFunctionList?.length">
                  <el-button type="primary" size="small">
                    更多
                    <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <template v-for="item in Object.keys(dataOperatePermission)" :key="item">
                        <el-dropdown-item
                          :command="dataOperatePermission[item].buttonCode"
                          v-if="Object.values(CLIP_BATCH_FUNC).includes(item) && dataOperatePermission[item]"
                        >
                          <ltw-icon :icon-code="dataOperatePermission[item].buttonIconCode"></ltw-icon>
                          {{ $t(dataOperatePermission[item].name) }}
                        </el-dropdown-item>
                      </template>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-row>
              <div class="clip-list-container">
                <el-scrollbar height="100%">
                  <div class="clip-container" v-for="(item, index) in datasetClipsList" :key="item.id">
                    <el-row>
                      <el-checkbox
                        v-model="item.checked"
                        @change="handleCheckedClipChange(item.checked, item)"
                      ></el-checkbox>
                      <el-tooltip
                        effect="dark"
                        :content="$t(item.statusName) + ' ' + item.vin"
                        placement="top"
                        :enterable="false"
                      >
                        <el-button
                          @click="getCurrentClipPageData(index)"
                          size="small"
                          :class="{ 'is-active': index == activeClipIndex }"
                        >
                          <div class="time-content">
                            <status-link :status="item.status" :text="item.startTime" />
                            <status-link :status="item.status" :text="item.endTime" />
                            <ltw-icon
                              icon-code="el-icon-circle-check"
                              style="position: absolute; color: #67c23a; top: 2px; right: 2px"
                              v-if="item.sendStatus==='sent'"
                            ></ltw-icon>
                          </div>
                          <el-divider direction="vertical" />
                          <StatusLink :status="item.status" :text="`${item.frameAmount || 0} 帧`" />
                          <el-divider direction="vertical" v-show="collapsed" />
                          <el-scrollbar height="100%" class="scenario-txt-container" v-show="collapsed">
                            <status-link :status="item.status" :text="item.scenarioName || '-'" class="scenario-txt" />
                          </el-scrollbar>
                        </el-button>
                      </el-tooltip>
                    </el-row>
                  </div>
                </el-scrollbar>
              </div>
              <div class="toggle-button" :class="{ collapsed: collapsed }" @click="toggle">
                <ltw-icon icon-code="el-icon-expand"></ltw-icon>
              </div>
            </el-card>
          </el-aside>
          <div class="data-container">
            <el-row class="header-toolbar">
              <el-row :class="infoData.selectionMode === 'sharding' ? 'sharding-row-left' : 'row-left'">
                <el-checkbox
                  @change="handleAllClick"
                  v-model="allChecked"
                  :indeterminate="isIndeterminate"
                  :disabled="innerPageData.records?.length == 0 || activeClip?.status === 'invalid'"
                >
                  <el-link>{{ $t('全选') }}</el-link>
                </el-checkbox>
                <el-tabs
                  v-model="scenarioTabName"
                  class="tabs-container"
                  @tab-change="handleTabChange"
                  v-if="scenarioList?.length && infoData.selectionMode !== 'sharding'"
                >
                  <el-tab-pane
                    :label="item.name + '(' + item.quantity + ')'"
                    :name="item.code"
                    v-for="item in scenarioList"
                    :key="item.name"
                  >
                  </el-tab-pane>
                </el-tabs>
                <template v-if="infoData.selectionMode === 'sharding'">
                  <div class="clip-tag-container" v-show="activeClip?.tagList?.length">
                    <el-tag
                      v-for="tag in activeClip.tagList"
                      :key="tag.id"
                      :type="checkTagType(tag)"
                      class="tag-container"
                      >{{ tag.name }}
                    </el-tag>
                  </div>
                </template>
              </el-row>
              <el-row
                class="button-container"
                v-if=" infoData.selectionMode === 'sharding' && activeClip.status !== 'invalid'"
              >
                <template v-for="item in Object.keys(dataOperatePermission)" :key="item">
                  <el-button
                    :type="dataOperatePermission[item].buttonStyleType"
                    @click="executeButtonMethod($event, dataOperatePermission[item])"
                    v-if="
                      Object.values(CLIP_OUTLINE_FUNC).includes(item) &&
                      dataOperatePermission[item] &&
                      checkPermission(item)
                    "
                  >
                    <ltw-icon :icon-code="dataOperatePermission[item].buttonIconCode"></ltw-icon>
                    {{ $t(dataOperatePermission[item].name) }}
                  </el-button>
                </template>
              </el-row>
            </el-row>
            <div
              class="file-sub-container"
              :load-more-total="Math.ceil(innerPageData?.records?.length / splitNum)"
              :load-more-selector="'.el-row'"
              :reload="!unloading"
              v-load-more="throttle(handleLoadMore)"
              id="fix-chrome-bug"
            >
              <el-scrollbar>
                <frame-data-page
                  v-model="displayData"
                  type="data"
                  :splitNum="splitNum"
                  :disabled="activeClip.status === 'invalid' || allChecked"
                  @item-checked="handleChecked"
                  v-if="
                    (updateTagFlag ? !updateTagFlag : !infoData.frameSync) &&
                    [
                      DATA_TYPE.MIXED_DATA,
                      DATA_TYPE.FRAME_DATA,
                      DATA_TYPE.LABELED_RESULT,
                      DATA_TYPE.LABELED_DATA
                    ].includes(datasetType)
                  "
                ></frame-data-page>
                <bev-frame-data-page
                  v-model="displayData"
                  :split-num="splitNum"
                  :disabled="activeClip.status === 'invalid' || allChecked"
                  @item-checked="handleChecked"
                  v-if="
                    (updateTagFlag ? !updateTagFlag : !infoData.frameSync) && datasetType === DATA_TYPE.BEV_FRAME_DATA
                  "
                ></bev-frame-data-page>
                <sync-frame-data-page
                  v-model="displayData.records"
                  :modality-selected="modalitySelected"
                  :split-num="splitNum"
                  :dataset-type="datasetType"
                  :dataset-id="datasetId"
                  :inner-query-param="innerQueryParam"
                  :shortcuts-map="shortcutsMap"
                  :disabled="activeClip?.status === 'invalid' || allChecked"
                  @card-checked="handleChecked"
                  @get-info-detail="getInfoDetail"
                  v-if="updateTagFlag ? updateTagFlag : infoData.frameSync"
                  ref="syncDataPageRef"
                ></sync-frame-data-page>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
  <data-filter-panel :dataType="datasetType" @filter="filter" ref="filterRef"></data-filter-panel>
  <download-pannel
    :datasetType="datasetType"
    @download-pannel-closed="downloadPanelClosed"
    ref="downloadRef"
  ></download-pannel>
  <tag-record
    ref="tagRecordRef"
    :datasetId="datasetId"
    @addTag="addTag"
    @start-update-tag="startUpdateTag"
    @finish-update-tag="finishUpdateTag"
    @shortcut-save="shortcutSave"
  ></tag-record>
  <scenario-selector
    ref="scenarioRef"
    :scenario-list="checkedScenarioList"
    @confirm="confirmScenario"
  ></scenario-selector>
  <sync-frame-data-card-detail
    ref="syncDataFrameCardDetailRef"
    :model-value="innerPageData.records"
    :data-operate-permission="dataOperatePermission"
    :scenario-list="checkedScenarioList"
    :writable="writable"
    @get-next-page-data="getNextPageData"
    @add-data-to-shopping-cart="addDataToShoppingCart"
  ></sync-frame-data-card-detail>
  <scenario-config ref="scenarioConfigRef" :dataset-id="infoData.id" @confirm="scenarioConfigConfirm"></scenario-config>
  <preview-routes
    :acquisition-type="infoData.acquisitionType"
    :dataset-id="datasetId"
    v-if="mapVisible"
    @exit="mapExit"
    ref="previewRouteRef"
  ></preview-routes>
  <map-result-page
    v-if="mapResultVisible"
    :dataset-id="datasetId"
    :recordsData="recordsData"
    :status="infoData.status"
    @goBackMainPage="goBackMainPage"
  ></map-result-page>
  <scenario-distribution
    :colors="chartsColors"
    :selectionMode="infoData.selectionMode"
    ref="scenarioDistributionRef"
  ></scenario-distribution>
  <clip-selection-workbench
    :dataset-name="infoData.name"
    :image-route-clip-map="imageRouteMap"
    :colors="chartsColors"
    :process-type="processType"
    ref="clipSelectionWorkbenchRef"
    v-if="clipSelectionVisible"
    @go-back="goBackMainPage"
  ></clip-selection-workbench>
  <bs-tag-group-drawer
    :drawerVisible="tagDistributeDrawerVisible"
    :rowTagList="rowTagList"
    @drawerClick="confirmDistributeTags"
  ></bs-tag-group-drawer>
</template>
<script>
import {
  debounce,
  checkTagType,
  checkFileSize,
  numUtils,
  showToast,
  showConfirmToast,
  throttle,
  batchDeleteByMeasurement,
  batchDeleteById
} from '@/plugins/util'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {
  queryInnerData,
  queryInnerMeasurementData,
  dataStatistic,
  deleteInvalidSlips,
  getScenarioDistribution
} from '@/apis/data/data_set'
import { preRetentionPageDataQuery } from '@/apis/shoppingCart/shopping_cart'
import { getDmDataset, getProcessResultsDetail } from '@/apis/data/dm-dataset'
import {
  startManualSelection,
  finishManualSelection,
  startManualCheck,
  finishManualCheck,
  preSplitClip,
  splitClip,
  markUpScenario,
  batchMarkUpScenario, updateClipTags
} from '@/apis/data/labeling-dataset'
import InfoDataDescription from '@/components/dataset/labeledDataset/InfoDataDescription.vue'
import FrameDataPage from '@/components/data/frameData/FrameDataPage.vue'
import SyncDataPage from '@/components/data/syncData/SyncDataPage.vue'
import SyncFrameDataPage from '@/components/data/syncData/SyncFrameDataPage.vue'
import DownloadPannel from '@/components/data/basic/DownloadPannel.vue'
import TagFrameDataDrawer from '@/components/dataset/basic/TagFrameDataDrawer.vue'
import util from '@/plugins/util'
import LoadMore from '@/directives/loadMore'
import DATA_SET_TYPE from '@/plugins/constants/data-set-type'
import DATA_TYPE from '@/plugins/constants/data-type'
import Drag from '@/directives/drag'
import ShoppingCart from '@/pages/data/dialog/ShoppingCart.vue'
import FtmVehicleModalitySelection from '@/components/fleet/FtmVehicleModalitySelection.vue'
import TagShortCutsConfig from '@/components/dataset/labeledDataset/TagShortCutsConfig.vue'
import TagRecord from '@/components/dataset/labeledDataset/TagRecord.vue'
import DataShoppingCart from '@/components/data/shoppingCart/DataShoppingCart.vue'
import ScenarioSelector from '@/components/dataset/labeledDataset/ScenarioSelector.vue'
import SyncFrameDataCardDetail from '@/components/data/syncData/SyncFrameDataCardDetail.vue'
import LabeledDatasetModalitySelection from '@/components/dataset/labeledDataset/LabeledDatasetModalitySelection.vue'
import DataRecycleBin from '@/components/data/dataRecycleBin/DataRecycleBin.vue'
import BevFrameDataPage from '@/components/data/bevFrameData/BevFrameDataPage.vue'
import dataType from '@/plugins/constants/data-type'
import { listDatasetClips, updateClip } from '@/apis/data/labeling-dataset'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import DataFilterPanel from '@/components/data/basic/DataFilterPanel.vue'
import { ElMessageBox } from 'element-plus'
import { h } from 'vue'
import { queryShoppingCartData, addShoppingCartData } from '@/apis/shoppingCart/shopping_cart'
import {
  CLIP_OUTLINE_FUNC,
  DATA_SET_STATUS,
  DATASET_INNER_DATA_OUTLINE_FUNC,
  CLIP_BATCH_FUNC
} from '@/plugins/constants/data-dictionary'
import { addRecycleBinData, queryRecycleBinData } from '@/apis/recycle/recycle_bin'
import ScenarioConfig from '@/components/dataset/labeledDataset/SenarioConfig.vue'
import StatusLink from '@/components/dataset/labeledDataset/StatusLink.vue'
import PreviewRoutes from '@/components/dataset/labeledDataset/PreviewRoutes.vue'
import BsVehicleSelection from '@/components/basic/BsVehicleSelection.vue'
import MapResultPage from '@/components/dataset/labeledDataset/MapResultPage.vue'
import { getProcessResult } from '@/apis/geography/map-result'
import ScenarioDistribution from '@/components/dataset/labeledDataset/ScenarioDistribution.vue'
import ClipSelectionWorkbench from '@/components/geography/ClipSelectionWorkbench.vue'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
// import test from '@/components/geography/test.json'
// import test2 from '@/components/geography/test2.json'
// import test3 from '@/components/geography/test3.json'

const defaultFormData = {}
export default {
  name: 'InnerDataPage',
  components: {
    BsTagGroupDrawer,
    ClipSelectionWorkbench,
    ScenarioDistribution,
    MapResultPage,
    BsVehicleSelection,
    PreviewRoutes,
    ScenarioConfig,
    DataFilterPanel,
    DictionarySelection,
    BevFrameDataPage,
    DataRecycleBin,
    LabeledDatasetModalitySelection,
    SyncFrameDataCardDetail,
    InfoDataDescription,
    ScenarioSelector,
    DataShoppingCart,
    FrameDataPage,
    SyncDataPage,
    SyncFrameDataPage,
    DownloadPannel,
    TagFrameDataDrawer,
    ShoppingCart,
    FtmVehicleModalitySelection,
    TagShortCutsConfig,
    TagRecord,
    StatusLink
  },
  props: {
    dataOperatePermission: {
      type: Object,
      default: {}
    },
    batchingFunctionList: {
      type: Array,
      default: []
    },
    writable: {
      type: Boolean,
      default: false
    },
    datasetId: {
      type: String,
      default: ''
    },
    updatable: {
      type: Boolean,
      default: false
    },
    chartsColors: {
      type: Array,
      default: []
    },
    datasetName: {
      type: String,
      default: ''
    }
  },
  emits: ['goBack'],
  data() {
    return {
      DATA_SET_TYPE,
      DATA_TYPE,
      DATASET_INNER_DATA_OUTLINE_FUNC,
      CLIP_OUTLINE_FUNC,
      CLIP_BATCH_FUNC,
      numUtils: numUtils,
      checkFileSize: checkFileSize,
      checkTagType: checkTagType,
      throttle: throttle,
      debounce: debounce,
      batchDeleteByMeasurement: batchDeleteByMeasurement,
      batchDeleteById: batchDeleteById,
      innerPageData: {
        records: [],
        total: 0
      },
      innerQueryParam: {
        mutiSortField: 'measurement desc',
        current: 1,
        size: 400
      },
      unloading: false,
      splitNum: 4,
      colNumList: [1, 2, 3, 4, 5],
      // datasetId: '',
      datasetType: '',
      selectedData: [],
      currentStartIndex: 0,
      currentEndIndex: 20,
      allChecked: false,
      isIndeterminate: false,
      idList: [],
      syncVisible: true,
      syncPageData: Object.assign({}, defaultFormData),
      syncFramePageData: {
        records: []
      },
      modalitySelected: '',
      infoData: Object.assign({}, defaultFormData),
      operationFlag: true,
      currentSelectedData: [],
      currentUser: {},
      shortcutsMap: {},
      allCheckBoxChecked: false,
      //只读模式
      dataWritable: false,
      readonly: true,
      scenarioSelected: '',
      scenarioTabName: '',
      scenarioList: [],
      lastMeasurement: '',
      startIndex: 0,
      currentRecycleBinData: [],
      currentShoppingCartData: [],
      statisticData: {},
      updateTagFlag: false,
      startUpdateTagFlag: false,
      datasetClipsList: [],
      activeClip: Object.assign({}, defaultFormData),
      activeClipIndex: 0,
      clipQueryParam: {},
      vehicleList: [],
      collapsed: false,
      checkedScenarioList: [],
      checkAllClip: false,
      checkedClips: [],
      isClipIndeterminate: false,
      isWholeSecond: false,
      mapVisible: false,
      shouldContinueProcessing: true,
      isInvalidConfirmDialogOpen: false,
      shortcutKeyMap: {
        W: '上一个切片',
        S: '下一个切片',
        Q: '切片无效',
        E: '更新场景'
      },
      recordsData: [],
      mapResultVisible: false,
      clipSelectionVisible: false,
      imageRouteClipList: [],
      imageRouteMap: new Map(),
      processType: '',
      tagDistributeDrawerVisible:false,
      tagList:[],
      rowTagList:[],
    }
  },
  created() {
    this.dataWritable = this.writable
    this.currentUser = this.$store.state.permission.currentUser
    let _this = this
    window.addEventListener('resize', debounce(_this.changeSize))
    this.changeSize()
    this.shortcutsMap = JSON.parse(localStorage.getItem('shortcuts'))
  },
  directives: {
    LoadMore,
    Drag
  },
  async mounted() {
    let _this = this
    await this.resetInfoData(this.datasetId, false)
    if (this.infoData?.scenarioList?.length) {
      let config = JSON.parse(localStorage.getItem(this.datasetId + 'config')) || []
      if (config?.length) {
        this.checkedScenarioList = config.filter(item => this.infoData.scenarioList.some(scena => scena.id === item.id))
      } else {
        this.checkedScenarioList = this.infoData.scenarioList
      }
      localStorage.setItem(this.datasetId + 'config', JSON.stringify(this.checkedScenarioList))
    }
    let flag =
      (this.currentUser.empId == this.infoData.manualSelectionEmpId ||
        this.currentUser.empId == this.infoData.selectionCheckEmpId) &&
      this.dataWritable
    if (flag) {
      await this.getLocalStorageData() //获取回收站以及购物车数据
      if (this.currentShoppingCartData?.length || this.currentRecycleBinData?.length) {
        showConfirmToast({
          message: BASE_CONSTANT.SELECTED_DATA_CONFIRM_MSG
        }).then(res => {})
      }
    }
    this.resetInnerPageData()
    this.$refs.innerDataPageRef.focus()
    if (this.infoData.selectionMode === 'sharding') {
      this.$refs.innerDataPageRef.addEventListener('keydown', debounce(_this.handleKeydown))
      this.handleKeydown()
    }
  },
  beforeUnmount() {
    let _this = this
    window.removeEventListener('resize', debounce(_this.changeSize))
    if (this.infoData.selectionMode === 'sharding') {
      this.$refs.innerDataPageRef?.removeEventListener('keydown', debounce(_this.handleKeydown))
    }
  },
  watch: {},
  computed: {
    dataType() {
      return dataType
    },
    displayData() {
      let records
      let selectedShoppingCartParams = this.currentShoppingCartData
      let selectedRecycleBinParams = this.currentRecycleBinData
      records = this.innerPageData?.records?.filter((item, index) => {
        if (!item.checked) {
          item.checked = false
        }
        if (!this.allChecked && !this.isIndeterminate) {
          item.checked = false
        }
        let idList = []
        if (this.infoData.frameSync) {
          let measurementList = selectedShoppingCartParams.concat(selectedRecycleBinParams)
          let flag = measurementList.some(listItem => {
            return listItem.measurement === item.measurement && listItem.vin === item.vin
          })
          if (flag || item.preOperationMark) {
            item.checked = true
            item.operationFlag = true
          }
        } else {
          idList = selectedShoppingCartParams.concat(selectedRecycleBinParams)
        }
        if (!this.infoData.frameSync && (idList.includes(item.id) || item.preOperationMark)) {
          item.checked = true
          item.operationFlag = true
        }
        if (index < this.currentStartIndex || index > this.currentEndIndex) {
          return false
        } else {
          return true
        }
      })
      return { records }
    },
    initPageLength() {
      return this.innerPageData?.records?.length < 50 ? this.innerPageData?.records?.length / 2 : 25
    },
    startSelectVisible() {
      return (
        this.checkStartVisible(DATA_SET_STATUS.TO_BE_SELECTED) ||
        this.checkStartVisible(DATA_SET_STATUS.SLAM_MAP_SUCCESS)
      )
    },
    startCheckVisible() {
      return this.checkStartVisible(DATA_SET_STATUS.SELECTED)
    },
    startAcceptanceVisible() {
      return this.checkStartVisible(DATA_SET_STATUS.TO_BE_ACCEPTANCE)
    },
    finishSelectVisible() {
      return (
        this.checkFinishVisible(DATA_SET_STATUS.SELECTING, this.infoData.manualSelectionEmpId) ||
        this.checkFinishVisible(DATA_SET_STATUS.SLAM_MAP_SUCCESS, this.infoData.manualSelectionEmpId)
      )
    },
    finishCheckVisible() {
      return this.checkFinishVisible(DATA_SET_STATUS.CHECKING, this.infoData.selectionCheckEmpId)
    },
    finishAcceptanceVisible() {
      return this.checkFinishVisible(DATA_SET_STATUS.UNDER_ACCEPTANCE, this.infoData.selectionCheckEmpId)
    },
    addToShoppingCartVisible() {
      if (this.finishSelectVisible || this.finishCheckVisible || this.finishAcceptanceVisible) {
        return true
      }
      return false
    }
  },
  methods: {
    checkPermission(item) {
      const funcHandlers = {
        [DATASET_INNER_DATA_OUTLINE_FUNC.START_SELECT]: () => this.startSelectVisible,
        [DATASET_INNER_DATA_OUTLINE_FUNC.FINISH_SELECT]: () => this.finishSelectVisible,
        [DATASET_INNER_DATA_OUTLINE_FUNC.START_CHECK]: () => this.startCheckVisible,
        [DATASET_INNER_DATA_OUTLINE_FUNC.FINISH_CHECK]: () => this.finishCheckVisible,
        [DATASET_INNER_DATA_OUTLINE_FUNC.ADD_DATA_TO_RECYCLE_BIN]: () => this.addToShoppingCartVisible,
        [DATASET_INNER_DATA_OUTLINE_FUNC.ADD_DATA_TO_CART]: () =>
          this.addToShoppingCartVisible && this.infoData.selectionMode !== 'sharding',
        [DATASET_INNER_DATA_OUTLINE_FUNC.FULL_UPDATE_SCENARIO]: () => this.infoData.selectionMode !== 'sharding',
        [CLIP_OUTLINE_FUNC.UPDATE_SCENARIO]: () => this.activeClip.status === 'valid' &&
            (this.finishCheckVisible || this.finishSelectVisible),
        [CLIP_OUTLINE_FUNC.AUTO_SPLIT_CLIP]: () =>
          this.infoData.selectionMode === 'sharding' &&
          (this.finishCheckVisible || this.finishSelectVisible) &&
          this.activeClip.status === 'to_be_sliced',
        [CLIP_OUTLINE_FUNC.INVALID_CLIP]: () => this.finishCheckVisible || this.finishSelectVisible,
        [DATASET_INNER_DATA_OUTLINE_FUNC.SCENARIO_CONFIG]: () => this.infoData.scenarioList?.length > 0,
        [DATASET_INNER_DATA_OUTLINE_FUNC.PREVIEW_ROUTES]: () => this.infoData.selectionMode === 'sharding',
        [DATASET_INNER_DATA_OUTLINE_FUNC.QUALITY_CHECK_CLIP]: () =>
          ['parking_lot_slam', 'camera_seg', 'parking_slam_cam_seg', 'parking_slam_rgb', 'parking_slam_intense'].some(
            type => this.datasetName.includes(type)
          )
      }
      return funcHandlers[item] ? funcHandlers[item]() : true
    },
    executeButtonMethod(event, button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    changeSize() {
      if (window.innerWidth >= 1920) {
        this.splitNum = 4
        this.colNumList = [1, 2, 3, 4, 5, 6]
      } else if (window.innerWidth >= 1200) {
        this.splitNum = 3
        this.colNumList = [1, 2, 3, 4, 5]
      } else if (window.innerWidth >= 992) {
        this.splitNum = 2
        this.colNumList = [1, 2]
      } else if (window.innerWidth >= 768) {
        this.splitNum = 2
        this.colNumList = [1, 2]
      } else {
        this.splitNum = 1
        this.colNumList = [1]
      }
    },
    viewMapResult() {
      getProcessResult(this.datasetId).then(res => {
        if (res.data) {
          this.recordsData = res.data
        } else {
          this.recordsData = null
        }
        this.mapResultVisible = true
      })
    },
    goBackMainPage() {
      this.$refs.innerDataPageRef.focus()
      this.mapResultVisible = false
      this.clipSelectionVisible = false
      this.clipRefresh()
    },
    qualityCheckClip() {
      getProcessResultsDetail(this.datasetId).then(res => {
        // let res = {}
        // res.data = result
        if (!res.data?.dataList?.[0].clipVOList?.length) return showToast('暂无数据', 'warning')
        this.imageRouteClipList = res.data.dataList[0].clipVOList
        this.processType = res.data.processType
        this.imageRouteClipList.forEach(item => {
          this.imageRouteMap.set(item.layer, item)
        })
        this.clipSelectionVisible = true
      })
    },
    checkStartVisible: function (status) {
      if (this.infoData.status === status) {
        return this.dataWritable
      }
      return false
    },
    checkFinishVisible: function (status, empId) {
      if (this.infoData.status === status && this.currentUser.empId === empId) {
        return true
      }
      return false
    },
    viewScenarioDistribution() {
      getScenarioDistribution(this.datasetId).then(res => {
        if (!res.data?.length) return showToast('该数据集暂无场景分布', 'warning')
        this.$refs.scenarioDistributionRef.show(res.data)
      })
    },
    //todo
    goBack() {
      this.shouldContinueProcessing = false
      this.initInnerParam()
      this.initInnerProps()
      this.$emit('goBack', this.infoData.status, this.infoData.statusName)
    },
    toggle() {
      this.collapsed = !this.collapsed
    },
    async listClips() {
      await listDatasetClips({ datasetId: this.datasetId }).then(res => {
        this.datasetClipsList = res.data
        const vinList = res.data.map(item => item.vin)
        this.vehicleList = [...new Set(vinList)]
        if (this.datasetClipsList?.length) {
          if (this.activeClipIndex == 0) {
            this.activeClip = res.data[0]
          }
          this.resetParamsOfCurrentClips(this.activeClipIndex)
        }
      })
    },
    clipRefresh() {
      listDatasetClips({ datasetId: this.datasetId, ...this.clipQueryParam }).then(res => {
        this.datasetClipsList = res.data
        this.datasetClipsList.forEach(item => (item.checked = false))
        if (this.datasetClipsList?.length) {
          this.activeClipIndex = 0
          this.resetParamsOfCurrentClips(0)
          this.innerRefresh()
        } else {
          this.innerPageData = { records: [] }
        }
      })
    },
    getCurrentClipPageData(index) {
      if (this.currentRecycleBinData?.length && this.activeClipIndex !== index) {
        showToast('请清空回收站', 'warning')
        return
      }
      if (this.activeClipIndex == index) return
      this.resetParamsOfCurrentClips(index)
      this.innerRefresh()
    },
    invalidClip() {
      this.isInvalidConfirmDialogOpen = true
      showConfirmToast({
        message: BASE_CONSTANT.UPDATE_CLIP_MSG
      })
        .then(res => {
          this.isInvalidConfirmDialogOpen = false
          updateClip({ id: this.activeClip.id, status: 'invalid' }).then(res => {
            this.activeClip.status = 'invalid'
            this.activeClip.statusName = '无效'
          })
        })
        .catch(() => {
          this.isInvalidConfirmDialogOpen = false
        })
    },
    autoSplitClip() {
      preSplitClip(this.activeClip.id).then(res => {
        let clipList = res.data
        ElMessageBox({
          message: h('p', null, [
            h('p', null, '该切片将会分为'),
            ...clipList.map(item =>
              h('p', { style: 'font-size:12px' }, item.startTime + ' —— ' + item.endTime + ' ： ' + item.frameAmount)
            ),
            h('p', null, '是否继续')
          ]),
          showCancelButton: true,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(res => {
          splitClip({
            id: this.activeClip.id,
            splitDataClips: clipList
          }).then(res => {
            this.clipRefresh()
            this.resetInfoData(this.datasetId, true)
            showToast('分片完成', 'success')
          })
        })
      })
    },
    resetClipList(clipList) {
      let index = this.datasetClipsList.findIndex(item => {
        return item.id === this.activeClip.id
      })
      this.datasetClipsList.splice(index, 1)
      // this.datasetClipsList.splice(index,0,...clipList)
      showToast('分片完成', 'success')
    },
    handleCheckAllClipChange() {
      this.datasetClipsList.forEach(item => {
        item.checked = this.checkAllClip
      })
      this.checkedClips = this.checkAllClip ? this.datasetClipsList : []
      this.isClipIndeterminate = false
    },
    handleCheckedClipChange(checked, val) {
      if (checked) {
        this.checkedClips.push(val)
      } else {
        this.checkedClips = this.checkedClips.filter(clip => clip.id !== val.id)
      }
      const checkedCount = this.checkedClips.length
      this.checkAllClip = checkedCount === this.datasetClipsList.length
      this.isClipIndeterminate = checkedCount > 0 && checkedCount < this.datasetClipsList.length
    },
    async handleInnerRefreshClick() {
      if (this.infoData.selectionMode === 'sharding') {
        await this.listClips()
      }
      this.resetInfoData(this.datasetId, true)
      this.innerRefresh()
    },
    remove(param) {
      deleteInvalidSlips(param).then(res => {
        this.handleInnerRefreshClick()
      })
    },
    handleCommand(command) {
      if (command === 'batchRemoveList') {
        this.batchRemoveList()
      }
      if (command === 'batchUpdateClipScenario') {
        this.batchUpdateClipScenario()
      }
    },
    batchRemoveList() {
      let msg = '此操作将永久删除不合格切片，是否继续?'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.remove({ datasetId: this.datasetId })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    batchUpdateClipScenario() {
      if (!this.checkedClips?.length) return showToast('请选择切片', 'warning')
      this.$refs.scenarioRef?.show(true)
    },
    removeSingle(id) {
      let msg = '此操作将永久删除当前切片，是否继续?'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.remove({ datasetId: this.datasetId, id })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    resetParamsOfCurrentClips(index) {
      this.activeClipIndex = index
      this.activeClip = this.datasetClipsList[index]
      this.innerQueryParam.startTime = this.activeClip.startTime
      this.innerQueryParam.endTime = this.activeClip.endTime
      this.innerQueryParam.vin = this.activeClip.vin
    },
    handleKeydown(event) {
      event?.preventDefault()
      let _this = this
      const key = event?.key?.toLowerCase() // 将按键转换为小写以统一处理
      if (this.isInvalidConfirmDialogOpen) return
      switch (key) {
        case 'q':
          let flag = !this.isInvalidConfirmDialogOpen && !this.$refs.scenarioRef.visible
          if (flag && (this.finishSelectVisible || this.finishCheckVisible)) {
            _this.invalidClip()
          }
          break
        case 'e':
          if (this.finishSelectVisible || this.finishCheckVisible) {
            _this.tryUpdateScenario()
          }
          break
        case 'w':
          _this.navigateClip(-1)
          break
        case 's':
          _this.navigateClip(1)
          break
        default:
          break
      }
    },
    tryUpdateScenario() {
      if (!this.$refs.scenarioRef.visible) {
        this.updateScenario()
      }
    },
    navigateClip(direction) {
      if (this.isInvalidConfirmDialogOpen || this.$refs.scenarioRef.visible) return
      const newIndex = this.activeClipIndex + direction
      if (newIndex >= 0 && newIndex < this.datasetClipsList?.length) {
        // this.activeClipIndex = newIndex
        this.getCurrentClipPageData(newIndex)
      } else {
        const message = direction < 0 ? '当前是第一个切片' : '当前是最后一个切片'
        showToast(message, 'warning')
      }
    },
    //查询infoData
    async resetInfoData(id, val) {
      await getDmDataset(id, val).then(res => {
        this.infoData = res.data
        if (this.infoData.mainViewDictList?.length && this.infoData.selectionMode !== 'sharding') {
          let mainViewDict = this.infoData.mainViewDictList.find(item => item.asDefault === true)
          this.modalitySelected = mainViewDict ? mainViewDict.code : this.infoData.mainViewDictList[0].code
        }
        this.resetScenarioList()
      })
    },
    //初始化场景数据
    resetScenarioList() {
      if (this.infoData.scenarioList?.length && this.infoData.selectionMode !== 'sharding') {
        let arr = this.infoData.scenarioList.filter(item => item.quantity > 0)
        this.scenarioList = JSON.parse(JSON.stringify(arr))
        this.scenarioList.unshift({ code: '', name: '全部', quantity: 'all' })
        this.scenarioTabName = this.scenarioList[0].code
        this.innerQueryParam.scerarioCode = this.scerarioTabName ? this.scenarioTabName : undefined
      }
    },
    //查询inner data
    async resetInnerPageData() {
      this.scerarioTabName = ''
      this.datasetType = this.infoData.dataType
      this.innerQueryParam.datasetId = this.datasetId
      if (this.infoData.selectionMode === 'sharding') {
        await this.listClips()
      }
      this.innerRefresh()
      this.$router.replace({ query: {} })
    },
    async getShoppingCartData() {
      await queryShoppingCartData(this.datasetId).then(res => {
        if (!res.data) {
          this.currentShoppingCartData = []
          return
        }
        let currentShoppingCartData = Object.values(res.data).flat(Infinity) || []
        this.currentShoppingCartData = this.parseSelectedParams(currentShoppingCartData)
      })
    },
    async getRecycleBinData() {
      await queryRecycleBinData(this.datasetId).then(res => {
        if (!res.data) {
          this.currentRecycleBinData = []
          return
        }
        let currentRecycleBinData = Object.values(res.data).flat(Infinity) || []
        this.currentRecycleBinData = this.parseSelectedParams(currentRecycleBinData)
      })
    },
    //获取回收站数据和购物车数据
    getLocalStorageData() {
      this.getShoppingCartData()
      this.getRecycleBinData()
    },
    parseSelectedParams(selectedList) {
      let measurementList = []
      let idList = []
      if (!selectedList?.length) return []
      if (this.infoData.frameSync) {
        measurementList =
          selectedList?.map(item => ({
            measurement: item.measurement,
            vin: item.vin
          })) || []
        return measurementList
      }
      idList =
        selectedList?.map(item => {
          return item.id
        }) || []
      return idList
    },
    async handleFinishStatus() {
      if (this.currentRecycleBinData?.length) {
        showToast('回收站内仍有数据', 'warning')
        return null
      }
      if (this.currentShoppingCartData?.length) {
        showToast('购物车内仍有数据', 'warning')
        return null
      }
      let arr = []
      await preRetentionPageDataQuery({ current: 1, size: 1, datasetId: this.datasetId }, true).then(res => {
        arr = res.data.records || []
      })
      if (arr?.length) {
        showToast('仍有数据是预保留状态', 'warning')
        return null
      }
      if (this.infoData.selectionMode === 'sharding') {
        let toBeSlicedIndex = this.datasetClipsList.findIndex(item => item.status === 'to_be_sliced')
        if (toBeSlicedIndex >= 0) {
          showToast('仍然存在待分片的切片，请进行分片', 'warning')
          return null
        }
        let invalidIndex = this.datasetClipsList.findIndex(item => item.status === 'invalid')
        if (invalidIndex >= 0) {
          showToast('仍然存在无效的切片，请点击一键删除无效分片', 'warning')
          return null
        }
        let arr = this.datasetClipsList.filter(item => {
          return !item.scenarioCode
        })
        if (arr?.length) {
          showToast('仍然存在切片无场景，请为无场景的切片更新场景', 'warning')
          return null
        }
      }
      return true
    },
    startSelect() {
      startManualSelection(this.datasetId).then(res => {
        this.dataWritable = true
        this.resetInfoData(this.datasetId, false)
        this.addToShoppingCartVisible = true
      })
    },
    async finishSelect() {
      let flag = await this.handleFinishStatus()
      if (!flag) {
        return
      }
      showConfirmToast({
        message: BASE_CONSTANT.FINISH_SELECT_MSG
      }).then(res => {
        finishManualSelection(this.datasetId).then(res => {
          this.addToShoppingCartVisible = false
          this.resetInfoData(this.datasetId, false)
          this.dataWritable = false
          this.startCheckVisible = false
          this.isIndeterminate = false
          this.allChecked = false
        })
      })
    },
    startCheck() {
      startManualCheck(this.datasetId).then(res => {
        this.dataWritable = true
        this.resetInfoData(this.datasetId, false)
        this.addToShoppingCartVisible = true
      })
    },
    async finishCheck() {
      let flag = await this.handleFinishStatus()
      if (!flag) {
        return
      }
      showConfirmToast({
        message: BASE_CONSTANT.FINISH_CHECK_MSG
      }).then(res => {
        finishManualCheck(this.datasetId).then(res => {
          this.dataWritable = false
          this.resetInfoData(this.datasetId, false)
          this.addToShoppingCartVisible = false
          this.isIndeterminate = false
          this.allChecked = false
        })
      })
    },
    filterButtonClick(event) {
      this.$refs.filterRef.show(this.innerQueryParam)
    },
    filter(param) {
      this.innerQueryParam = JSON.parse(JSON.stringify(param))
      this.innerQueryParam.size = 400
      this.innerRefresh()
    },
    initInnerParam() {
      this.innerQueryParam = {
        current: 1,
        size: 400,
        mutiSortField: 'measurement desc'
      }
    },
    initInnerProps() {
      this.unloading = false
      this.currentStartIndex = 0
      this.currentEndIndex = 20
      this.selectedData = []
      this.allChecked = false
      this.isIndeterminate = false
      this.lastMeasurement = ''
    },
    handleTabChange(val) {
      this.innerQueryParam.scenarioCode = val
      this.innerRefresh()
    },
    handleLoadMore(currentStartIndex, currentEndIndex) {
      this.currentStartIndex = currentStartIndex * this.splitNum
      this.currentEndIndex = currentEndIndex * this.splitNum
      let flag = this.currentEndIndex > this.innerPageData.records?.length - this.initPageLength
      if (flag && this.innerPageData.hasNextPage) {
        this.innerQueryParam.current += 1
        this.innerQuery()
      }
    },
    innerRefresh() {
      this.innerQueryParam.current = 1
      this.innerQueryParam.lastMeasurement = ''
      this.initInnerProps()
      this.innerPageData = {
        records: []
      }
      this.innerQuery()
    },
    dataStatistic() {
      if (!this.shouldContinueProcessing) return
      dataStatistic(this.innerQueryParam).then(res => {
        this.statisticData = res.data
      })
    },
    getPageData() {
      if (!this.shouldContinueProcessing) return
      queryInnerData(this.innerQueryParam, {}, this.unloading).then(res => {
        if (this.unloading === false) {
          util.loadingShow()
          this.dataStatistic()
        }
        let data = res.data?.records || []
        this.innerPageData.hasNextPage = res.data.hasNextPage
        this.innerPageData.records = this.innerQueryParam.current === 1 ? data : this.innerPageData.records.concat(data)
        this.$nextTick(() => {
          util.loadingHide()
          this.unloading = true
        })
      })
    },
    getSyncFramePageData() {
      if (!this.shouldContinueProcessing) return
      if (this.innerQueryParam.lastMeasurement === this.lastMeasurement && this.unloading) {
        return
      }
      this.innerQueryParam.lastMeasurement = this.lastMeasurement
      queryInnerMeasurementData(this.innerQueryParam, {}, this.unloading).then(res => {
        if (this.unloading === false) {
          util.loadingShow()
          this.dataStatistic()
        }
        let syncFrameData = res.data?.records
        this.lastMeasurement = syncFrameData?.length ? syncFrameData[syncFrameData?.length - 1].measurement : ''
        this.innerPageData.hasNextPage = res.data.hasNextPage
        this.innerPageData.records =
          this.innerQueryParam.current === 1 ? syncFrameData : this.innerPageData.records.concat(syncFrameData)
        if (syncFrameData < 40 && this.innerPageData.hasNextPage) {
          this.innerQueryParam.current += 1
          this.getSyncFramePageData()
        }
        this.$nextTick(() => {
          util.loadingHide()
          this.unloading = true
        })
      })
    },
    async innerQuery() {
      if (this.updateTagFlag) {
        debounce(await this.getSyncFramePageData())
        return
      }
      if (this.infoData.frameSync) {
        debounce(await this.getSyncFramePageData())
      } else {
        debounce(await this.getPageData())
      }
    },
    getInfoDetail(data) {
      this.startIndex = this.innerPageData.records.findIndex(
        item => item.measurement === data.measurement && item.vin === data.vin
      )
      this.$refs.syncDataFrameCardDetailRef.show(this.startIndex)
      this.$refs.syncDataFrameCardDetailRef.addDomEventListener()
    },
    getNextPageData() {
      if (this.innerPageData.hasNextPage) {
        this.innerQueryParam.current += 1
        this.innerQuery()
      }
    },
    getIndeterminate() {
      let selectedAmount = this.selectedData.length
      if (this.infoData.frameSync) {
        this.isIndeterminate = selectedAmount > 0 && selectedAmount < this.statisticData.measurementAmount
        this.allChecked = selectedAmount === this.statisticData.measurementAmount
      } else {
        this.isIndeterminate = selectedAmount > 0 && selectedAmount < this.statisticData.dataAmount
        this.allChecked = selectedAmount === this.statisticData.dataAmount
      }
    },
    handleChecked(val, item) {
      item.checked = val
      let index = this.selectedData.indexOf(item)
      if (index >= 0) {
        if (item.checked === false) {
          this.selectedData.splice(index, 1)
        }
      } else {
        if (item.checked) {
          this.selectedData.push(item)
        }
      }
      this.getIndeterminate()
    },
    initChecked(val) {
      this.innerPageData.records.forEach(item => {
        item.checked = val
      })
    },
    hasStringValue(params) {
      const keysToCheck = ['name', 'vins', 'fileTypes', 'modalities', 'marks', 'tagCodes', 'startTime', 'endTime']
      return keysToCheck.some(key => {
        const value = params[key]
        return typeof value === 'string' && value.trim().length > 0
      })
    },
    handleAllClick(val) {
      if (val) {
        showConfirmToast({
          message: BASE_CONSTANT.ALL_CHECKED_MSG
        })
          .then(res => {
            this.isIndeterminate = false
            this.selectedData = []
            this.initChecked(val)
          })
          .catch(err => {
            this.allChecked = false
          })
      } else {
        this.isIndeterminate = false
        this.selectedData = []
        this.initChecked(val)
      }
    },
    groupByScenario(objectArray, property) {
      return objectArray.reduce(function (acc, obj) {
        if (obj[property] === undefined) {
          obj[property] = 'default'
        }
        if (!acc[obj[property]]) {
          acc[obj[property]] = []
        }
        acc[obj[property]].push(obj)
        return acc
      }, {})
    },
    async batchRemove() {
      this.resetInfoData(this.datasetId, true)
      await this.dataStatistic()
      if (this.infoData.selectionMode === 'sharding') {
        await this.listClips()
        await this.innerRefresh()
      }
      if (this.infoData.frameSync) {
        let selectedParams = this.parseSelectedParams(this.currentRecycleBinData)
        batchDeleteByMeasurement(this.innerPageData.records, selectedParams)
      } else {
        batchDeleteById(this.innerPageData.records, this.currentRecycleBinData)
      }
      this.currentRecycleBinData = []
      this.isIndeterminate = false
      this.allChecked = false
    },
    handleSelectedDataBeforeAdded() {
      if (!this.selectedData?.length && !this.allChecked) {
        showToast(this.$t('请选择数据'), 'warning')
        return null
      }
      let hasFilterParam = this.hasStringValue(this.innerQueryParam)
      if (!hasFilterParam && !this.selectedData?.length) {
        this.allChecked = false
        this.isIndeterminate = false
        this.selectedData = []
        showToast('没有筛选条件不支持全选加入购物车和回收站', 'warning')
        return null
      }
      return true
    },
    //添加数据
    handleSelectedShoppingCartData() {
      let param = this.parseDataParam()
      addShoppingCartData(param).then(res => {
        this.currentShoppingCartData = this.parseSelectedParams(res.data)
        this.allChecked = false
        this.isIndeterminate = false
        this.selectedData = []
        if (this.currentShoppingCartData.length > 1000) {
          showConfirmToast({
            message: BASE_CONSTANT.SHOPPING_CART_TIP_MSG
          }).then(res => {
            this.$refs.dataShoppingCartRef.openShoppingCart()
          })
        }
      })
    },
    parseDataParam() {
      let selectedParams = this.parseSelectedParams(this.selectedData)
      let param = {
        datasetId: this.datasetId,
        frameSync: this.infoData.frameSync,
        scenarioCode: this.scenarioSelected || 'default'
      }
      if (this.allChecked) {
        param.innerDataQueryParam = this.innerQueryParam
      } else {
        param.innerDataQueryParam = {}
        if (this.infoData.frameSync) {
          param.innerDataQueryParam = { ...param.innerDataQueryParam, vinMeasurementList: selectedParams }
        } else {
          param.innerDataQueryParam = { ...param.innerDataQueryParam, idList: selectedParams }
        }
      }
      return param
    },
    addDataToCart() {
      let flag = this.handleSelectedDataBeforeAdded()
      if (!flag) {
        return
      }
      if (this.infoData.scenarioList?.length) {
        this.$refs.scenarioRef?.show()
        return
      }
      this.handleSelectedShoppingCartData()
      this.$refs.dataShoppingCartRef?.handleBallShow()
    },
    addDataToShoppingCart(data) {
      let flag =
        this.addToShoppingCartVisible &&
        ((this.finishCheckVisible && this.infoData.scenarioList?.length) || this.finishSelectVisible)
      if (!flag) {
        data.checked = false
        showToast('检查状态不支持预保留操作', 'warning')
        return
      }
      this.selectedData = [data]
      this.addDataToCart()
    },
    //确定保留
    retentionConfirm(val) {
      this.currentShoppingCartData = []
      this.currentRecycleBinData = []
      this.innerRefresh()
      this.scenarioList = []
      this.resetInfoData(this.datasetId, true)
    },
    //预保留
    preRetain(data) {
      this.currentShoppingCartData = this.parseSelectedParams(data)
    },
    //移除
    restore(data, restoreData, type) {
      if (type === 'shopping_cart') {
        this.currentShoppingCartData = this.parseSelectedParams(data)
      } else {
        this.currentRecycleBinData = this.parseSelectedParams(data)
      }
      if (!this.infoData.frameSync) {
        this.innerPageData.records?.forEach(item => {
          if (restoreData.includes(item.id)) {
            item.checked = false
            item.operationFlag = false
          }
        })
        return
      }
      this.innerPageData.records?.forEach(item => {
        let flag = restoreData.some(listItem => {
          return listItem.measurement === item.measurement && listItem.vin === item.vin
        })
        if (flag) {
          item.checked = false
          item.operationFlag = false
        }
      })
    },
    clean(type) {
      if (type === 'shopping_cart') {
        this.currentShoppingCartData = []
      } else {
        this.currentRecycleBinData = []
      }
      if (!this.infoData.frameSync) {
        this.innerPageData.records?.forEach(item => {
          item.checked = false
          item.operationFlag = false
        })
        return
      }
      this.innerPageData.records?.forEach(item => {
        item.checked = false
        item.operationFlag = false
      })
    },
    handleSelectedRecycleBinData() {
      let param = this.parseDataParam()
      delete param.scenarioCode
      addRecycleBinData(param).then(res => {
        this.currentRecycleBinData = this.parseSelectedParams(res.data)
        this.allChecked = false
        this.isIndeterminate = false
        this.selectedData = []
        if (this.currentRecycleBinData.length > 1000) {
          showConfirmToast({
            message: BASE_CONSTANT.RECYCLE_BIN_TIP_MSG
          }).then(res => {
            this.$refs.dataRecycleBinRef.openRecycleBin()
          })
        }
      })
    },
    addDataToRecycleBin() {
      let flag = this.handleSelectedDataBeforeAdded()
      if (!flag) {
        return
      }
      this.handleSelectedRecycleBinData()
      this.$refs.dataRecycleBinRef?.handleBallShow()
    },
    fullUpdateScenario() {
      this.$refs.scenarioRef?.show(true)
    },
    updateScenario() {
      this.$refs.scenarioRef?.show()
    },
    updateClipTag() {
      this.rowTagList=this.activeClip.tagList || []
      this.tagDistributeDrawerVisible = true
    },
    confirmDistributeTags(data){
      if (data && data.tagList && data.tagList.length) {
        this.tagList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagList(tagGroup)
        })
        let tagCodes=this.tagList.map(item=>item.code).join(',')
        this.tagDistributeDrawerVisible = false
        updateClipTags({id: this.activeClip.id,tagCodes:tagCodes}).then(res=>{
          this.activeClip.tagList=JSON.parse(JSON.stringify(this.tagList))
        })
      }else{
        this.tagList = []
        this.tagDistributeDrawerVisible = false
        updateClipTags({id: this.activeClip.id,tagCodes:""}).then(res=>{
          this.activeClip.tagList=JSON.parse(JSON.stringify(this.tagList))
        })
      }
    },
    saveCheckedTagList(group) {
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.tagList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      }
    },
    batchMarkClipScenario(scenarioCode) {
      if (!this.checkedScenarioList?.length) return showToast('暂无场景可更新', 'warning')
      let invalidClipList = this.datasetClipsList.filter(item => item.status === 'invalid')
      let toBeSlicedClipList = this.datasetClipsList.filter(item => item.status === 'to_be_sliced')
      if (invalidClipList?.length || toBeSlicedClipList?.length) return showToast('请先完成对切片的前序操作', 'warning')
      let params = { scenarioCode: scenarioCode }
      if (this.checkAllClip) {
        params.datasetId = this.datasetId
      } else {
        let idList = this.checkedClips.map(item => item.id)
        params.idList = idList
      }
      return markUpScenario(params).then(res => {
        showToast('批量切片场景更新成功', 'success')
        this.checkAllClip = false
        this.isClipIndeterminate = false
        this.checkedClips = []
        this.clipRefresh()
      })
    },
    batchMarkScenario(scenarioCode) {
      return batchMarkUpScenario({
        scenarioCode: scenarioCode,
        queryParam: {
          idList: [this.datasetId]
        }
      }).then(res => {
        showToast('更新场景成功', 'success')
        this.resetInfoData(this.datasetId)
        this.innerRefresh()
      })
    },
    async confirmScenario(val, fullUpdate) {
      if (!val) {
        showToast('请选择场景', 'warning')
        return
      }
      if (fullUpdate) {
        if (this.infoData.selectionMode === 'sharding') {
          await this.batchMarkClipScenario(val)
          return
        }
        await this.batchMarkScenario(val)
        return
      }
      if (this.infoData.selectionMode === 'sharding') {
        markUpScenario({ id: this.activeClip.id, scenarioCode: val }).then(res => {
          showToast('场景更新成功', 'success')
          this.activeClip.scenarioCode = res.data[0].scenarioCode
          this.activeClip.scenarioName = res.data[0].scenarioName
        })
        return
      }
      this.scenarioSelected = val
      this.handleSelectedShoppingCartData()
      this.$refs.dataShoppingCartRef?.handleBallShow()
      this.$refs.syncDataFrameCardDetailRef?.focusDetail()
    },
    switchWholeSecond() {
      this.isWholeSecond = !this.isWholeSecond
      this.innerQueryParam.isWholeSecond = this.isWholeSecond
      this.innerRefresh()
    },
    scenarioConfig() {
      this.$refs.scenarioConfigRef.show()
    },
    scenarioConfigConfirm(data) {
      this.checkedScenarioList = data
    },
    deleteAllInnerData(measurement) {
      delete this.syncPageData[measurement]
    },
    //下载
    download() {
      if (this.selectedData?.length == 0 && !this.allChecked) {
        showToast(this.$t('请选择需要下载的数据'), 'warning')
        return
      } else {
        let selectedData = JSON.parse(JSON.stringify(this.selectedData))
        let param = JSON.parse(JSON.stringify(this.innerQueryParam))
        delete param?.lastMeasurement
        delete param?.current
        delete param?.datasetId
        if (this.infoData.frameSync) {
          let arr = []
          this.selectedData.forEach(item => {
            arr = arr.concat(item.dataList)
          })
          selectedData = arr
        }
        this.$refs.downloadRef.show({
          datasetId: this.datasetId,
          queryParam: param,
          selectedData: selectedData
        })
      }
    },
    downloadPanelClosed() {
      this.selectedData = []
      this.allChecked = false
      this.isIndeterminate = false
      this.initChecked(false)
    },
    //更新tag
    updateTag() {
      let tagRecordMap = this.$refs.syncDataPageRef?.tagRecordMap
      this.$refs.tagRecordRef.show(tagRecordMap)
    },
    startUpdateTag() {
      this.updateTagFlag = true
      this.startUpdateTagFlag = true
      if (this.updateTagFlag === this.infoData.frameSync) {
        return
      }
      this.innerPageData = {
        records: []
      }
      this.innerRefresh()
    },
    finishUpdateTag() {
      this.updateTagFlag = false
      this.startUpdateTagFlag = false
      if (!this.updateTagFlag === this.infoData.frameSync) {
        return
      }
      this.innerPageData = {
        records: []
      }
      this.innerRefresh()
    },
    shortcutSave() {
      this.shortcutsMap = JSON.parse(localStorage.getItem('shortcuts'))
    },
    addTag() {
      this.$refs.syncDataPageRef.initTagRecord()
    },
    handleDrawerClosed() {
      this.resetInfoData(this.datasetId, true)
    },
    previewRoutes() {
      let toBeSlicedIndex = this.datasetClipsList.findIndex(item => item.status === 'to_be_sliced')
      if (toBeSlicedIndex >= 0) {
        showToast('仍然存在待分片的切片，请进行分片', 'warning')
        return null
      }
      let invalidIndex = this.datasetClipsList.findIndex(item => item.status === 'invalid')
      if (invalidIndex >= 0) {
        showToast('仍然存在无效的切片，请点击一键删除无效分片', 'warning')
        return null
      }
      this.mapVisible = true
    },
    mapExit() {
      this.mapVisible = false
      this.clipQueryParam = {}
      this.checkAllClip = false
      this.resetInnerPageData()
    }
  }
}
</script>
<style scoped lang="scss">
.items-center {
  display: flex;
  justify-content: space-between;
  margin-right: 120px;
}

.flex {
  display: flex;
  justify-content: space-between;
}

.f-s-12 {
  font-size: 12px;
}

.statistic-content {
  display: flex;

  .statistic-content-item {
    margin-right: 10px;
    color: #13ce66;
  }

  .frame-font {
    color: #409eff;
  }
}

.refresh-button {
  margin-right: 10px;
}

.column-content {
  margin-left: 5px;
  width: 50px;
}

.m-r-15 {
  margin-right: 15px;
}

.data-card {
  height: calc(100vh - 140px);

  :deep(.el-card__body) {
    height: 100%;
    padding: 10px;

    .ltw-page-container {
      height: calc(100% - 65px);
      width: 100%;

      .right-button {
        display: flex;
        justify-content: space-between;
      }

      .container {
        height: calc(100% - 100px);
        display: flex;
        justify-content: space-between;
        width: 100%;

        .aside {
          height: 100%;
          width: 255px;
          margin-right: 15px;

          .sider-container {
            height: 100%;

            .clip-button-content {
              font-size: 12px;
              display: flex;
              justify-content: space-between;
              margin-bottom: 5px;

              .status-txt {
                display: flex;
                align-items: center;
                margin-right: 5px;
              }
            }

            .clip-button-container {
              width: 100%;
              justify-content: space-between;
              margin-bottom: 5px;
            }

            .is-active {
              color: #409eff;
              border-color: #409eff;
              background-color: #ecf5ff;
            }

            .clip-list-container {
              height: calc(100% - 65px);

              .clip-container {
                margin-bottom: 5px;
                height: 50px;

                .el-row {
                  width: 100%;
                  height: 100%;
                  flex-wrap: nowrap;

                  .el-button {
                    height: 100%;
                    width: calc(100% - 15px);
                    margin-left: 5px;

                    .time-content {
                      display: flex;
                      flex-direction: column;
                      width: 135px;

                      .el-link:nth-child(1) {
                        margin-bottom: 10px;
                      }
                    }

                    .scenario-txt {
                      display: flex;
                      align-items: center;
                      white-space: break-spaces;
                      margin: 0;
                      height: 50px;
                    }
                  }
                }
              }
            }

            .toggle-button {
              width: 100%;
              font-size: 16px;
              color: #000000;
              line-height: 24px;
              padding-right: 10px;
              text-align: right;
              cursor: pointer;
              transform: rotate(0);

              &.collapsed {
                text-align: left;
                transform: rotate(180deg);
              }
            }
          }
        }

        .collapsed-aside {
          width: 350px;
        }

        .data-container {
          height: 100%;
          //width: 100%;
          flex: 1 1 0%;
          min-width: 0;

          .header-toolbar {
            justify-content: space-between;
            flex-wrap: nowrap;

            .button-container {
              flex-wrap: nowrap;
            }
          }

          .key-config-content {
            height: 32px;
            margin-left: 15px;
          }

          .active-class {
            color: #409eff;
          }

          .default-class {
            border: none;
            padding: 0;
            margin: 0 10px;
          }

          .row-left {
            width: calc(100% - 80px);

            .tabs-container {
              width: calc(100% - 80px);
              margin-left: 15px;
              font-size: 12px;
              margin-top: -15px;
            }
          }

          .sharding-row-left {
            //width: calc(100% - 270px);
            flex: 1;
            min-width: 0;
            .clip-tag-container {
              display: inline-flex;
              width: calc(100% - 48px);
              margin-left: 3px;
              overflow-x: auto;
              white-space: nowrap;
              flex-wrap: nowrap;
              &::-webkit-scrollbar {
                height: 3px;
                background: #eee;
                border-radius: 2px;
                cursor: pointer;
              }
              &::-webkit-scrollbar-thumb {
                background: #ccc;
                border-radius: 3px;
                cursor: pointer;
              }
              .el-tag {
                margin: 0 3px;
              }
            }
          }

          .file-sub-container {
            height: calc(100% - 10px);
            overflow-y: auto;
          }

          .default-file-sub-container {
            height: calc(100% - 125px);
            overflow-y: auto;
          }
        }
      }
    }
  }
}

.el-card {
  .ltw-toolbar {
    display: flex;
    justify-content: space-between;

    .is-whole-second {
      border: 1px solid #67c23a;
      background: #67c23a;
      color: #ffffff;
    }

    .input-button {
      width: 250px;
    }

    .filter-button {
      width: 100px;
    }

    .batch-button {
      margin-right: 12px;
    }

    .middle-button {
      span {
        font-size: 12px;
        margin-right: 15px;
      }
    }
  }
}

.tip-container {
  .shortcut-key {
    display: flex;
    font-size: 12px;
    align-items: center;
    margin-bottom: 5px;

    .shortcut-key-button {
      margin-right: 5px;
      padding: 2px 10px;
      background-color: #f0f0f0;
      border: 1px solid #d0d0d0;
      box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
      border-radius: 3px;
      font-family: 'Courier New', Courier, monospace;
      text-align: center;
      user-select: none;
    }
  }
}
</style>
