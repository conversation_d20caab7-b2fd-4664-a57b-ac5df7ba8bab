import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveQaTagTask = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks',
    data,
    params
  })
export const updateQaTagTask = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks',
    data,
    params
  })
export const deleteQaTagTask = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks',
    params
  })
export const listQaTagTask = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks',
    params
  })
export const listQaTagTaskSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks/selections',
    params
  })
export const pageQaTagTask = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks/page',
    params
  })
export const getQaTagTask = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks/' + id })
export const receiveQaTagTask = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks/' + id + '/receive' })
export const exportExcel = (params = {}, responseType = 'blob') =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks/export',
    params,
    responseType
  })
export const startCheckQaTagTask = id =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks/' + id + '/sampling_check/start' })
export const finishCheckQaTagTask = (id, data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks/' + id + '/sampling_check/finish', data })
export const batchStartQaTagTask = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks/batch/start', data })
export const releaseQaTagTask = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/qa/qa_tag_tasks/' + id + '/release' })
