<template>
  <div class="mining-result-container">
    <!-- 头部 -->
    <div class="header">
      <el-tooltip effect="dark" content="返回" placement="top-start">
        <ltw-icon icon-code="el-icon-arrow-left" @click="goBack" class="icon-back"></ltw-icon>
      </el-tooltip>
      <span class="title">{{ recordData.name || '挖掘结果可视化' }}</span>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 选择器区域 -->
      <div class="select-box">
        <div class="select-item">
          <span>摄像头：</span>
          <el-select
            v-model="selectedCameras"
            placeholder="请选择摄像头（最多9个）"
            style="width: 300px"
            multiple
            :multiple-limit="9"
            @change="onCameraChange"
          >
            <el-option v-for="item in cameraList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </div>
        <div class="select-item">
          <span>车辆：</span>
          <el-select v-model="selectedVehicle" placeholder="请选择车辆" style="width: 200px" @change="onVehicleChange">
            <el-option v-for="item in vehicleList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </div>
        <div class="select-item">
          <span>时间段：</span>
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="onTimeRangeChange"
            style="width: 350px"
          />
        </div>
        <el-button type="primary" @click="loadData" :loading="loading">
          <ltw-icon icon-code="el-icon-search"></ltw-icon>
          查询
        </el-button>
      </div>

      <!-- 内容展示区域 -->
      <div class="content-box" v-loading="loading">
        <!-- 左侧图像区域 -->
        <div class="left-box">
          <div class="image-section">
            <div class="section-title">
              图像数据
              <span v-if="selectedCameras.length > 0" class="camera-count">
                ({{ selectedCameras.length }}个摄像头)
              </span>
            </div>
            <div class="image-container" ref="imageContainer">
              <!-- 多摄像头网格布局 -->
              <div
                v-if="selectedCameras.length > 0"
                class="camera-grid"
                :class="getCameraGridClass()"
              >
                <div
                  v-for="(cameraId, index) in selectedCameras"
                  :key="cameraId"
                  class="camera-item"
                >
                  <div class="camera-header">
                    <span class="camera-name">{{ getCameraName(cameraId) }}</span>
                  </div>
                  <canvas
                    :ref="`imageCanvas_${index}`"
                    :width="getCanvasWidth(selectedCameras.length)"
                    :height="getCanvasHeight(selectedCameras.length)"
                    class="image-canvas"
                  ></canvas>
                  <div v-if="!currentImages[cameraId]" class="no-image">
                    <span>暂无图像</span>
                  </div>
                </div>
              </div>

              <!-- 未选择摄像头时的提示 -->
              <div v-else class="no-camera">
                <el-empty description="请选择摄像头"></el-empty>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧轨迹区域 -->
        <div class="right-box">
          <div class="trajectory-section">
            <div class="section-title">
              轨迹数据
              <div class="trajectory-controls">
                <el-button-group>
                  <el-button size="small" @click="zoomIn" :disabled="zoomLevel >= maxZoom">
                    <el-icon><ZoomIn /></el-icon>
                  </el-button>
                  <el-button size="small" @click="zoomOut" :disabled="zoomLevel <= minZoom">
                    <el-icon><ZoomOut /></el-icon>
                  </el-button>
                  <el-button size="small" @click="resetZoom">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </el-button-group>
                <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
              </div>
            </div>
            <div class="trajectory-container" ref="trajectoryContainer">
              <div class="trajectory-wrapper"
                   @wheel="onTrajectoryWheel"
                   @mousedown="onTrajectoryMouseDown"
                   @mousemove="onTrajectoryMouseMove"
                   @mouseup="onTrajectoryMouseUp"
                   @mouseleave="onTrajectoryMouseUp">
                <canvas
                  ref="trajectoryCanvas"
                  :width="trajectoryCanvasWidth"
                  :height="trajectoryCanvasHeight"
                  class="trajectory-canvas"
                  :style="{
                    transform: `scale(${zoomLevel}) translate(${panX}px, ${panY}px)`,
                    transformOrigin: 'center center'
                  }"
                ></canvas>
                <div v-if="!currentTrajectory" class="no-trajectory">
                  <el-empty description="轨迹数据将在此显示"></el-empty>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部控制区域 -->
      <div class="control-box" v-show="timelineData.length > 0">
        <!-- 时间轴 -->
        <div class="timeline-container">
          <el-slider
            v-model="currentTimeIndex"
            :min="0"
            :max="timelineData.length ? timelineData.length - 1 : 0"
            :step="1"
            show-stops
            :format-tooltip="formatTooltip"
            @change="onTimeIndexChange"
            class="timeline-slider"
          />
          <div class="time-info">
            <el-tag>{{ currentTimeText }}</el-tag>
            <el-tag class="speed-tag" @click="changePlaySpeed">x{{ playbackSpeed }}</el-tag>
          </div>
        </div>

        <!-- 播放控制按钮 -->
        <div class="play-controls">
          <el-button-group>
            <el-button @click="previousFrame" :disabled="currentTimeIndex <= 0">
              <ltw-icon icon-code="el-icon-arrow-left"></ltw-icon>
            </el-button>
            <el-button @click="togglePlay" type="primary">
              <ltw-icon :icon-code="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"></ltw-icon>
              {{ isPlaying ? '暂停' : '播放' }}
            </el-button>
            <el-button @click="nextFrame" :disabled="currentTimeIndex >= timelineData.length - 1">
              <ltw-icon icon-code="el-icon-arrow-right"></ltw-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { ZoomIn, ZoomOut, Refresh } from '@element-plus/icons-vue'
import {
  getCameraList,
  getVehicleList,
  getImageData,
  getTrajectoryData,
  getTimelineData
} from '@/apis/dataMining/mining_result_visualization'

export default {
  name: 'MiningResultVisualization',
  components: {
    ZoomIn,
    ZoomOut,
    Refresh
  },
  props: {
    recordData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 选择器数据
      cameraList: [],
      vehicleList: [],
      selectedCameras: [], // 改为多选数组
      selectedVehicle: '',
      timeRange: [],

      // 数据相关
      timelineData: [], // 时间轴数据
      imageData: [], // 图像数据
      trajectoryData: [], // 轨迹数据

      // 播放控制
      currentTimeIndex: 0,
      isPlaying: false,
      playbackSpeed: 1,
      playTimer: null,

      // 多摄像头相关
      currentImages: {}, // 存储多个摄像头的当前图像

      // 轨迹缩放相关
      zoomLevel: 1,
      minZoom: 0.5,
      maxZoom: 3,
      panX: 0,
      panY: 0,
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0,
      trajectoryCanvasWidth: 400,
      trajectoryCanvasHeight: 300,
      currentTrajectory: null,

      // UI相关
      loading: false,
      canvasWidth: 640,
      canvasHeight: 480
    }
  },
  computed: {
    currentTimeText() {
      if (this.timelineData.length > 0 && this.currentTimeIndex < this.timelineData.length) {
        return moment(this.timelineData[this.currentTimeIndex].timestamp).format('YYYY-MM-DD HH:mm:ss')
      }
      return '--'
    }
  },
  watch: {
    currentTimeIndex(newIndex) {
      this.updateCurrentFrame(newIndex)
    }
  },
  mounted() {
    this.initCanvas()
    this.loadInitialData()
  },
  beforeUnmount() {
    this.stopPlay()
  },
  methods: {
    // 初始化画布
    initCanvas() {
      this.$nextTick(() => {
        if (this.$refs.imageContainer) {
          const container = this.$refs.imageContainer
          this.canvasWidth = container.clientWidth - 20
          this.canvasHeight = container.clientHeight - 60
        }
      })
    },

    // 加载初始数据
    async loadInitialData() {
      try {
        // 加载摄像头和车辆列表
        await Promise.all([
          this.loadCameraList(),
          this.loadVehicleList()
        ])
      } catch (error) {
        console.error('加载初始数据失败:', error)
        this.$message.error('加载初始数据失败')
      }
    },

    // 加载摄像头列表
    async loadCameraList() {
      try {
        const res = await getCameraList()
        this.cameraList = res.data || []
      } catch (error) {
        console.error('加载摄像头列表失败:', error)
        // 使用模拟数据作为降级方案
        this.cameraList = [
          { id: 'cam_001', name: '摄像头001' },
          { id: 'cam_002', name: '摄像头002' },
          { id: 'cam_003', name: '摄像头003' }
        ]
      }
    },

    // 加载车辆列表
    async loadVehicleList() {
      try {
        const res = await getVehicleList()
        this.vehicleList = res.data || []
      } catch (error) {
        console.error('加载车辆列表失败:', error)
        // 使用模拟数据作为降级方案
        this.vehicleList = [
          { id: 'vehicle_001', name: '车辆001' },
          { id: 'vehicle_002', name: '车辆002' },
          { id: 'vehicle_003', name: '车辆003' }
        ]
      }
    },

    // 加载模拟数据（降级方案）
    loadMockData() {
      // 生成模拟的时间轴数据
      const startTime = moment().subtract(1, 'hour')
      this.timelineData = []
      for (let i = 0; i < 100; i++) {
        this.timelineData.push({
          timestamp: startTime.clone().add(i * 30, 'seconds').toISOString(),
          imageUrl: null, // 实际项目中这里是图像URL或base64
          geoData: {
            longitude: 116.4074 + Math.random() * 0.01,
            latitude: 39.9042 + Math.random() * 0.01
          }
        })
      }
    },

    // 选择器变化事件
    onCameraChange() {
      console.log('摄像头变化:', this.selectedCameras)
      // 重新初始化画布
      this.$nextTick(() => {
        this.initMultiCameraCanvas()
      })
    },

    onVehicleChange() {
      console.log('车辆变化:', this.selectedVehicle)
    },

    onTimeRangeChange() {
      console.log('时间范围变化:', this.timeRange)
    },

    // 加载数据
    async loadData() {
      if (!this.selectedCameras.length || !this.selectedVehicle || !this.timeRange || this.timeRange.length !== 2) {
        this.$message.warning('请选择摄像头、车辆和时间范围')
        return
      }

      this.loading = true
      try {
        // 这里调用实际的API获取数据
        await this.fetchMiningResultData()
        this.$message.success('数据加载成功')
      } catch (error) {
        this.$message.error('数据加载失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    },

    // 获取挖掘结果数据
    async fetchMiningResultData() {
      try {
        const params = {
          miningDefineId: this.recordData.id,
          cameraId: this.selectedCamera,
          vehicleId: this.selectedVehicle,
          startTime: this.timeRange[0],
          endTime: this.timeRange[1]
        }

        // 并行获取时间轴、图像和轨迹数据
        const [timelineRes, imageRes, trajectoryRes] = await Promise.all([
          getTimelineData(params),
          getImageData(params),
          getTrajectoryData(params)
        ])

        // 处理时间轴数据
        this.timelineData = timelineRes.data || []

        // 处理图像数据
        this.imageData = imageRes.data || []

        // 处理轨迹数据
        this.trajectoryData = trajectoryRes.data || []

        // 如果没有数据，使用模拟数据
        if (this.timelineData.length === 0) {
          this.loadMockData()
        }

      } catch (error) {
        console.error('获取挖掘结果数据失败:', error)
        // 降级到模拟数据
        this.loadMockData()
        throw error
      }
    },

    // 时间轴变化
    onTimeIndexChange(index) {
      this.stopPlay()
      this.updateCurrentFrame(index)
    },

    // 更新当前帧
    updateCurrentFrame(index) {
      if (index >= 0 && index < this.timelineData.length) {
        const frameData = this.timelineData[index]
        this.updateImage(frameData.imageUrl)
        this.updateTrajectory(frameData.geoData)
      }
    },

    // 更新图像
    updateImage(imageUrl) {
      const canvas = this.$refs.imageCanvas
      if (!canvas) return

      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      if (imageUrl) {
        const img = new Image()
        img.onload = () => {
          // 计算缩放比例以适应画布
          const scale = Math.min(canvas.width / img.width, canvas.height / img.height)
          const width = img.width * scale
          const height = img.height * scale
          const x = (canvas.width - width) / 2
          const y = (canvas.height - height) / 2

          ctx.drawImage(img, x, y, width, height)
        }
        img.src = imageUrl
        this.currentImage = imageUrl
      } else {
        // 绘制占位符
        ctx.fillStyle = '#f0f0f0'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        ctx.fillStyle = '#999'
        ctx.font = '16px Arial'
        ctx.textAlign = 'center'
        ctx.fillText('暂无图像', canvas.width / 2, canvas.height / 2)
        this.currentImage = null
      }
    },

    // 更新轨迹
    updateTrajectory(geoData) {
      // 这里可以调用地图组件更新轨迹显示
      console.log('更新轨迹:', geoData)
    },

    // 播放控制
    togglePlay() {
      if (this.isPlaying) {
        this.stopPlay()
      } else {
        this.startPlay()
      }
    },

    startPlay() {
      if (this.currentTimeIndex >= this.timelineData.length - 1) {
        this.currentTimeIndex = 0
      }

      this.isPlaying = true
      this.playTimer = setInterval(() => {
        if (this.currentTimeIndex >= this.timelineData.length - 1) {
          this.stopPlay()
          return
        }
        this.currentTimeIndex++
      }, 1000 / this.playbackSpeed)
    },

    stopPlay() {
      this.isPlaying = false
      if (this.playTimer) {
        clearInterval(this.playTimer)
        this.playTimer = null
      }
    },

    previousFrame() {
      if (this.currentTimeIndex > 0) {
        this.stopPlay()
        this.currentTimeIndex--
      }
    },

    nextFrame() {
      if (this.currentTimeIndex < this.timelineData.length - 1) {
        this.stopPlay()
        this.currentTimeIndex++
      }
    },

    changePlaySpeed() {
      this.playbackSpeed = this.playbackSpeed >= 2 ? 1 : this.playbackSpeed + 1
      if (this.isPlaying) {
        this.stopPlay()
        this.startPlay()
      }
    },

    formatTooltip(val) {
      if (this.timelineData.length > 0 && val < this.timelineData.length) {
        return moment(this.timelineData[val].timestamp).format('HH:mm:ss')
      }
      return ''
    },

    goBack() {
      this.stopPlay()
      this.$emit('back')
    },

    // 多摄像头相关方法
    getCameraGridClass() {
      const count = this.selectedCameras.length
      if (count <= 1) return 'grid-1'
      if (count <= 4) return 'grid-2x2'
      if (count <= 6) return 'grid-2x3'
      return 'grid-3x3'
    },

    getCameraName(cameraId) {
      const camera = this.cameraList.find(c => c.id === cameraId)
      return camera ? camera.name : `摄像头${cameraId}`
    },

    getCanvasWidth(cameraCount) {
      if (cameraCount <= 1) return 640
      if (cameraCount <= 4) return 320
      if (cameraCount <= 6) return 213
      return 160
    },

    getCanvasHeight(cameraCount) {
      if (cameraCount <= 1) return 480
      if (cameraCount <= 4) return 240
      if (cameraCount <= 6) return 160
      return 120
    },

    initMultiCameraCanvas() {
      this.$nextTick(() => {
        this.selectedCameras.forEach((_, index) => {
          const canvas = this.$refs[`imageCanvas_${index}`]?.[0]
          if (canvas) {
            const ctx = canvas.getContext('2d')
            ctx.clearRect(0, 0, canvas.width, canvas.height)
            // 绘制占位符
            ctx.fillStyle = '#f0f0f0'
            ctx.fillRect(0, 0, canvas.width, canvas.height)
            ctx.fillStyle = '#999'
            ctx.font = '14px Arial'
            ctx.textAlign = 'center'
            ctx.fillText('等待图像数据', canvas.width / 2, canvas.height / 2)
          }
        })
      })
    },

    // 轨迹缩放相关方法
    zoomIn() {
      if (this.zoomLevel < this.maxZoom) {
        this.zoomLevel = Math.min(this.zoomLevel * 1.2, this.maxZoom)
      }
    },

    zoomOut() {
      if (this.zoomLevel > this.minZoom) {
        this.zoomLevel = Math.max(this.zoomLevel / 1.2, this.minZoom)
      }
    },

    resetZoom() {
      this.zoomLevel = 1
      this.panX = 0
      this.panY = 0
    },

    onTrajectoryWheel(event) {
      event.preventDefault()
      const delta = event.deltaY > 0 ? -0.1 : 0.1
      const newZoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.zoomLevel + delta))
      this.zoomLevel = newZoom
    },

    onTrajectoryMouseDown(event) {
      this.isDragging = true
      this.lastMouseX = event.clientX
      this.lastMouseY = event.clientY
    },

    onTrajectoryMouseMove(event) {
      if (this.isDragging) {
        const deltaX = event.clientX - this.lastMouseX
        const deltaY = event.clientY - this.lastMouseY
        this.panX += deltaX / this.zoomLevel
        this.panY += deltaY / this.zoomLevel
        this.lastMouseX = event.clientX
        this.lastMouseY = event.clientY
      }
    },

    onTrajectoryMouseUp() {
      this.isDragging = false
    }
  }
}
</script>

<style lang="scss" scoped>
.mining-result-container {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: white;
  z-index: 999;
  display: flex;
  flex-direction: column;

  .header {
    height: 50px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid #ebeef5;
    background: #f8f9fa;

    .icon-back {
      color: #409eff;
      cursor: pointer;
      margin-right: 15px;
      font-size: 18px;

      &:hover {
        color: #66b1ff;
      }
    }

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }

  .main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 0;

    .select-box {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      background: #f8f9fa;
      border-bottom: 1px solid #ebeef5;
      gap: 20px;
      flex-wrap: wrap;

      .select-item {
        display: flex;
        align-items: center;
        gap: 8px;

        span {
          font-size: 14px;
          color: #606266;
          white-space: nowrap;
        }
      }
    }

    .content-box {
      flex: 1;
      display: flex;
      height: 0;
      padding: 20px;
      gap: 20px;

      .left-box {
        flex: 1;
        display: flex;
        flex-direction: column;

        .image-section {
          height: 100%;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          overflow: hidden;

          .section-title {
            height: 40px;
            line-height: 40px;
            padding: 0 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #ebeef5;
            font-weight: 500;
            color: #303133;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .camera-count {
              font-size: 12px;
              color: #909399;
              font-weight: normal;
            }

            .trajectory-controls {
              display: flex;
              align-items: center;
              gap: 10px;

              .zoom-level {
                font-size: 12px;
                color: #909399;
                font-weight: normal;
                min-width: 40px;
              }
            }
          }

          .image-container {
            flex: 1;
            height: calc(100% - 40px);
            position: relative;
            background: #000;

            .camera-grid {
              width: 100%;
              height: 100%;
              display: grid;
              gap: 2px;
              padding: 10px;

              &.grid-1 {
                grid-template-columns: 1fr;
                grid-template-rows: 1fr;
              }

              &.grid-2x2 {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 1fr 1fr;
              }

              &.grid-2x3 {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 1fr 1fr 1fr;
              }

              &.grid-3x3 {
                grid-template-columns: 1fr 1fr 1fr;
                grid-template-rows: 1fr 1fr 1fr;
              }

              .camera-item {
                position: relative;
                border: 1px solid #333;
                border-radius: 4px;
                overflow: hidden;
                background: #1a1a1a;

                .camera-header {
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  height: 24px;
                  background: rgba(0, 0, 0, 0.7);
                  color: white;
                  font-size: 12px;
                  line-height: 24px;
                  padding: 0 8px;
                  z-index: 1;

                  .camera-name {
                    font-weight: 500;
                  }
                }

                .image-canvas {
                  width: 100%;
                  height: 100%;
                  display: block;
                }

                .no-image {
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  color: #666;
                  font-size: 12px;
                }
              }
            }

            .no-camera {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
            }

            .no-image {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #f8f9fa;
            }
          }
        }
      }

      .right-box {
        width: 400px;
        display: flex;
        flex-direction: column;

        .trajectory-section {
          height: 100%;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          overflow: hidden;

          .section-title {
            height: 40px;
            line-height: 40px;
            padding: 0 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #ebeef5;
            font-weight: 500;
            color: #303133;
          }

          .trajectory-container {
            flex: 1;
            height: calc(100% - 40px);
            position: relative;
            background: #f8f9fa;
            overflow: hidden;

            .trajectory-wrapper {
              width: 100%;
              height: 100%;
              position: relative;
              cursor: grab;

              &:active {
                cursor: grabbing;
              }

              .trajectory-canvas {
                display: block;
                transition: transform 0.1s ease-out;
              }

              .no-trajectory {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                pointer-events: none;
              }
            }

            .trajectory-placeholder {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }

    .control-box {
      height: 80px;
      padding: 15px 20px;
      border-top: 1px solid #ebeef5;
      background: #f8f9fa;

      .timeline-container {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        gap: 15px;

        .timeline-slider {
          flex: 1;
        }

        .time-info {
          display: flex;
          gap: 10px;

          .speed-tag {
            cursor: pointer;
            user-select: none;

            &:hover {
              background: #409eff;
              color: white;
            }
          }
        }
      }

      .play-controls {
        display: flex;
        justify-content: center;
      }
    }
  }
}
</style>
