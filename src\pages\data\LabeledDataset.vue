<template>
  <el-card v-show="!innerDataPageVisible && !mapResultVisible">
    <div class="ltw-page-container">
      <div class="ltw-toolbar">
        <ltw-input
          :placeholder="$t('请输入关键字')"
          clearable
          @clear="refresh"
          v-model="queryParam.key"
          class="input-button"
        >
          <template #append>
            <el-button @click="refresh">
              <ltw-icon icon-code="el-icon-search"></ltw-icon>
            </el-button>
          </template>
        </ltw-input>
        <div class="right-button">
          <el-checkbox v-model="allChecked" class="all-checked-button">{{ $t('全选') }}</el-checkbox>
          <div>
            <el-button type="primary" class="batch-button" size="small" @click="openDialog">
              {{ $t('导入文件生成数据集') }}
              <ltw-icon icon-code="el-icon-upload" class="el-icon--right"></ltw-icon>
            </el-button>
            <ImportDatasetDialog v-if="visible" @close="visible = false" />
          </div>
          <el-dropdown @command="handleCommand" class="batch-operate-btn" v-if="batchingFunctionList?.length > 0">
            <el-button type="primary" class="batch-button" size="small">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <template v-if="Object.keys(dataOperatePermission)?.length">
                  <template v-for="item in Object.keys(dataOperatePermission)" :key="item">
                    <el-dropdown-item
                      :command="dataOperatePermission[item].buttonCode"
                      v-if="Object.values(LABELED_DATA_SET_BATCH_FUNC).includes(item) && dataOperatePermission[item]"
                    >
                      <ltw-icon :icon-code="dataOperatePermission[item].buttonIconCode"></ltw-icon>
                      {{ $t(dataOperatePermission[item].name) }}
                    </el-dropdown-item>
                  </template>
                </template>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <template v-if="Object.keys(dataOperatePermission)?.length">
            <template v-for="item in Object.keys(dataOperatePermission)" :key="item">
              <el-button
                :type="dataOperatePermission[item].buttonStyleType"
                @click="executeButtonMethod($event, dataOperatePermission[item])"
                v-if="Object.values(LABELED_DATA_SET_OUT_FUNC).includes(item) && dataOperatePermission[item]"
                size="small"
              >
                <ltw-icon :icon-code="dataOperatePermission[item].buttonIconCode"></ltw-icon>
                {{ $t(dataOperatePermission[item].name) }}
              </el-button>
            </template>
          </template>
        </div>
      </div>
      <div class="container">
        <div class="table-container">
          <statistical-data v-model="statisticalData"></statistical-data>
          <bs-dataset-table
            v-model="queryParam"
            :dataOperatePermission="dataOperatePermission"
            :inlineFunctionList="inlineFunctionList"
            :type="DATA_SET_TYPE.LABELING"
            :orgList="orgList"
            @viewMapResult="viewMapResult"
            @handleNameClick="handleNameClick"
            @handleSelectionChange="handleSelectionChange"
            ref="tableRef"
          ></bs-dataset-table>
        </div>
      </div>
    </div>
  </el-card>
  <map-result-page
    v-if="mapResultVisible"
    :recordsData="recordsData"
    @goBackMainPage="goBackMainPage"
  ></map-result-page>
  <pose-create-dialog
    ref="poseDialog"
    v-if="dialogPoseVisible"
    @createPoseTask="createPoseTask"
    @dialogClosed="dialogClosed"
  />
  <inner-data-page
    ref="innerPageRef"
    :data-operate-permission="dataOperatePermission"
    :batching-function-list="batchingFunctionList"
    :dataset-id="datasetId"
    :dataset-name="datasetName"
    :writable="writable"
    :updatable="updatable"
    :charts-colors="chartsColors"
    @goBack="goBack"
    v-if="innerDataPageVisible"
  ></inner-data-page>
  <dm-dataset-form :type="queryParam.type" @form-save="refresh" ref="formRef"></dm-dataset-form>
  <batch-send-labeling-form ref="batchSendLabelingRef" @confirm="batchSendLabelingConfirm" @close="initTable">
  </batch-send-labeling-form>
  <temp-storage-form ref="tempStorageRef" @confirm="tempStorageConfirm" @close="initTable"></temp-storage-form>
  <labeling-requirement-selector
    ref="xLabelRef"
    @close="initTable"
    @confirm="xLabelRefConfirm"
  ></labeling-requirement-selector>
  <data-batch-copy ref="batchCopyRef" @confirm="batchDataCopyConfirm"></data-batch-copy>
  <generate-dataset ref="generateDatasetRef" @confirm="generateDatasetConfirm"></generate-dataset>
  <pose-selector ref="poseRef" @confirm="poseConfirm"></pose-selector>
  <employee-selection-dialog ref="employeeSelectionRef" @confirm="employeeSelectionConfirm"></employee-selection-dialog>
  <generate-dataset-name-input
    bis="datasetGenerate"
    ref="generateRef"
    @confirm="generateDatasetWithProcessTypeConfirm"
  ></generate-dataset-name-input>
  <download-input-file ref="downloadInputRef" @confirm="downloadInputConfirm"></download-input-file>
  <pull-data ref="pullDataRef" @confirm="refresh"></pull-data>
  <dataset-type-convert ref="convertRef" @confirm="convertConfirm"></dataset-type-convert>
  <download-meta-info ref="downloadMetaRef" @confirm="downloadMetaConfirm"></download-meta-info>
  <dataset-status-update ref="updateStatusRef" @confirm="updateStatusConfirm"></dataset-status-update>
  <batch-split-aisle-dataset ref="aisleRef" @confirm="splitAisleDatasetConform"></batch-split-aisle-dataset>
</template>

<script>
import { showConfirmToast, showToast } from '@/plugins/util'
import { listSysRoleOrg } from '@/apis/system/sys-role-org'
import BsDatasetTable from '@/components/dataset/basic/BsDatasetTable.vue'
import TempStorageForm from '@/components/dataset/labeledDataset/TempStorageForm.vue'
import BatchSendLabelingForm from '@/components/dataset/labeledDataset/BatchSendLabelingForm.vue'
import DmDatasetForm from '@/components/dataset/basic/DmDatasetForm.vue'
import DATA_SET_TYPE from '@/plugins/constants/data-set-type'
import DATA_TYPE from '@/plugins/constants/data-type'
import InnerDataPage from '@/components/dataset/labeledDataset/InnerDataPage.vue'
import statisticalData from '@/components/dataset/basic/StatisticalData.vue'
import { getSendForLabelingDestination, warmUp } from '@/apis/data/labeling-dataset'
import LabelingRequirementSelector from '@/pages/data/dialog/LabelingRequirementSelector.vue'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import DataBatchCopy from '@/components/data/basic/DataBatchCopy.vue'
import {
  DATA_SET_STATUS,
  LABELED_DATA_SET_BATCH_FUNC,
  LABELED_DATA_SET_OUT_FUNC,
  PROCESS_RESOURCE_TYPE
} from '@/plugins/constants/data-dictionary'
import {
  batchDemotion,
  batchFusionLidar,
  markUpScenarioByRule,
  batchFilter,
  batchPreProcessFrameExtraction,
  batchStartIcp,
  batchTenHZFusionLidar,
  batchTenHZDemotion,
  batchSlamPose,
  batchStartTenHZIcp,
  batchRosBagFrame,
  batchConvert,
  batchTenHZMainLidarFrameExtraction,
  batchCompleteTenHzData,
  batchCreatePoseTask,
  batchDownloadMetaInfo,
  batchSplitDataset,
  batchDeepCopy
} from '@/apis/data/data_set'
import GenerateDataset from '@/components/dataset/labeledDataset/GenerateDataset.vue'
import ImportDatasetDialog from '@/pages/data/dialog/ImportDatasetDialog.vue'
import PoseSelector from '@/components/dataset/labeledDataset/PoseSelector.vue'
import {
  batchSubmitAcceptance,
  batchTransferAcceptance,
  submitAcceptance,
  coldHandleDataset,
  hotHandleDataset,
  batchDatasetDeduplication,
  batchAbnormalLabelDelete,
  batchRemove
} from '@/apis/data/labeling-dataset'
import EmployeeSelectionDialog from '@/components/system/EmployeeSelectionDialog.vue'
import MapResultPage from '@/components/dataset/labeledDataset/MapResultPage.vue'
import PoseCreateDialog from '@/components/data/basic/PoseForm.vue'
import { getProcessResult } from '@/apis/geography/map-result'
import { batchStartDsSlam, downloadInput } from '@/apis/autox/autox-dataset'
import GenerateDatasetNameInput from '@/components/map/generateDatasetNameInput.vue'
import DownloadInputFile from '@/components/map/DownloadInputFile.vue'
import PullData from '@/components/dataset/basic/PullData.vue'
import DatasetTypeConvert from '@/components/dataset/DatasetTypeConvert.vue'
import DownloadMetaInfo from '@/components/dataset/basic/DownloadMetaInfo.vue'
import { generateMultipleRandomColors } from '@/plugins/map/TxMap'
import { batchUpdateStatus } from '@/apis/sta/sta-project-progress'
import DatasetStatusUpdate from '@/components/dataset/DatasetStatusUpdate.vue'
import BatchSplitAisleDataset from "@/components/dataset/labeledDataset/BatchSplitAisleDataset.vue";

const defaultFormData = {}
export default {
  name: 'LabeledDataset',
  components: {
    BatchSplitAisleDataset,
    DatasetStatusUpdate,
    DownloadMetaInfo,
    DatasetTypeConvert,
    DownloadInputFile,
    PullData,
    GenerateDatasetNameInput,
    MapResultPage,
    PoseSelector,
    EmployeeSelectionDialog,
    GenerateDataset,
    DataBatchCopy,
    LabelingRequirementSelector,
    InnerDataPage,
    BsDatasetTable,
    DmDatasetForm,
    TempStorageForm,
    BatchSendLabelingForm,
    statisticalData,
    ImportDatasetDialog,
    PoseCreateDialog
  },
  data() {
    return {
      LABELED_DATA_SET_BATCH_FUNC,
      LABELED_DATA_SET_OUT_FUNC,
      PROCESS_RESOURCE_TYPE,
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      DATA_SET_TYPE: DATA_SET_TYPE,
      DATA_TYPE: DATA_TYPE,
      orgList: [],
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10,
        type: DATA_SET_TYPE.LABELING
      },
      datasetId: '',
      datasetName: '',
      statisticalData: {
        checkedDatasetAmount: 0,
        checkedDataAmount: 0,
        checkedDataSize: 0
      },
      innerDataPageVisible: false,
      mapResultVisible: false,
      writable: false,
      visible: false,
      currentRow: Object.assign({}, defaultFormData),
      currentUser: Object.assign({}, defaultFormData),
      allChecked: false,
      dialogPoseVisible: false,
      recordsData: [],
      chartsColors: []
    }
  },
  created() {
    this.currentUser = this.$store.state.permission.currentUser
    let routerFunctionMap = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]
    if (routerFunctionMap) {
      this.batchingFunctionList = routerFunctionMap.batchingFunctionList
      this.inlineFunctionList = routerFunctionMap.inlineFunctionList
      this.outlineFunctionList = routerFunctionMap.outlineFunctionList
      this.dataOperatePermission = routerFunctionMap.dataOperatePermission
    }
    if (!this.orgList?.length) {
      this.listSupplier()
    }
    if (!this.chartsColors?.length) {
      this.chartsColors = generateMultipleRandomColors(500)
    }
  },
  watch: {},
  computed: {},
  methods: {
    listSupplier() {
      listSysRoleOrg({ tagCode: 'org_supplier', asTenant: true }).then(res => {
        this.orgList = res.data
      })
    },
    executeButtonMethod(event, button, row) {
      let target = event.target
      while (target.nodeName !== 'BUTTON') {
        target = target.parentNode
      }
      target.blur()
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    handleCommand(command) {
      this[command]()
    },
    handleSelectionChange(value) {
      this.statisticalData.checkedDatasetAmount = value.length
      this.statisticalData.checkedDataAmount = value
        ? value.reduce((pre, cur) => {
            return pre + cur.dataAmount
          }, 0)
        : 0
      this.statisticalData.checkedDataSize = value
        ? value.reduce((pre, cur) => {
            return pre + cur.size
          }, 0)
        : 0
    },
    refresh() {
      this.$refs.tableRef.query()
    },
    add() {
      this.$refs.formRef.show({
        formData: {
          type: this.queryParam.type
        },
        status: 'add',
        title: this.$t('添加数据集')
      })
    },
    generateDataset() {
      this.$refs.generateDatasetRef.show()
    },
    generateDatasetConfirm() {
      this.refresh()
    },
    initTable() {
      this.$refs.tableRef.initTable()
    },
    batchTempStorage() {
      let selectedData = this.$refs.tableRef.selectedData
      if (!selectedData?.length) {
        showToast('请选择数据', 'warning')
      } else {
        this.$refs.tempStorageRef.show({
          selectedData: selectedData,
          isBatch: true
        })
      }
    },
    tempStorageConfirm() {
      this.$refs.tableRef.clearTableSelection()
    },
    xLabelRefConfirm() {
      this.$refs.tableRef.clearTableSelection()
    },
    batchSendLabeling() {
      let selectedData = this.$refs.tableRef.selectedData
      if (!selectedData?.length && !this.allChecked) {
        return showToast('请选择数据', 'warning')
      }
      let projectCodeList = [...new Set(selectedData.map(item => item.projectCode))]
      if (projectCodeList?.length !== 1) {
        showToast('只可以选择同一个项目进行送标', 'warning')
        return
      }
      let hasOtherStatus = selectedData.some(
        item => item.status !== DATA_SET_STATUS.TO_BE_SENT && item.status !== DATA_SET_STATUS.SENT_FAIL
      )
      if (hasOtherStatus) {
        return showToast('只有待送标以及送标失败的数据集可以批量送标', 'warning')
      }
      let datasetIdList = selectedData.map(item => item.id)
      getSendForLabelingDestination({ datasetIdList: datasetIdList }).then(res => {
        if (res.data == 1) {
          this.$refs.batchSendLabelingRef.show({
            selectedData: selectedData
          })
          return
        }
        if (res.data === 0) {
          this.$refs.xLabelRef.show({
            selectedData: selectedData,
            projectCode: projectCodeList[0]
          })
          return
        }
        showConfirmToast({
          message: BASE_CONSTANT.PUSH_TO_AI_MSG
        }).then(res => {
          warmUp({ datasetIdList: datasetIdList }).then(res => {
            showToast('开始预热至AI平台', 'success')
            this.$refs.tableRef.clearTableSelection()
          })
        })
      })
    },
    batchSendLabelingConfirm() {
      this.$refs.tableRef.clearTableSelection()
    },
    batchDataCopy() {
      let selectedData = this.$refs.tableRef.selectedData
      if (!selectedData.length && !this.allChecked) {
        return showToast('请选择数据', 'warning')
      }
      this.$refs.batchCopyRef.show(selectedData, this.allChecked, this.queryParam)
    },
    batchDataCopyConfirm() {
      this.allChecked = false
      this.$refs.tableRef.clearSelections()
    },
    batchUpdateScenario() {
      let selectedData = this.$refs.tableRef.selectedData
      if (!selectedData.length && !this.allChecked) {
        return showToast('请选择数据', 'warning')
      }
      let postData = {}
      if (this.allChecked) {
        postData = JSON.parse(JSON.stringify(this.queryParam))
        delete postData.current
        delete postData.size
      } else {
        let idList = selectedData.map(item => item.id)
        let idStr = idList.join(',')
        postData = {
          idList: idStr
        }
      }
      markUpScenarioByRule(postData).then(res => {
        showToast('更新场景成功', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    parseParams() {
      let selectedData = this.$refs.tableRef.selectedData
      if (!selectedData.length && !this.allChecked) {
        showToast('请选择数据集', 'warning')
        return null
      }
      let idList = selectedData.map(item => item.id)
      let postData = {
        idList: idList
      }
      return postData
    },
    batchDemotion() {
      let postData = this.parseParams()
      if (!postData) return
      this.$refs.poseRef.show(postData, LABELED_DATA_SET_BATCH_FUNC.BATCH_DEMOTION)
    },
    poseConfirm(postData, command) {
      if (command === LABELED_DATA_SET_BATCH_FUNC.BATCH_DEMOTION) {
        batchDemotion(postData).then(res => {
          showToast('批量去畸变成功', 'success')
          this.allChecked = false
          this.$refs.tableRef.clearTableSelection()
        })
        return
      }
      batchTenHZDemotion(postData).then(res => {
        showToast('开始批量10hz去畸变', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchFusionLidar() {
      let postData = this.parseParams()
      if (!postData) return
      batchFusionLidar(postData).then(res => {
        showToast('批量雷达融合成功', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchFilter() {
      let postData = this.parseParams()
      if (!postData) return
      batchFilter(postData).then(res => {
        showToast('批量过滤数据成功', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchPreProcessFrameExtraction() {
      let postData = this.parseParams()
      if (!postData) return
      batchPreProcessFrameExtraction(postData.idList).then(res => {
        showToast('开始批量预处理抽帧', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchStartIcp() {
      let postData = this.parseParams()
      if (!postData) return
      batchStartIcp(postData).then(res => {
        showToast('开始批量ICP', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchStartTenHZIcp() {
      let postData = this.parseParams()
      if (!postData) return
      batchStartTenHZIcp(postData).then(res => {
        showToast('开始批量10hzICP', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchRosBagFrame() {
      let postData = this.parseParams()
      if (!postData) return
      batchRosBagFrame(postData).then(res => {
        showToast('开始批量rosbag抽帧', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchUpdateStatus() {
      let postData = this.parseParams()
      if (!postData) return
      this.$refs.updateStatusRef.show(postData)
    },
    updateStatusConfirm(formData) {
      batchUpdateStatus(formData).then(res => {
        showToast('开始批量更新数据集状态', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchConvert() {
      let postData = this.parseParams()
      if (!postData) return
      this.$refs.convertRef.show(postData)
    },
    convertConfirm(formData) {
      batchConvert(formData).then(res => {
        showToast('开始批量数据集转换', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchDatasetDeduplication() {
      let postData = this.parseParams()
      if (!postData) return
      batchDatasetDeduplication(postData).then(res => {
        showToast('开始批量数据集去重', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchAbnormalLabelDelete() {
      let postData = this.parseParams()
      if (!postData) return
      batchAbnormalLabelDelete(postData).then(res => {
        showToast('开始批量送标回退', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchTenHZMainLidarFrameExtraction() {
      let postData = this.parseParams()
      if (!postData) return
      batchTenHZMainLidarFrameExtraction(postData).then(res => {
        showToast('开始MainLidar10HZ抽帧', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    coldHandleDataset() {
      let postData = this.parseParams()
      if (!postData) return
      coldHandleDataset(postData).then(res => {
        showToast('开始数据集冷处理', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    hotHandleDataset() {
      let postData = this.parseParams()
      if (!postData) return
      hotHandleDataset(postData).then(res => {
        showToast('开始数据集热加载', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchTenHZDemotion() {
      let postData = this.parseParams()
      if (!postData) return
      this.$refs.poseRef.show(postData, LABELED_DATA_SET_BATCH_FUNC.BATCH_TEN_HZ_DEMOTION)
    },
    batchTenHZFusionLidar() {
      let postData = this.parseParams()
      if (!postData) return
      batchTenHZFusionLidar(postData).then(res => {
        showToast('开始批量10hz去雷达融合', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchCreatePose() {
      let postData = this.parseParams()
      if (!postData) return
      this.dialogPoseVisible = true
      this.$nextTick(() => {
        this.$refs.poseDialog.show()
      })
    },
    createPoseTask(formData) {
      let postData = this.parseParams()
      const param = {
        datasetIdList: postData.idList,
        ...formData
      }
      batchCreatePoseTask(param).then(res => {
        this.dialogPoseVisible = false
        this.allChecked = false
        showToast('批量创建Pose质检任务成功')
        this.$refs.tableRef.clearTableSelection()
      })
    },
    dialogClosed() {
      this.dialogPoseVisible = false
      this.allChecked = false
      this.$refs.tableRef.clearTableSelection()
    },
    batchSlamPose() {
      let postData = this.parseParams()
      if (!postData) return
      batchSlamPose(postData).then(res => {
        showToast('开始批量建图pose', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchSubmitAcceptance() {
      let postData = this.parseParams()
      if (!postData) return
      let index = this.$refs.tableRef.selectedData.findIndex(item => item.status !== DATA_SET_STATUS.SELECTED)
      if (index > -1) {
        showToast('只有挑选完成的数据可以进行批量提交验收', 'warning')
        return
      }
      this.$refs.employeeSelectionRef.show('submit')
    },
    batchTransferAcceptance() {
      let postData = this.parseParams()
      if (!postData) return
      let index = this.$refs.tableRef.selectedData.findIndex(item => item.status !== DATA_SET_STATUS.TO_BE_ACCEPTANCE)
      if (index > -1) {
        showToast('只有待验收的数据可以进行批量转交验收', 'warning')
        return
      }
      this.$refs.employeeSelectionRef.show('transfer')
    },
    employeeSelectionConfirm(data, type) {
      if (type === 'submit') {
        this.batchSubmitAcceptanceConfirm(data)
        return
      }
      this.batchTransferAcceptanceConfirm(data)
    },
    batchSubmitAcceptanceConfirm(data) {
      let idList = this.$refs.tableRef.selectedData.map(item => item.id)
      let postData = {
        queryParam: {
          idList: idList
        },
        selectionAcceptanceEmpId: data.empId,
        selectionAcceptanceEmpName: data.empName
      }
      batchSubmitAcceptance(postData).then(res => {
        showToast('成功提交验收')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
        this.$refs.employeeSelectionRef.dialogClosed()
      })
    },
    batchTransferAcceptanceConfirm(data) {
      let idList = this.$refs.tableRef.selectedData.map(item => item.id)
      let postData = {
        queryParam: {
          idList: idList
        },
        selectionAcceptanceEmpId: data.empId,
        selectionAcceptanceEmpName: data.empName
      }
      batchTransferAcceptance(postData).then(res => {
        showToast('成功转移验收')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
        this.$refs.employeeSelectionRef.dialogClosed()
      })
    },
    batchCompleteTenHzData() {
      let selectedData = this.$refs.tableRef.selectedData
      const allFrameData = selectedData.every(item => item.dataType === 'frame_data')
      if (!allFrameData) return showToast('请去除不是frame_data类型的数据集', 'warning')
      let postData = this.parseParams()
      if (!postData) return
      batchCompleteTenHzData(postData.idList).then(res => {
        showToast('批量按包补全10hz数据', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchStartDsSlam() {
      let selectedData = this.$refs.tableRef.selectedData
      if (!selectedData.length && !this.allChecked) {
        return showToast('请选择数据', 'warning')
      }
      let idList = selectedData.map(item => item.id)
      this.$refs.generateRef.show({ resourceIdList: idList })
    },
    generateDatasetWithProcessTypeConfirm(formData) {
      let postData = {
        processType: formData.processType,
        resourceIdList: formData.resourceIdList,
        resourceType: 'DATASET',
        ext: {}
      }
      if (formData.poseAlgorithm) {
        postData.ext.poseAlgorithm = formData.poseAlgorithm
      }
      if (formData.stages?.length) {
        postData.stages = formData.stages
      }
      if (formData.projectId) {
        postData.ext.projectId = formData.projectId
      }
      batchStartDsSlam(postData).then(res => {
        showToast('开始批量DS建图')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchDownloadInput() {
      let selectedData = this.$refs.tableRef.selectedData
      if (!selectedData.length && !this.allChecked) {
        return showToast('请选择数据', 'warning')
      }
      let isAllFrameData = !selectedData.some(item => item.dataType !== 'frame_data')
      if (!isAllFrameData) return showToast('只有抽帧数据类型的数据集支持下载', 'warning')
      let idList = selectedData.map(item => item.id)
      this.$refs.downloadInputRef.show({ resourceIdList: idList })
    },
    downloadInputConfirm(formData) {
      let postData = {
        ...formData,
        sourceId: this.$store.state.permission.currentUser.empId,
        resourceType: PROCESS_RESOURCE_TYPE.DATASET
      }
      downloadInput(postData).then(res => {
        showToast('下载准备中，请前往我的下载查看下载进度')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchPullData() {
      let selectedData = this.$refs.tableRef.selectedData
      if (!selectedData.length && !this.allChecked) {
        return showToast('请选择数据', 'warning')
      }
      this.$refs.pullDataRef.show(selectedData)
    },
    batchDownloadMetaInfo() {
      let selectedData = this.$refs.tableRef.selectedData
      if (!selectedData.length && !this.allChecked) {
        return showToast('请选择数据', 'warning')
      }
      this.$refs.downloadMetaRef.show()
    },
    downloadMetaConfirm(formData) {
      let selectedData = this.$refs.tableRef.selectedData
      let datasetIdList = selectedData.map(item => item.id)
      let postData = {
        datasetIdList: datasetIdList,
        innerDataQueryParam: {
          includeFieldList: formData.includeFieldList
        },
        fileName: formData.fileName
      }
      batchDownloadMetaInfo(postData).then(res => {
        showToast('下载准备中，请前往我的下载查看下载进度')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchSplitDataset() {
      let postData = this.parseParams()
      if (!postData) return
      batchSplitDataset(postData).then(res => {
        showToast('批量拆分数据集', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    batchSplitAisleDataset() {
      let selectedData = this.$refs.tableRef.selectedData
      const parkingLotSlamDatasetList = selectedData.filter(item =>
        item.name.toLowerCase().includes('parking_lot_slam')
      )
      // if(!parkingLotSlamDatasetList?.length) return showToast('仅支持parking_lot_slam类型的数据集拆分甬道数据，您选中的数据集中没有parking_lot_slam的类型','warning')
      this.$refs.aisleRef.show(parkingLotSlamDatasetList)
    },
    batchDeepCopy(){
      let postData = this.parseParams()
      if (!postData) return
      batchDeepCopy(postData).then(res => {
        showToast('批量深拷贝数据集', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    },
    splitAisleDatasetConform(){
      this.allChecked = false
      this.$refs.tableRef.clearTableSelection()
    },
    goBackMainPage() {
      this.mapResultVisible = false
      this.$nextTick(() => {
        this.$refs.tableRef?.highlightCurrentRow(this.currentRow)
      })
    },
    goBack(status, statusName) {
      this.innerDataPageVisible = false
      this.$nextTick(() => {
        this.$refs.tableRef?.highlightCurrentRow(this.currentRow, status, statusName)
      })
    },
    viewMapResult(row) {
      this.currentRow = row
      getProcessResult(row.id).then(res => {
        if (res.data) {
          this.recordsData = res.data
        } else {
          this.recordsData = null
        }
        this.mapResultVisible = true
      })
    },
    handleNameClick(row) {
      this.currentRow = row
      let current
      this.writable =
        (this.currentUser.empId === row.manualSelectionEmpId && row.status === DATA_SET_STATUS.SELECTING) ||
        (this.currentUser.empId === row.selectionCheckEmpId && row.status === DATA_SET_STATUS.CHECKING) ||
        [DATA_SET_STATUS.TO_BE_SELECTED, DATA_SET_STATUS.SELECTED, DATA_SET_STATUS.SLAM_MAP_SUCCESS].includes(
          row.status
        )
      this.datasetId = row.id
      this.datasetName = row.name
      this.updatable = row.updatable
      this.innerDataPageVisible = true
    },
    openDialog() {
      this.visible = true
    },
    batchRemove() {
      let postData = this.parseParams()
      if (!postData) return
      let index = this.$refs.tableRef.selectedData.findIndex(item => item.status !== DATA_SET_STATUS.SLAM_MAP_FAIL)
      if (index > -1) {
        showToast('只有建图失败的数据才可以删除', 'warning')
        return
      }
      batchRemove(postData).then(res => {
        showToast('开始批量删除', 'success')
        this.allChecked = false
        this.$refs.tableRef.clearTableSelection()
      })
    }
  }
}
</script>
<style scoped lang="scss">
.el-card {
  .ltw-toolbar {
    display: flex;
    justify-content: space-between;

    .right-button {
      display: flex;

      .all-checked-button {
        margin-right: 5px;
      }
    }

    .input-button {
      width: 250px;
    }

    .batch-button {
      margin-right: 12px;
    }
  }
}
</style>
