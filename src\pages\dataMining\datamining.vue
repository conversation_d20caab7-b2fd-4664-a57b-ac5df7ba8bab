<template>
    <div class="ltw-page-container data-mining-initiated">
      <!-- 固定顶部进度条 -->
      <div class="fixed-steps">
        <el-steps :active="currentStep" class="steps">
          <el-step title="步骤一" description="数据筛选"></el-step>
          <el-step title="步骤二" description="静态筛选"></el-step>
          <el-step title="步骤三" description="自车筛选"></el-step>
          <el-step title="步骤四" description="发起任务"></el-step>
        </el-steps>
      </div>
      <!-- 自适应内容区 -->
      <div class="card-area">
        <el-scrollbar class="card-scroll">
          <div v-if="currentStep === 0">
            <h3>数据筛选</h3>
            <el-form label-width="120px" class="step1-form" label-position="left">
              <!-- 变量（多选） -->
              <el-form-item label="车辆（支持多选）">
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                  <div style="flex: 1"></div>
                  <div>
                    <el-button type="primary" size="small" @click="addVariable">新增</el-button>
                    <el-button type="danger" size="small" @click="resetVariables">重置</el-button>
                  </div>
                </div>
              </el-form-item>
              <div class="info-box">
                <el-tag
                  v-for="(item, idx) in selectedVariables"
                  :key="item"
                  closable
                  @close="removeVariable(item)"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ item }}
                </el-tag>
                <span v-if="selectedVariables.length === 0" style="color: #bbb">请选择车辆</span>
              </div>
              <!-- 时间范围（多选，必填） -->
              <el-form-item label="时间段（支持多选）" required label-width="130px">
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                  <div style="flex: 1"></div>
                  <div>
                    <el-button type="primary" size="small" @click="addDate">新增</el-button>
                    <el-button type="danger" size="small" @click="resetDates">重置</el-button>
                  </div>
                </div>
              </el-form-item>
              <div class="info-box">
                <el-tag
                  v-for="(item, idx) in selectedDates"
                  :key="item"
                  closable
                  @close="removeDate(idx)"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ item[0] }} - {{ item[1] }}
                </el-tag>
                <span v-if="selectedDates.length === 0" style="color: #bbb">请选择时间段</span>
              </div>
              <!-- 每日时间段（多选） -->
              <el-form-item label="每日时间范围（支持多选）" label-width="150px">
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                  <div style="flex: 1"></div>
                  <div>
                    <el-button type="primary" size="small" @click="addTimeRange">新增</el-button>
                    <el-button type="danger" size="small" @click="resetTimeRanges">重置</el-button>
                  </div>
                </div>
              </el-form-item>
              <div class="info-box">
                <el-tag
                  v-for="(item, idx) in selectedTimeRanges"
                  :key="item"
                  closable
                  @close="removeTimeRange(idx)"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ item[0] }} - {{ item[1] }}
                </el-tag>
                <span v-if="selectedTimeRanges.length === 0" style="color: #bbb">请选择每日时间范围</span>
              </div>
              <!-- 标签规则 -->
              <el-form-item label="标签规则（支持多选）" label-width="130px">
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                  <div style="flex: 1"></div>
                  <div>
                    <el-button type="primary" size="small" @click="addTag">新增</el-button>
                    <el-button type="danger" size="small" @click="resetTags">重置</el-button>
                  </div>
                </div>
              </el-form-item>
              <div class="info-box">
                <el-tag
                  v-for="(item, idx) in selectedTags"
                  :key="item"
                  closable
                  @close="removeTag(idx)"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ item }}
                </el-tag>
                <span v-if="selectedTags.length === 0" style="color: #bbb">请选择标签</span>
              </div>
              <!-- 过滤区域 -->
              <el-form-item label="选择区域（支持多选）" label-width="140px">
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                  <div style="flex: 1"></div>
                  <div>
                    <el-button type="primary" size="small" @click="addRegion">新增</el-button>
                    <el-button type="primary" size="small" @click="manualRegionSelect">手动框选</el-button>
                    <el-button type="danger" size="small" @click="resetRegions">重置</el-button>
                  </div>
                </div>
              </el-form-item>
              <div class="info-box">
                <el-tag
                  v-for="(item, idx) in selectedRegionsName"
                  :key="item"
                  closable
                  @close="removeRegion(idx)"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ item }}
                </el-tag>
                <span v-if="selectedRegionsName.length === 0" style="color: #bbb">请选择区域</span>
              </div>
            </el-form>
          </div>
          <div v-if="currentStep === 1">
            <h3>静态筛选</h3>
            <!-- 静态标签输入框区域 -->
            <el-form label-width="130px" class="static-form" label-position="left">
              <el-form-item label="静态标签（支持多选）">
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                  <div style="flex: 1"></div>
                  <el-button type="danger" size="small" @click="resetStaticTags">重置</el-button>
                </div>
              </el-form-item>
            </el-form>
            <div class="info-box">
              <div style="flex: 1; min-height: 40px">
                <el-tag
                  v-for="tag in selectedStaticTags"
                  :key="tag.id"
                  closable
                  @close="removeStaticTag(tag)"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ tagLabelWithBuffer(tag) }}
                </el-tag>
                <span v-if="selectedStaticTags.length === 0" style="color: #bbb;font-size:small">请选择静态标签</span>
              </div>
            </div>
            <!-- 静态标签卡片区域 -->
            <div class="static-tag-cards">
              <el-card v-for="cat in staticTagCategories" :key="cat.type" class="static-tag-card">
                <template #header>
                  <el-checkbox
                    :model-value="isAllChecked(cat)"
                    :indeterminate="isIndeterminate(cat)"
                    @change="(val) => toggleCategory(cat, val)"
                  >
                    {{ cat.name }}
                  </el-checkbox>
                </template>
                
                  <div v-for="tag in cat.tags" :key="tag.id" class="tag-item">
                    <div class="tag-checkbox">
                      <el-checkbox v-model="checkedTagIds" :label="String(tag.id)">
                        {{ tag.name }}
                      </el-checkbox>
                    </div>
                    <div v-if="isTagChecked(tag)" class="tag-buffer-settings">
                      <div>
                        <el-checkbox
                          v-model="bufferSettings[String(tag.id)].enabled"
                        >匹配缓冲区</el-checkbox>
                      </div>
                      <div class="tag-box" v-if="bufferSettings[String(tag.id)]?.enabled">
                        <el-radio-group 
                          v-model="bufferSettings[String(tag.id)].type" 
                          size="small"
                          @change="(val) => handleBufferTypeChange(String(tag.id), val)"
                          @click.stop
                        >
                          <el-radio-button label="distance">距离</el-radio-button>
                          <el-radio-button label="lane">车道</el-radio-button>
                        </el-radio-group>
                        <div class="number-input-wrapper" @click.stop @mousedown.stop>
                          <el-input-number
                            v-model="bufferSettings[String(tag.id)].value"
                            :min="0"
                            :max="bufferSettings[String(tag.id)].type === 'distance' ? 100 : 10"
                            @change="(val) => handleBufferValueChange(String(tag.id), val)"
                            style="width:150px;"
                          />
                        </div>
                        <span class="unit">{{ bufferSettings[String(tag.id)].type === 'distance' ? 'm' : '个' }}</span>
                      </div>
                    </div>
                  </div>
            
              </el-card>
            </div>
          </div>
          <div v-if="currentStep === 2">
            <h3>自车筛选</h3>
            <!-- 上方标签输入框区域 -->
            <el-form  class="self-form" label-position="left">
              <el-form-item label="自车标签（支持多选）">
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                  <div style="flex: 1"></div>
                  <el-button type="danger" size="small" @click="resetSelfTags">重置</el-button>
                </div>
              </el-form-item>
              <div class="info-box">
                <div style="flex: 1; min-height: 40px">
                  <el-tag
                    v-for="tag in selfSelectedTags"
                    :key="tag.id"
                    closable
                    @close="removeSelfTag(tag)"
                    style="margin-right: 8px; margin-bottom: 4px"
                  >
                    {{ selfTagLabelWithSettings(tag) }}
                  </el-tag>
                  <span v-if="selfSelectedTags.length === 0" style="color: #bbb">请选择标签</span>
                </div>
              </div>
            </el-form>
  
            <!-- 下方全量标签列表卡片 -->
            <el-card class="self-tag-list-card">
              <template #header>
                <div class="self-tag-list-header">
                  <el-checkbox
                    v-model="selfAllChecked"
                    :indeterminate="selfIndeterminate"
                    @change="handleSelfCheckAllChange"
                  >
                    全部标签
                  </el-checkbox>
                </div>
              </template>
              <div class="self-tag-list self-tag-list-grid">
                <div
                  v-for="tag in allTags"
                  :key="tag.id"
                  class="self-tag-item"
                >
                  <el-checkbox
                    v-model="selfCheckedTagIds"
                    :label="String(tag.id)"
                  >
                    {{ tag.name }}
                  </el-checkbox>
                  <template v-if="isSelfTagChecked(tag)">
                    <div class="tag-settings">
                      <!-- 匹配缓冲区设置 -->
                      <div class="setting-item">
                        <el-checkbox
                          v-model="selfBufferSettings[String(tag.id)].enabled"
                        >匹配缓冲区</el-checkbox>
                      </div>
                      <div v-if="selfBufferSettings[String(tag.id)].enabled" class="setting-content">
                        <el-radio-group
                          v-model="selfBufferSettings[String(tag.id)].type"
                          size="small"
                        >
                          <el-radio-button label="distance">距离</el-radio-button>
                          <el-radio-button label="lane">车道</el-radio-button>
                        </el-radio-group>
                        <el-input-number
                          v-model="selfBufferSettings[String(tag.id)].value"
                          :min="0"
                          size="small"
                          style="width: 120px; margin-left: 8px;"
                        />
                        <span class="unit">
                          {{ selfBufferSettings[String(tag.id)].type === 'distance' ? 'm' : '个' }}
                        </span>
                      </div>
                      <!-- 数据补全设置 -->
                      <div class="setting-item">
                        <el-checkbox
                          v-model="selfPatchSettings[String(tag.id)].enabled"
                        >数据补全</el-checkbox>
                      </div>
                      <div v-if="selfPatchSettings[String(tag.id)].enabled" class="setting-content">
                        <el-radio-group
                          v-model="selfPatchSettings[String(tag.id)].type"
                          size="small"
                        >
                          <el-radio-button label="distance">距离</el-radio-button>
                          <el-radio-button label="time">时间</el-radio-button>
                        </el-radio-group>
                        <el-input-number
                          v-model="selfPatchSettings[String(tag.id)].value"
                          :min="0"
                          size="small"
                          style="width: 120px; margin-left: 8px;"
                        />
                        <span class="unit">
                          {{ selfPatchSettings[String(tag.id)].type === 'distance' ? 'm' : 's' }}
                        </span>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </el-card>
          </div>
          <div v-if="currentStep === 3">
            <h3>确认任务内容</h3>
            <el-card class="step4-card" shadow="never">
              <div class="card-header-flex">
                <span class="card-title">数据筛选</span>
                <el-button type="primary" size="small" @click="goToStep(0)">编辑</el-button>
              </div>
              <div class="card-section">
                <div class="section-label">车辆（支持多选）</div>
                <div class="info-box">
                <el-tag
                  v-for="(item, idx) in selectedVariables"
                  :key="item"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ item }}
                </el-tag>
              </div>
              </div>
              <div class="card-section">
                <div class="section-label">时间段（支持多选）</div>
                <div class="info-box">
                <el-tag
                  v-for="(item, idx) in selectedDates"
                  :key="item"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ item[0] }} - {{ item[1] }}
                </el-tag>
  
              </div>
              </div>
              <div class="card-section">
                <div class="section-label">标签规则</div>
                <div class="info-box">
                <el-tag
                  v-for="(item, idx) in selectedTags"
                  :key="item"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ item }}
                </el-tag>
              </div>
              </div>
              <div class="card-section">
                <div class="section-label">选择区域</div>
                <div class="info-box">
                <el-tag
                  v-for="(item, idx) in selectedRegionsName"
                  :key="item"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ item }}
                </el-tag>
              </div>
              </div>
            </el-card>
            <el-card class="step4-card" shadow="never">
              <div class="card-header-flex">
                <span class="card-title">静态筛选</span>
                <el-button type="primary" size="small" @click="goToStep(1)">编辑</el-button>
              </div>
              <div class="card-section">
                <div class="section-label">静态标签</div>
                <div class="info-box">
                <div style="flex: 1; min-height: 40px">
                <el-tag
                  v-for="tag in selectedStaticTags"
                  :key="tag.id"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ tagLabelWithBuffer(tag) }}
                </el-tag>
              </div>
            </div>
              </div>
            </el-card>
            <el-card class="step4-card" shadow="never">
              <div class="card-header-flex">
                <span class="card-title">自车筛选</span>
                <el-button type="primary"  size="small" @click="goToStep(2)">编辑</el-button>
              </div>
              <div class="card-section">
                <div class="section-label">自车标签</div>
                <div class="info-box">
                <div style="flex: 1; min-height: 40px">
                  <el-tag
                    v-for="tag in selfSelectedTags"
                    :key="tag.id"
                    style="margin-right: 8px; margin-bottom: 4px"
                  >
                    {{ selfTagLabelWithSettings(tag) }}
                  </el-tag>
                </div>
              </div>
              </div>
            </el-card>
          
            <!-- <div class="button-group step4-btns">
               <el-button @click="prevStep">上一步</el-button>
               <el-button type="primary" @click="submitTask">发起任务</el-button>
             </div> -->
          </div>
        </el-scrollbar>
        <!-- 固定右下角按钮 -->
        <div class="fixed-btn-group">
          <el-button @click="prevStep" :disabled="currentStep === 0">上一步</el-button>
          <el-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</el-button>
          <el-button v-else type="primary" @click="submitTask">发起任务</el-button>
        </div>
      </div>
  
      <!-- 任务成功弹窗 -->
      <el-dialog v-model="successDialogVisible" width="800px" :show-close="true" center>
        <div style="font-size: 20px; font-weight: 500; margin-bottom: 32px;">数据挖掘任务已成功发起！</div>
        <el-steps :active="4" finish-status="success" align-center>
          <el-step title="Step 1" description="数据筛选" />
          <el-step title="Step 2" description="静态筛选" />
          <el-step title="Step 3" description="自车筛选" />
          <el-step title="Step 4" description="发起任务" />
        </el-steps>
        <div style="text-align: right; margin-top: 32px;">
          <el-button @click="handleSuccessDialog" type="primary">确定</el-button>
        </div>
      </el-dialog>
  
      <!-- 弹窗统一入口 -->
      <div v-if="dialogVisible" class="dialog-container">
        <el-dialog v-model="dialogVisible" :title="dialogTitleMap[dialogType]" :width="dialogWidthMap[dialogType]">
          <template v-if="dialogType === 'vehicle'">
            <bs-vehicle-selection
                :data="vehicleList"
                :auto-load="false"
                modelCode="vin"
                v-model="tempSelectedVariables"
                ref="vehicleRef"
                clearable
                multiple
                filterable
                size="small"
              ></bs-vehicle-selection>
            <!-- <el-select
              v-model="tempSelectedVariables"
              multiple
              filterable
              allow-create
              default-first-option
              placeholder="请输入或选择车辆"
              style="width: 100%"
            >
              <el-option v-for="item in variableOptions" :key="item" :label="item" :value="item" />
            </el-select> -->
          </template>
          <template v-else-if="dialogType === 'tag'">
            <el-select
              v-model="tempSelectedTags"
              multiple
              filterable
              allow-create
              default-first-option
              placeholder="请输入或选择标签"
              style="width: 100%"
            >
              <el-option v-for="item in tagOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </template>
          <template v-else-if="dialogType === 'region'">
            <el-cascader
              v-model="tempSelectedRegions"
              :placeholder="$t('请选择行政区')"
              :options="cantonTreeList"
              :props="cantonListProps"
              clearable
              filterable
            />
          </template>
          <template v-else-if="dialogType === 'date'">
            <el-date-picker
              v-model="tempDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </template>
          <template v-else-if="dialogType === 'time'">
            <el-time-picker
              v-model="tempTimeRange"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 100%"
            />
          </template>
          <template v-else-if="dialogType === 'manualSelect'">
            <TMapEditor v-if="dialogType === 'manualSelect'" ref="TMapEditorRef" @updateDistrictCode="handleUpdateDistrictCode"/>
          </template>
          <template #footer>
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleDialogConfirm">确定</el-button>
          </template>
        </el-dialog>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, watch, computed } from 'vue'
  import { showToast, showConfirmToast } from '@/plugins/util'
  import { getSysCantonTree } from '@/apis/system/sys-canton'
//   import TMapEditor from '@/components/geography/TMapEditor.vue'
  import BsVehicleSelection from '@/components/basic/BsVehicleSelection.vue'
  import { listBsVehicle } from '@/apis/basic/bs-vehicle'
  
  const currentStep = ref(0)
  const TMapEditorRef = ref(null)
  const prevStep = () => {
    if (currentStep.value > 0) {
      currentStep.value--
    }
  }
  
  const nextStep = () => {
    if (currentStep.value < 3) {
      currentStep.value++
    }
  }
  
  const vehicleList = ref([])
  const selectedVariables = ref([])
  const tempSelectedVariables = ref([])
  const dialogVisible = ref(false)
  const dialogType = ref('') // vehicle | tag | region | date | time| manualSelect
  const dialogTitleMap = {
    vehicle: '选择车辆',
    tag: '选择标签',
    region: '选择区域',
    date: '选择时间段',
    time: '选择每日时间范围',
    manualSelect: '手动选择区域范围'
  }
  const dialogWidthMap = {
    vehicle: '400px',
    tag: '400px',
    region: '600px',
    date: '400px',
    time: '400px',
    manualSelect: '800px'
  }
  
  const cantonListProps = {
    value: 'locationArr',
    label: 'name',
    checkStrictly: true,
    emitPath: false
  }
  
  const cantonTreeList = ref([])
  const selectedCatTags = ref([])
  const checkAll = ref([])
  
  const listVehicle = () => {
        return listBsVehicle().then(res => {
          vehicleList.value = res.data
        })
      }
  const extractLocationArr = item => {
    if (item.latitude !== undefined && item.longitude !== undefined) {
      return [[item.latitude, item.longitude], item.nameLink, item.code]
    }
    return []
  }
  
  const addLocationField = data => {
    return data.map(item => {
      item.locationArr = extractLocationArr(item)
      if (item.children?.length) {
        item.children = addLocationField(item.children)
      }
      return item
    })
  }
  
  const listCantonTree = () => {
    if (cantonTreeList.value?.length) return
    getSysCantonTree().then(res => {
      cantonTreeList.value = addLocationField(res.data)
    })
  }
  
  const addVariable = () => openDialog('vehicle')
  const resetVariables = () => {
    selectedVariables.value = []
  }
  const resetTags = () => {
    selectedTags.value = []
  }
  const removeVariable = item => {
    selectedVariables.value = selectedVariables.value.filter(v => v !== item)
  }
  const handleDialogConfirm = () => {
    if (dialogType.value === 'vehicle') {
      tempSelectedVariables.value.forEach(item => {
        if (!selectedVariables.value.includes(item)) {
          selectedVariables.value.push(item)
        } else {
          showToast('请勿重复添加标签', 'warning')
        }
      })
      tempSelectedVariables.value = []
    } else if (dialogType.value === 'tag') {
      tempSelectedTags.value.forEach(item => {
        if (!selectedTags.value.includes(item)) {
          selectedTags.value.push(item)
        } else {
          showToast('请勿重复添加标签', 'warning')
        }
      })
      tempSelectedTags.value = []
    } else if (dialogType.value === 'region') {
      const label = tempSelectedRegions.value[1]
      const code = tempSelectedRegions.value[2]
      if (!selectedRegionContonList.value.includes(code)) {
        selectedRegionsName.value.push(label)
        selectedRegionContonList.value.push(code)
      } else {
        showToast('请勿重复添加区域', 'warning')
      }
      tempSelectedRegions.value = ''
    } else if (dialogType.value === 'date') {
      if (tempDateRange.value && tempDateRange.value.length === 2) {
        selectedDates.value.push([...tempDateRange.value])
        tempDateRange.value = []
      }
    } else if (dialogType.value === 'time') {
      if (tempTimeRange.value && tempTimeRange.value.length === 2) {
        selectedTimeRanges.value.push([...tempTimeRange.value])
        tempTimeRange.value = []
      }
    }else if( dialogType.value === 'manualSelect'){
       if (tempSelectedRegionCode.value && tempSelectedRegionName.value) {
        if (!selectedRegionContonList.value.includes(tempSelectedRegionCode.value)) {
          selectedRegionsName.value.push(tempSelectedRegionName.value)
          selectedRegionContonList.value.push(tempSelectedRegionCode.value)
        } else {
          showToast('请勿重复添加区域', 'warning')
        }
        // 清空临时数据
        tempSelectedRegionCode.value = ''
        tempSelectedRegionName.value = ''
      } else {
        showToast('未选择有效区域', 'warning')
      }
     TMapEditorRef.value.destroyMap()
    }
    dialogVisible.value = false
  }
  
  const selectedDates = ref([])
  const tempDateRange = ref([])
  const addDate = () => openDialog('date')
  const removeDate = idx => {
    selectedDates.value.splice(idx, 1)
  }
  const resetDates = () => {
    selectedDates.value = []
  }
  
  const selectedTimeRanges = ref([])
  const tempTimeRange = ref([])
  const addTimeRange = () => openDialog('time')
  const removeTimeRange = idx => {
    selectedTimeRanges.value.splice(idx, 1)
  }
  const resetTimeRanges = () => {
    selectedTimeRanges.value = []
  }
  
  const tagOptions = ref(['Tag X', 'Tag Y', 'Tag Z'])
  const selectedTags = ref([])
  const addTag = () => openDialog('tag')
  const tempSelectedTags = ref([])
  const removeTag = idx => {
    selectedTags.value.splice(idx, 1)
  }
  
  const regionDialogVisible = ref(false)
  const tempSelectedRegions = ref('')
  const tempSelectedRegionCode = ref('')
  const tempSelectedRegionName = ref('')
  
  const selectedRegionsName = ref([])
  const selectedRegionContonList = ref([])
  const addRegion = () => openDialog('region')
  const removeRegion = idx => {
    selectedRegionsName.value.splice(idx, 1)
    selectedRegionContonList.value.splice(idx, 1)
  }
  const resetRegions = () => {
    selectedRegionsName.value = []
    selectedRegionContonList.value = []
  }
  const manualRegionSelect = () => {
    openDialog('manualSelect')
  }
  const handleUpdateDistrictCode =(addressCode,addressName)=>{
    tempSelectedRegionCode.value = addressCode
    tempSelectedRegionName.value = addressName
  }
  
  // 打开弹窗方法
  const openDialog = type => {
    dialogType.value = type
    dialogVisible.value = true
  }
  
  // 步骤二静态标签相关
  const staticTagCategories = ref([
    { type: 'text', name: '文字', tags: [] },
    { type: 'number', name: '数字', tags: [] },
    { type: 'symbol', name: '符号', tags: [] },
    { type: 'arrow', name: '箭头', tags: [] }
  ])
  const selectedStaticTags = ref([]) // 输入框已选标签
  const bufferSettings = ref({}) 
  const checkedTagIds = ref([])
  const categoryCheckedMap = ref({}) 
  
  // 计算每个类别的选中状态
  const categoryCheckedStates = computed(() => {
    const states = {}
    staticTagCategories.value.forEach(cat => {
      const allTagIds = cat.tags.map(tag => String(tag.id))
      const checkedCount = allTagIds.filter(id => checkedTagIds.value.includes(id)).length
      states[cat.type] = {
        checked: checkedCount === cat.tags.length && cat.tags.length > 0,
        indeterminate: checkedCount > 0 && checkedCount < cat.tags.length
      }
    })
    return states
  })
  
  const isAllChecked = (cat) => {
    return cat.tags.length > 0 && cat.tags.every(tag => checkedTagIds.value.includes(String(tag.id)))
  }
  
  const isIndeterminate = (cat) => {
    const checkedCount = cat.tags.filter(tag => checkedTagIds.value.includes(String(tag.id))).length
    return checkedCount > 0 && checkedCount < cat.tags.length
  }
  
  // 根据标签ID查找标签对象
  const findTagById = (tagId) => {
    for (const category of staticTagCategories.value) {
      const tag = category.tags.find(t => String(t.id) === tagId)
      if (tag) return tag
    }
    return null
  }
  
  // 获取静态标签API（请替换为实际API）
  const getStaticTagsApi = async () => {
    // mock
    return {
      data: [
        { id: 1, name: '文字标签', type: 'text' },
        { id: 2, name: '数字标签', type: 'number' },
        { id: 3, name: '符号标签', type: 'symbol' },
        { id: 4, name: '箭头标签', type: 'arrow' },
        { id: 5, name: '文字标签2', type: 'text' },
        { id: 6, name: '数字标签2', type: 'number' },
        { id: 7, name: '符号标签2', type: 'symbol' },
        { id: 8, name: '箭头标签2', type: 'arrow' }
      ]
    }
  }
  
  const fetchStaticTags = async () => {
    const res = await getStaticTagsApi()
    staticTagCategories.value.forEach(cat => {
      cat.tags = res.data.filter(tag => tag.type === cat.type)
      categoryCheckedMap.value[cat.type] = false
    })
  }
  
  // 初始化标签的缓冲区设置
  const initBufferSetting = (tagId) => {
    if (!bufferSettings.value[tagId]) {
      bufferSettings.value[tagId] = {
        enabled: false,
        type: 'distance',
        value: 0
      }
    }
  }
  
  // 更新缓冲区值
  const handleBufferValueChange = (tagId, value) => {
    if (bufferSettings.value[tagId]) {
      bufferSettings.value[tagId].value = value
    }
  }
  
  // 更新缓冲区类型
  const handleBufferTypeChange = (tagId, type) => {
    if (bufferSettings.value[tagId]) {
      bufferSettings.value[tagId].type = type
      // 重置值为0，避免切换类型时值超出范围
      bufferSettings.value[tagId].value = 0
    }
  }
  
  // 标签内容拼接缓冲区
  const tagLabelWithBuffer = (tag) => {
    const buf = bufferSettings.value[String(tag.id)]
    if (!buf || !buf.enabled) return tag.name
    if(buf.type === 'distance'){
      return `${tag.name} (距离/${buf.value}${buf.type === 'distance' ? 'm' : '个'})`
    }else{
       return `${tag.name} (车道/${buf.value}${buf.type === 'distance' ? 'm' : '个'})`
    }
  }
  
  // 处理类别复选框变化
  const handleCategoryChange = (cat, checked) => {
    const ids = cat.tags.map(tag => String(tag.id))
    if (checked) {
      // 选中分类下所有标签
      const newIds = ids.filter(id => !checkedTagIds.value.includes(id))
      checkedTagIds.value = [...checkedTagIds.value, ...newIds]
      // 初始化新选中标签的缓冲区设置和添加到已选标签列表
      newIds.forEach(id => {
        // 初始化缓冲区设置
        if (!bufferSettings.value[id]) {
          bufferSettings.value[id] = {
            type: 'distance',
            value: 0
          }
        }
        // 添加到已选标签列表
        const tag = findTagById(id)
        if (tag && !selectedStaticTags.value.some(t => t.id === tag.id)) {
          selectedStaticTags.value.push(tag)
        }
      })
    } else {
      // 取消选中分类下所有标签
      checkedTagIds.value = checkedTagIds.value.filter(id => !ids.includes(id))
      // 清除取消选中标签的缓冲区设置和已选标签
      ids.forEach(id => {
        delete bufferSettings.value[id]
        selectedStaticTags.value = selectedStaticTags.value.filter(tag => String(tag.id) !== id)
      })
    }
  }
  
  const removeStaticTag = tag => {
    const tagId = String(tag.id)
    checkedTagIds.value = checkedTagIds.value.filter(id => id !== tagId)
    delete bufferSettings.value[tagId]
    selectedStaticTags.value = selectedStaticTags.value.filter(t => String(t.id) !== tagId)
  }
  
  const resetStaticTags = () => {
    selectedStaticTags.value = []
    checkedTagIds.value = []
    bufferSettings.value = {}
  }
  
  // 监听选中的标签变化
  watch(checkedTagIds, (newVal, oldVal) => {
    if (!oldVal) return // 初始化时跳过
    newVal.forEach(id =>{
      if (!bufferSettings.value[id]) {
          bufferSettings.value[id] = {
            enabled:false,
            type: 'distance',
            value: 0
          }
        }
    })
  
    // 找出新增的标签
    const addedTags = newVal.filter(id => !oldVal.includes(id))
    // 找出移除的标签
    const removedTags = oldVal.filter(id => !newVal.includes(id))
    
    // 为新增的标签初始化缓冲区设置
    addedTags.forEach(tagId => {
      initBufferSetting(tagId)
      // 添加到已选标签列表
      const tag = findTagById(tagId)
      if (tag && !selectedStaticTags.value.some(t => t.id === tag.id)) {
        selectedStaticTags.value.push(tag)
      }
    })
    
    // 移除不再选中的标签的缓冲区设置和已选标签
    removedTags.forEach(tagId => {
      delete bufferSettings.value[tagId]
      selectedStaticTags.value = selectedStaticTags.value.filter(tag => String(tag.id) !== tagId)
    })
  }, { deep: true })
  
  const isTagChecked = tag => checkedTagIds.value.includes(String(tag.id))
  
  const toggleCategory = (cat, checked) => {
    const ids = cat.tags.map(tag => String(tag.id))
    if (checked) {
      // 选中分类下所有标签
      const newIds = ids.filter(id => !checkedTagIds.value.includes(id))
      checkedTagIds.value = [...checkedTagIds.value, ...newIds]
      // 初始化新选中标签的缓冲区设置和添加到已选标签列表
      newIds.forEach(id => {
        // 初始化缓冲区设置
        if (!bufferSettings.value[id]) {
          bufferSettings.value[id] = {
            enabled:false,
            type: 'distance',
            value: 0
          }
        }
        // 添加到已选标签列表
        const tag = findTagById(id)
        if (tag && !selectedStaticTags.value.some(t => t.id === tag.id)) {
          selectedStaticTags.value.push(tag)
        }
      })
    } else {
      // 取消选中分类下所有标签
      checkedTagIds.value = checkedTagIds.value.filter(id => !ids.includes(id))
      // 清除取消选中标签的缓冲区设置和已选标签
      ids.forEach(id => {
        delete bufferSettings.value[id]
        selectedStaticTags.value = selectedStaticTags.value.filter(tag => String(tag.id) !== id)
      })
    }
  }
  
  // 步骤三相关
  const selfCheckedTagIds = ref([])
  const selfBufferSettings = ref({})
  const selfPatchSettings = ref({})
  
  // 获取所有可选标签
  const allTags = computed(() => {
    const tags = []
    staticTagCategories.value.forEach(cat => {
      tags.push(...cat.tags)
    })
    return tags
  })
  
  // 已选标签列表
  const selfSelectedTags = computed(() => {
    return allTags.value.filter(tag => selfCheckedTagIds.value.includes(String(tag.id)))
  })
  
  // 生成标签显示文本
  const selfTagLabelWithSettings = (tag) => {
    const tagId = String(tag.id)
    const parts = [tag.name]
    
    if (selfBufferSettings.value[tagId]?.enabled) {
      const buffer = selfBufferSettings.value[tagId]
      const unit = buffer.type === 'distance' ? 'm' : '个'
      parts.push(`匹配缓冲区(${buffer.type === 'distance' ? '距离' : '车道'}/${buffer.value}${unit})`)
    }
    
    if (selfPatchSettings.value[tagId]?.enabled) {
      const patch = selfPatchSettings.value[tagId]
      const unit = patch.type === 'distance' ? 'm' : 's'
      parts.push(`数据补全(${patch.type === 'distance' ? '距离' : '时间'}/${patch.value}${unit})`)
    }
    
    return parts.join(' ')
  }
  
  // 移除标签
  const removeSelfTag = (tag) => {
    const tagId = String(tag.id)
    selfCheckedTagIds.value = selfCheckedTagIds.value.filter(id => id !== tagId)
  }
  
  // 重置所有设置
  const resetSelfTags = () => {
    selfCheckedTagIds.value = []
    selfBufferSettings.value = {}
    selfPatchSettings.value = {}
  }
  
  // 检查标签是否被选中
  const isSelfTagChecked = (tag) => selfCheckedTagIds.value.includes(String(tag.id))
  
  // 监听标签选中状态变化
  watch(selfCheckedTagIds, (newVal, oldVal) => {
    // 初始化新选中标签的设置
    newVal.forEach(id => {
      if (!selfBufferSettings.value[id]) {
        selfBufferSettings.value[id] = {
          enabled: false,
          type: 'distance',
          value: 0
        }
      }
      if (!selfPatchSettings.value[id]) {
        selfPatchSettings.value[id] = {
          enabled: false,
          type: 'distance',
          value: 0
        }
      }
    })
    
    // 清理未选中标签的设置
    Object.keys(selfBufferSettings.value).forEach(id => {
      if (!newVal.includes(id)) {
        delete selfBufferSettings.value[id]
      }
    })
    Object.keys(selfPatchSettings.value).forEach(id => {
      if (!newVal.includes(id)) {
        delete selfPatchSettings.value[id]
      }
    })
  }, { deep: true })
  
  // 全选相关
  const selfAllChecked = ref(false)
  const selfIndeterminate = ref(false)
  
  const handleSelfCheckAllChange = (val) => {
    selfCheckedTagIds.value = val ? allTags.value.map(tag => String(tag.id)) : []
    selfIndeterminate.value = false
  }
  
  // 监听选中标签变化，更新全选状态
  watch(selfCheckedTagIds, (val) => {
    const checkedCount = val.length
    const total = allTags.value.length
    selfAllChecked.value = checkedCount === total
    selfIndeterminate.value = checkedCount > 0 && checkedCount < total
  }, { deep: true })
  
  // 跳转到指定步骤
  const goToStep = (step) => {
    currentStep.value = step
  }
  
  // 任务成功弹窗状态
  const successDialogVisible = ref(false)
  
  // mock API
  const startDataMiningTask = async () => {
    const param={
      dataList:[{
        acquisitionType: "driving",
        vinList:selectedVariables.value,
        timeRangeList: selectedDates.value,
        hourRangeList: selectedTimeRanges.value,
        // "tagList": null,
        // "cantonCode": 
  
      }]
    }
    // 模拟API延迟
    return new Promise((resolve) => setTimeout(resolve, 1000))
  }
  
  // 发起任务按钮
  const submitTask = async () => {
    try {
      await startDataMiningTask()
      successDialogVisible.value = true
    } catch (e) {
      showToast('任务发起失败', 'error')
    }
  }
  
  const handleSuccessDialog = () => {
    successDialogVisible.value = false
    
    // 重置所有步骤的数据
    selectedVariables.value = []
    selectedDates.value = []
    selectedTimeRanges.value = []
    selectedTags.value = []
    selectedRegionsName.value = []
    
    selectedStaticTags.value = []
    checkedTagIds.value = []
    bufferSettings.value = {}
    
    selfCheckedTagIds.value = []
    selfBufferSettings.value = {}
    selfPatchSettings.value = {}
    selfAllChecked.value = false
    selfIndeterminate.value = false
    
    currentStep.value = 0
  }
  
  onMounted(() => {
    listCantonTree()
    fetchStaticTags()
    listVehicle()
  })
  </script>
  
  <style lang="scss" scoped>
  .data-mining-initiated {
    overflow: hidden;
    padding: 20px;
    height: 89vh;
    display: flex;
    flex-direction: column;
    border: 1px #e4e7ed solid;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }
  .info-box {
    width: 100%;
    margin-top: 10px;
    margin-bottom: 10px;
    min-height: 80px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 8px;
    background: #fafbfc;
  }
  .steps {
    margin-bottom: 30px;
  }
  .fixed-btn-group{
    padding:10px 25px;
    text-align: right;
  }
  
  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    min-height: calc(100vh - 200px);
  }
  
  .content-scroll {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    padding-bottom: 10px; /* 为底部按钮留出空间 */
  }
  
  .button-group {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background-color: #fff;
    text-align: center;
    border-top: 1px solid #ebeef5;
    z-index: 1;
  }
  
  .button-group .el-button {
    margin: 0 10px;
  }
  
  .static-tag-cards {
    display: flex;
    gap: 20px;
    margin-top: 24px;
  }
  .static-tag-card {
    flex: 1;
    min-width: 220px;
  }
  .tag-row {
    margin-bottom: 8px;
  }
  .divider-text {
    font-weight: 400;
  }
  .tag-buffer-settings {
  
    margin-top: 8px;
    gap: 8px;
    .tag-box{
      display: flex;
      align-items: center;
    }
    
    .el-radio-group {
      margin-right: 8px;
    }
    
    .el-input-number {
      width: 100px;
    }
    
    .unit {
      margin-left:10px;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .static-tags {
    margin-top: 16px;
    
    .tag-category {
      margin-bottom: 16px;
      
      .category-title {
        font-weight: bold;
        margin-bottom: 8px;
      }
      
      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        
        .tag-item {
          display: flex;
          flex-direction: column;
          margin-bottom: 12px;
          
          .tag-checkbox {
            display: flex;
            align-items: center;
          }
          
          .tag-buffer-settings {
            margin-left: 24px;
            margin-top: 8px;
          //   display: flex;
          //   align-items: center;
            gap: 8px;
            
            .el-radio-group {
              margin-right: 8px;
            }
            
            .number-input-wrapper {
              display: inline-block;
              
              :deep(.el-input-number) {
                width: 100px;
              }
            }
            
            .unit {
              color: #606266;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  
  .tag-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
    
    .tag-checkbox {
      display: flex;
      align-items: center;
    }
    
    .tag-buffer-settings {
      margin-left: 24px;
      margin-top: 8px;
      // display: flex;
      // align-items: center;
      gap: 8px;
      
      .el-radio-group {
        margin-right: 8px;
      }
      
      .number-input-wrapper {
        display: inline-block;
        
        :deep(.el-input-number) {
          width: 100px;
        }
      }
      
      .unit {
        color: #606266;
        font-size: 14px;
      }
    }
  }
  
  :deep(.el-checkbox-group) {
    display: flex;
    flex-direction: column;
  }
  
  :deep(.el-input-number.el-input-number--small) {
    width: 100px !important;
  }
  
  .self-form {
    margin-bottom: 16px;
    
    .info-box {
      padding: 8px 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      min-height: 40px;
      display: flex;
      align-items: center;
    }
  }
  
  .self-tag-list-card {
    margin-top: 16px;
  }
  
  .self-tag-list-header {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 40px;
  }
  
  .self-tag-list-grid {
    display: flex;
    flex-wrap: wrap;
  }
  
  .self-tag-item {
    flex: 0 0 33.3333%;
    box-sizing: border-box;
    padding: 12px 16px 12px 0;
    border-bottom: 1px solid #ebeef5;
    min-width: 0;
  }
  
  .tag-settings {
    margin-left: 24px;
    margin-top: 8px;
    .setting-item {
      margin-bottom: 8px;
    }
    .setting-content {
      margin-left: 24px;
      margin-top: 8px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      .el-radio-group {
        margin-right: 8px;
      }
      .unit {
        margin-left: 8px;
        color: #606266;
      }
    }
  }
  
  .step4-card {
    margin-bottom: 24px;
    .card-header-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }
    .card-title {
      font-size: 18px;
      font-weight: 600;
    }
    .card-section {
      margin-bottom: 16px;
      .section-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 4px;
      }
      .el-input {
        width: 100%;
      }
    }
  }
  
  .step4-btns {
    margin-top: 32px;
    text-align: center;
  }
  
  
  .card-area {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 0; // 关键，防止flex溢出
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10), 0 1.5px 6px 0 rgba(0,0,0,0.06);
    background: #fff;
  
    .card-scroll {
      flex: 1;
      height: 100%;
      
      :deep(.el-scrollbar__wrap) {
        overflow-x: hidden;
      }
      
      :deep(.el-scrollbar__bar) {
        z-index: 10;
      }
      
      :deep(.el-scrollbar__view) {
        padding: 24px;
        padding-bottom: 80px; // 为底部按钮留出空间
      }
    }
  }
  
  .fixed-btn-group {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 25px;
    text-align: right;
    background: #fff;
    border-top: 1px solid #ebeef5;
    z-index: 11;
  }
  </style>
  