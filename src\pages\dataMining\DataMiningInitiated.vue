<template>
  <div class="ltw-page-container data-mining-initiated">
    <!-- 头部区域 -->
    <div class="header-section">
      <div class="header-left">
        <div @click="handleBack" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
        </div>
        <span class="page-title">数据挖掘任务发起</span>
      </div>
    </div>

    <!-- 固定顶部进度条 -->
    <div class="fixed-steps">
      <el-steps :active="currentStep" class="steps">
        <el-step title="步骤一" description="数据筛选"></el-step>
        <el-step title="步骤二" description="静态筛选"></el-step>
        <el-step title="步骤三" description="自车筛选"></el-step>
        <el-step title="步骤四" description="发起任务"></el-step>
      </el-steps>
    </div>
    <!-- 自适应内容区 -->
    <div class="card-area">
      <el-scrollbar class="card-scroll">
        <div v-if="currentStep === 0">
          <h3>数据筛选</h3>
          <el-form label-width="120px" class="step1-form" label-position="left">
            <!-- 变量（多选） -->
            <el-form-item label="车辆（支持多选）" required>
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                <div style="flex: 1"></div>
                <div>
                  <el-button type="primary" size="small" @click="addVariable">新增</el-button>
                  <el-button type="danger" size="small" @click="resetVariables">重置</el-button>
                </div>
              </div>
            </el-form-item>
            <div class="info-box">
              <el-tag
                v-for="(item, idx) in selectedVariables"
                :key="item"
                closable
                @close="removeVariable(item)"
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ item }}
              </el-tag>
              <span v-if="selectedVariables.length === 0" style="color: #bbb">请选择车辆</span>
            </div>
            <!-- 时间范围（多选，必填） -->
            <el-form-item label="时间段（支持多选）" required label-width="130px">
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                <div style="flex: 1"></div>
                <div>
                  <el-button type="primary" size="small" @click="addDate">新增</el-button>
                  <el-button type="danger" size="small" @click="resetDates">重置</el-button>
                </div>
              </div>
            </el-form-item>
            <div class="info-box">
              <el-tag
                v-for="(item, idx) in selectedDates"
                :key="item"
                closable
                @close="removeDate(idx)"
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ item[0] }} - {{ item[1] }}
              </el-tag>
              <span v-if="selectedDates.length === 0" style="color: #bbb">请选择时间段</span>
            </div>
            <!-- 每日时间段（多选） -->
            <el-form-item label="每日时间范围（支持多选）" label-width="150px">
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                <div style="flex: 1"></div>
                <div>
                  <el-button type="primary" size="small" @click="addTimeRange">新增</el-button>
                  <el-button type="danger" size="small" @click="resetTimeRanges">重置</el-button>
                </div>
              </div>
            </el-form-item>
            <div class="info-box">
              <el-tag
                v-for="(item, idx) in selectedTimeRanges"
                :key="item"
                closable
                @close="removeTimeRange(idx)"
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ item[0] }} - {{ item[1] }}
              </el-tag>
              <span v-if="selectedTimeRanges.length === 0" style="color: #bbb">请选择每日时间范围</span>
            </div>
            <!-- 标签规则 -->
            <el-form-item label="标签规则（支持多选）" label-width="130px">
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                <div style="flex: 1"></div>
                <div>
                  <el-button type="primary" size="small" @click="addTag">新增</el-button>
                  <el-button type="danger" size="small" @click="resetTags">重置</el-button>
                </div>
              </div>
            </el-form-item>
            <div class="info-box">
              <el-tag
                v-for="(item, idx) in selectedTags"
                :key="item"
                closable
                @close="removeTag(idx)"
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ item }}
              </el-tag>
              <span v-if="selectedTags.length === 0" style="color: #bbb">请选择标签</span>
            </div>
            <!-- 过滤区域 -->
            <el-form-item label="选择区域（支持多选）" label-width="140px">
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                <div style="flex: 1"></div>
                <div>
                  <el-button type="primary" size="small" @click="addRegion">新增</el-button>
                  <el-button type="primary" size="small" @click="manualRegionSelect">手动框选</el-button>
                  <el-button type="danger" size="small" @click="resetRegions">重置</el-button>
                </div>
              </div>
            </el-form-item>
            <div class="info-box">
              <el-tag
                v-for="(item, idx) in selectedRegionsName"
                :key="item"
                closable
                @close="removeRegion(idx)"
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ item }}
              </el-tag>
              <span v-if="selectedRegionsName.length === 0" style="color: #bbb">请选择区域</span>
            </div>
          </el-form>
        </div>
        <div v-if="currentStep === 1">
          <h3>静态筛选</h3>
          <!-- 静态标签输入框区域 -->
          <el-form label-width="130px" class="static-form" label-position="left">
            <el-form-item label="静态标签（支持多选）">
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                <el-button type="primary" size="small" @click="openStaticTagSelector">选择标签</el-button>
                <el-button type="danger" size="small" @click="resetStaticTags">重置</el-button>
              </div>
            </el-form-item>
          </el-form>
          <div class="info-box">
            <div style="flex: 1; min-height: 40px">
              <el-tag
                v-for="tag in selectedStaticTags"
                :key="tag.id"
                closable
                @close="removeStaticTag(tag)"
                style="margin-right: 8px; margin-bottom: 4px"
                type="success"
              >
                {{ tagLabelWithBuffer(tag) }}
              </el-tag>
              <span v-if="selectedStaticTags.length === 0" style="color: #bbb">请选择标签</span>
            </div>
          </div>

          <!-- 最外层根节点卡片（支持多个） -->
          <div v-for="rootInfo in staticRootTagInfoList" :key="rootInfo.id" class="root-section">
            <el-card class="root-tag-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <el-checkbox
                    :model-value="getRootCheckedState(rootInfo.id).checked"
                    :indeterminate="getRootCheckedState(rootInfo.id).indeterminate"
                    @change="(val) => handleRootChange(rootInfo.id, val)"
                  >
                    {{ rootInfo.name }}
                  </el-checkbox>
                </div>
              </template>

              <!-- 标签分类卡片 -->
              <div class="static-tag-cards">
                <el-card v-for="cat in getCategoriesByRootId(rootInfo.id)" :key="cat.type" class="tag-card" shadow="hover">
                  <template #header>
                    <div class="card-header">
                      <el-checkbox
                        :model-value="categoryCheckedStates[cat.type]?.checked"
                        :indeterminate="categoryCheckedStates[cat.type]?.indeterminate"
                        @change="(val) => handleCategoryChange(cat, val)"
                      >
                        {{ cat.name }}
                      </el-checkbox>
                    </div>
                  </template>

                  <div v-for="tag in cat.tags" :key="tag.id" class="tag-item">
                    <div class="tag-checkbox">
                      <el-checkbox v-model="checkedTagIds" :label="String(tag.id)">
                        {{ tag.name }}
                      </el-checkbox>
                    </div>
                    <div v-if="isTagChecked(tag)" class="tag-buffer-settings">
                      <div>
                        <el-checkbox
                          v-model="bufferSettings[String(tag.id)].enabled"
                        >匹配缓冲区</el-checkbox>
                      </div>
                      <div class="tag-box" v-if="bufferSettings[String(tag.id)]?.enabled">
                        <el-radio-group
                          v-model="bufferSettings[String(tag.id)].type"
                          size="small"
                          @change="(val) => handleBufferTypeChange(String(tag.id), val)"
                        >
                          <el-radio-button label="distance">距离</el-radio-button>
                          <el-radio-button label="lane">车道</el-radio-button>
                        </el-radio-group>
                        <div class="number-input-wrapper">
                          <el-input-number
                            v-model="bufferSettings[String(tag.id)].value"
                            :min="0"
                            :max="bufferSettings[String(tag.id)].type === 'distance' ? 1000 : 10"
                            :step="1"
                            :precision="0"
                            size="small"
                            @change="(val) => handleBufferValueChange(String(tag.id), val)"
                            :class="{ 'input-error': isBufferValueInvalid(String(tag.id)) }"
                            placeholder="请输入值"
                          ></el-input-number>
                          <span class="unit">{{ bufferSettings[String(tag.id)].type === 'distance' ? 'm' : '个' }}</span>
                          <div v-if="isBufferValueInvalid(String(tag.id))" class="error-tip">请输入有效值</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
            </el-card>
          </div>
          <el-empty v-show="staticRootTagInfoList.length === 0" description="暂无标签" />
        </div>
        <div v-if="currentStep === 2">
          <h3>自车筛选</h3>
          <!-- 上方标签输入框区域 -->
          <el-form class="self-form" label-position="left">
            <el-form-item label="自车标签（支持多选）">
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%">
                <el-button type="primary" size="small" @click="openSelfTagSelector">选择标签</el-button>
                <el-button type="danger" size="small" @click="resetSelfTags">重置</el-button>
              </div>
            </el-form-item>
            <div class="info-box">
              <div style="flex: 1; min-height: 40px">
                <el-tag
                  v-for="tag in getSelfSelectedTags()"
                  :key="tag.id"
                  closable
                  @close="removeSelfTag(tag)"
                  style="margin-right: 8px; margin-bottom: 4px"
                  type="primary"
                >
                  {{ selfTagLabelWithSettings(tag) }}
                </el-tag>
                <span v-if="getSelfSelectedTags().length === 0" style="color: #bbb">请选择标签</span>
              </div>
            </div>
          </el-form>

          <!-- 最外层根节点卡片（支持多个） -->
          <div v-for="rootInfo in selfRootTagInfoList" :key="rootInfo.id" class="root-section">
            <el-card class="root-tag-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <el-checkbox
                    :model-value="getSelfRootCheckedState(rootInfo.id).checked"
                    :indeterminate="getSelfRootCheckedState(rootInfo.id).indeterminate"
                    @change="(val) => handleSelfRootChange(rootInfo.id, val)"
                  >
                    {{ rootInfo.name }}
                  </el-checkbox>
                </div>
              </template>

              <!-- 标签选择区域 -->
              <div class="self-tag-cards">
                <!-- 分类卡片 -->
                <el-card v-for="cat in getSelfCategoriesByRootId(rootInfo.id)" :key="cat.type" class="tag-card" shadow="hover">
                  <template #header>
                    <div class="card-header">
                      <el-checkbox
                        :model-value="selfCategoryCheckedStates[cat.type]?.checked"
                        :indeterminate="selfCategoryCheckedStates[cat.type]?.indeterminate"
                        @change="(val) => handleSelfCategoryChange(cat, val)"
                      >
                        {{ cat.name }}
                      </el-checkbox>
                    </div>
                  </template>

                  <div v-for="tag in cat.tags" :key="tag.id" class="tag-item">
                    <div class="tag-checkbox">
                      <el-checkbox v-model="selfCheckedTagIds" :label="String(tag.id)">
                        {{ tag.name }}
                      </el-checkbox>
                    </div>
                    <div v-if="isSelfTagChecked(tag)" class="tag-buffer-settings">
                      <!-- 匹配缓冲区设置 -->
                      <div>
                        <el-checkbox
                          v-model="selfBufferSettings[String(tag.id)].enabled"
                        >匹配缓冲区</el-checkbox>
                      </div>
                      <div class="tag-box" v-if="selfBufferSettings[String(tag.id)]?.enabled">
                        <el-radio-group
                          v-model="selfBufferSettings[String(tag.id)].type"
                          size="small"
                        >
                          <el-radio-button label="distance">距离</el-radio-button>
                          <el-radio-button label="lane">车道</el-radio-button>
                        </el-radio-group>
                        <div class="number-input-wrapper">
                          <el-input-number
                            v-model="selfBufferSettings[String(tag.id)].value"
                            :min="0"
                            :max="selfBufferSettings[String(tag.id)].type === 'distance' ? 1000 : 10"
                            :step="1"
                            :precision="0"
                            size="small"
                            :class="{ 'input-error': isSelfBufferValueInvalid(String(tag.id)) }"
                            placeholder="请输入值"
                          ></el-input-number>
                          <span class="unit">{{ selfBufferSettings[String(tag.id)].type === 'distance' ? 'm' : '个' }}</span>
                          <div v-if="isSelfBufferValueInvalid(String(tag.id))" class="error-tip">请输入有效值</div>
                        </div>
                      </div>

                      <!-- 数据补全设置 -->
                      <div style="margin-top: 8px;">
                        <el-checkbox
                          v-model="selfPatchSettings[String(tag.id)].enabled"
                        >数据补全</el-checkbox>
                      </div>
                      <div class="tag-box" v-if="selfPatchSettings[String(tag.id)]?.enabled">
                        <el-radio-group
                          v-model="selfPatchSettings[String(tag.id)].type"
                          size="small"
                        >
                          <el-radio-button label="distance">距离</el-radio-button>
                          <el-radio-button label="time">时间</el-radio-button>
                        </el-radio-group>
                        <div class="number-input-wrapper">
                          <el-input-number
                            v-model="selfPatchSettings[String(tag.id)].value"
                            :min="0"
                            :max="1000"
                            :step="1"
                            :precision="0"
                            size="small"
                            :class="{ 'input-error': isSelfPatchValueInvalid(String(tag.id)) }"
                            placeholder="请输入值"
                          ></el-input-number>
                          <span class="unit">{{ selfPatchSettings[String(tag.id)].type === 'distance' ? 'm' : 's' }}</span>
                          <div v-if="isSelfPatchValueInvalid(String(tag.id))" class="error-tip">请输入有效值</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
            </el-card>
          </div>
          <el-empty v-show="selfRootTagInfoList.length === 0" description="暂无标签" />
        </div>
        <div v-if="currentStep === 3">
          <h3>确认任务内容</h3>
          <el-card class="step4-card" shadow="never">
            <div class="card-header-flex">
              <span class="card-title">数据筛选</span>
              <el-button type="primary" size="small" @click="goToStep(0)">编辑</el-button>
            </div>
            <div class="card-section">
              <div class="section-label">车辆（支持多选）</div>
              <div class="info-box">
              <el-tag
                v-for="(item, idx) in selectedVariables"
                :key="item"
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ item }}
              </el-tag>
            </div>
            </div>
            <div class="card-section">
              <div class="section-label">时间段（支持多选）</div>
              <div class="info-box">
              <el-tag
                v-for="(item, idx) in selectedDates"
                :key="item"
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ item[0] }} - {{ item[1] }}
              </el-tag>

            </div>
            </div>
            <div class="card-section">
              <div class="section-label">标签规则</div>
              <div class="info-box">
              <el-tag
                v-for="(item, idx) in selectedTags"
                :key="item"
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ item }}
              </el-tag>
            </div>
            </div>
            <div class="card-section">
              <div class="section-label">选择区域</div>
              <div class="info-box">
              <el-tag
                v-for="(item, idx) in selectedRegionsName"
                :key="item"
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ item }}
              </el-tag>
            </div>
            </div>
          </el-card>
          <el-card class="step4-card" shadow="never">
            <div class="card-header-flex">
              <span class="card-title">静态筛选</span>
              <el-button type="primary" size="small" @click="goToStep(1)">编辑</el-button>
            </div>
            <div class="card-section">
              <div class="section-label">静态标签</div>
              <div class="info-box">
              <div style="flex: 1; min-height: 40px">
              <el-tag
                v-for="tag in selectedStaticTags"
                :key="tag.id"
                style="margin-right: 8px; margin-bottom: 4px"
              >
                {{ tagLabelWithBuffer(tag) }}
              </el-tag>
            </div>
          </div>
            </div>
          </el-card>
          <el-card class="step4-card" shadow="never">
            <div class="card-header-flex">
              <span class="card-title">自车筛选</span>
              <el-button type="primary"  size="small" @click="goToStep(2)">编辑</el-button>
            </div>
            <div class="card-section">
              <div class="section-label">自车标签</div>
              <div class="info-box">
              <div style="flex: 1; min-height: 40px">
                <el-tag
                  v-for="tag in selfSelectedTags"
                  :key="tag.id"
                  style="margin-right: 8px; margin-bottom: 4px"
                >
                  {{ selfTagLabelWithSettings(tag) }}
                </el-tag>
              </div>
            </div>
            </div>
          </el-card>

          <!-- <div class="button-group step4-btns">
             <el-button @click="prevStep">上一步</el-button>
             <el-button type="primary" @click="submitTask">发起任务</el-button>
           </div> -->
        </div>
      </el-scrollbar>
      <!-- 固定右下角按钮 -->
      <div class="fixed-btn-group">
        <el-button @click="prevStep" :disabled="currentStep === 0">上一步</el-button>
        <el-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-else type="primary" @click="submitTask">发起任务</el-button>
      </div>
    </div>

    <!-- 任务成功弹窗 -->
    <el-dialog v-model="successDialogVisible" width="800px" :show-close="true" center>
      <div style="font-size: 20px; font-weight: 500; margin-bottom: 32px;">数据挖掘任务已成功发起！</div>
      <el-steps :active="4" finish-status="success" align-center>
        <el-step title="Step 1" description="数据筛选" />
        <el-step title="Step 2" description="静态筛选" />
        <el-step title="Step 3" description="自车筛选" />
        <el-step title="Step 4" description="发起任务" />
      </el-steps>
      <div style="text-align: right; margin-top: 32px;">
        <el-button @click="handleSuccessDialog" type="primary">确定</el-button>
      </div>
    </el-dialog>

    <!-- 弹窗统一入口 -->
    <div v-if="dialogVisible" class="dialog-container">
      <el-dialog v-model="dialogVisible" :title="dialogTitleMap[dialogType]" :width="dialogWidthMap[dialogType]">
        <template v-if="dialogType === 'vehicle'">
          <bs-vehicle-selection
              :data="vehicleList"
              :auto-load="false"
              modelCode="vin"
              v-model="tempSelectedVariables"
              ref="vehicleRef"
              clearable
              multiple
              filterable
              size="small"
            ></bs-vehicle-selection>
          <!-- <el-select
            v-model="tempSelectedVariables"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择车辆"
            style="width: 100%"
          >
            <el-option v-for="item in variableOptions" :key="item" :label="item" :value="item" />
          </el-select> -->
        </template>
        <template v-else-if="dialogType === 'tag'">


        </template>
        <template v-else-if="dialogType === 'region'">
          <el-cascader
            v-model="tempSelectedRegions"
            :placeholder="$t('请选择行政区')"
            :options="cantonTreeList"
            :props="cantonListProps"
            clearable
            filterable
          />
        </template>
        <template v-else-if="dialogType === 'date'">
          <el-date-picker
            v-model="tempDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </template>
        <template v-else-if="dialogType === 'time'">
          <el-time-picker
            v-model="tempTimeRange"
            is-range
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="HH"
            value-format="HH"
            style="width: 100%"
          />
        </template>
        <template v-else-if="dialogType === 'manualSelect'">
          <TMapEditor v-if="dialogType === 'manualSelect'" ref="TMapEditorRef" @updateDistrictCode="handleUpdateDistrictCode"/>
        </template>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleDialogConfirm">确定</el-button>
        </template>
      </el-dialog>
    </div>

    <!-- 静态标签选择抽屉 -->
    <bs-tag-group-drawer
      :drawerVisible="staticTagDrawerVisible"
      :rowTagList="staticRowTagList"
      @drawer-click="handleStaticTagDrawerClick"
    />

    <!-- 自车标签选择抽屉 -->
    <bs-tag-group-drawer
      :drawerVisible="selfTagDrawerVisible"
      :rowTagList="selfRowTagList"
      :isPoi="false"
      @drawer-click="handleSelfTagDrawerClick"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { showToast, showConfirmToast } from '@/plugins/util'
import { getSysCantonTree } from '@/apis/system/sys-canton'
import TMapEditor from '@/components/geography/TMapEditor.vue'
import BsVehicleSelection from '@/components/basic/BsVehicleSelection.vue'
import { listBsVehicle } from '@/apis/basic/bs-vehicle'
import { getTagTreeData } from '@/apis/dataMining/data_mining_define'
import { ArrowLeft } from '@element-plus/icons-vue'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
// Props 定义
const props = defineProps({
  selectedDataset: {
    type: Object,
    default: null
  }
})

// Emit 定义
const emit = defineEmits(['back'])

const currentStep = ref(0)
const TMapEditorRef = ref(null)
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 验证当前步骤
const validateCurrentStep = () => {
  switch (currentStep.value) {
    case 0:
      return validateStep1()
    case 1:
      return validateStep2()
    case 2:
      return validateStep3()
    default:
      return true
  }
}

// 第一步验证：数据筛选必选项
const validateStep1 = () => {
  const errors = []

  if (!selectedVariables.value || selectedVariables.value.length === 0) {
    showToast('请选择至少一个车辆', 'warning')
    return false
  }

  if (!selectedDates.value || selectedDates.value.length === 0) {
    showToast('请选择至少一个时间段', 'warning')
    return false
  }


  return true
}

// 第二步验证：静态筛选缓冲区设置
const validateStep2 = () => {

  // 检查静态标签的缓冲区设置
  Object.keys(bufferSettings.value).forEach(tagId => {
    const buffer = bufferSettings.value[tagId]
    if (buffer.enabled && (buffer.value === null || buffer.value === undefined || buffer.value === '')) {
      showToast('请检查静态标签的匹配缓冲区设置', 'warning')
      return false
    }
  })

  return true
}

// 第三步验证：自车筛选缓冲区和数据补全设置
const validateStep3 = () => {

  // 检查自车标签的缓冲区设置
  Object.keys(selfBufferSettings.value).forEach(tagId => {
    const buffer = selfBufferSettings.value[tagId]
    if (buffer.enabled && (buffer.value === null || buffer.value === undefined || buffer.value === '')) {
      showToast('请检查自车标签的匹配缓冲区设置', 'warning')
      return false
    }
  })

  // 检查自车标签的数据补全设置
  Object.keys(selfPatchSettings.value).forEach(tagId => {
    const patch = selfPatchSettings.value[tagId]
    if (patch.enabled && (patch.value === null || patch.value === undefined || patch.value === '')) {
      showToast('请检查自车标签的数据补全设置', 'warning')
      return false
    }
  })

  return true
}

const nextStep = () => {
  if (currentStep.value < 3) {
    // 验证当前步骤
    if (validateCurrentStep()) {
      currentStep.value++
    }
  }
}

const vehicleList = ref([])
const selectedVariables = ref([])
const tempSelectedVariables = ref([])
const dialogVisible = ref(false)
const dialogType = ref('') // vehicle | tag | region | date | time| manualSelect
const dialogTitleMap = {
  vehicle: '选择车辆',
  tag: '选择标签',
  region: '选择区域',
  date: '选择时间段',
  time: '选择每日时间范围',
  manualSelect: '手动选择区域范围'
}
const dialogWidthMap = {
  vehicle: '400px',
  tag: '400px',
  region: '600px',
  date: '400px',
  time: '400px',
  manualSelect: '800px'
}

const cantonListProps = {
  value: 'locationArr',
  label: 'name',
  checkStrictly: true,
  emitPath: false
}

const cantonTreeList = ref([])
const selectedCatTags = ref([])
const checkAll = ref([])

const listVehicle = () => {
      return listBsVehicle().then(res => {
        vehicleList.value = res.data
      })
    }
const extractLocationArr = item => {
  if (item.latitude !== undefined && item.longitude !== undefined) {
    return [[item.latitude, item.longitude], item.nameLink, item.code]
  }
  return []
}

const addLocationField = data => {
  return data.map(item => {
    item.locationArr = extractLocationArr(item)
    if (item.children?.length) {
      item.children = addLocationField(item.children)
    }
    return item
  })
}

const listCantonTree = () => {
  if (cantonTreeList.value?.length) return
  getSysCantonTree().then(res => {
    cantonTreeList.value = addLocationField(res.data)
  })
}

const addVariable = () => openDialog('vehicle')
const resetVariables = () => {
  selectedVariables.value = []
}
const resetTags = () => {
  selectedTags.value = []
}
const removeVariable = item => {
  selectedVariables.value = selectedVariables.value.filter(v => v !== item)
}
const handleDialogConfirm = () => {
  if (dialogType.value === 'vehicle') {
    tempSelectedVariables.value.forEach(item => {
      if (!selectedVariables.value.includes(item)) {
        selectedVariables.value.push(item)
      } else {
        showToast('请勿重复添加标签', 'warning')
      }
    })
    tempSelectedVariables.value = []
  } else if (dialogType.value === 'tag') {
    tempSelectedTags.value.forEach(item => {
      if (!selectedTags.value.includes(item)) {
        selectedTags.value.push(item)
      } else {
        showToast('请勿重复添加标签', 'warning')
      }
    })
    tempSelectedTags.value = []
  } else if (dialogType.value === 'region') {
    const label = tempSelectedRegions.value[1]
    const code = tempSelectedRegions.value[2]
    if (!selectedRegionContonList.value.includes(code)) {
      selectedRegionsName.value.push(label)
      selectedRegionContonList.value.push(code)
    } else {
      showToast('请勿重复添加区域', 'warning')
    }
    tempSelectedRegions.value = ''
  } else if (dialogType.value === 'date') {
    if (tempDateRange.value && tempDateRange.value.length === 2) {
      selectedDates.value.push([...tempDateRange.value])
      tempDateRange.value = []
    }
  } else if (dialogType.value === 'time') {
    if (tempTimeRange.value && tempTimeRange.value.length === 2) {
      selectedTimeRanges.value.push([...tempTimeRange.value])
      tempTimeRange.value = []
    }
  }else if( dialogType.value === 'manualSelect'){
     if (tempSelectedRegionCode.value && tempSelectedRegionName.value) {
      if (!selectedRegionContonList.value.includes(tempSelectedRegionCode.value)) {
        selectedRegionsName.value.push(tempSelectedRegionName.value)
        selectedRegionContonList.value.push(tempSelectedRegionCode.value)
      } else {
        showToast('请勿重复添加区域', 'warning')
      }
      // 清空临时数据
      tempSelectedRegionCode.value = ''
      tempSelectedRegionName.value = ''
    } else {
      showToast('未选择有效区域', 'warning')
    }
   TMapEditorRef.value.destroyMap()
  }
  dialogVisible.value = false
}

const selectedDates = ref([])
const tempDateRange = ref([])
const addDate = () => openDialog('date')
const removeDate = idx => {
  selectedDates.value.splice(idx, 1)
}
const resetDates = () => {
  selectedDates.value = []
}

const selectedTimeRanges = ref([])
const tempTimeRange = ref([])
const addTimeRange = () => openDialog('time')
const removeTimeRange = idx => {
  selectedTimeRanges.value.splice(idx, 1)
}
const resetTimeRanges = () => {
  selectedTimeRanges.value = []
}

const tagOptions = ref(['Tag X', 'Tag Y', 'Tag Z'])
const selectedTags = ref([])
const addTag = () => {
  openStaticTagSelector()
}
const tempSelectedTags = ref([])
const removeTag = idx => {
  selectedTags.value.splice(idx, 1)
}

const regionDialogVisible = ref(false)
const tempSelectedRegions = ref('')
const tempSelectedRegionCode = ref('')
const tempSelectedRegionName = ref('')

const selectedRegionsName = ref([])
const selectedRegionContonList = ref([])
const addRegion = () => openDialog('region')
const removeRegion = idx => {
  selectedRegionsName.value.splice(idx, 1)
  selectedRegionContonList.value.splice(idx, 1)
}
const resetRegions = () => {
  selectedRegionsName.value = []
  selectedRegionContonList.value = []
}
const manualRegionSelect = () => {
  openDialog('manualSelect')
}
const handleUpdateDistrictCode =(addressCode,addressName)=>{
  tempSelectedRegionCode.value = addressCode
  tempSelectedRegionName.value = addressName
}

// 打开弹窗方法
const openDialog = type => {
  dialogType.value = type
  dialogVisible.value = true
}

// 步骤二静态标签相关 - 完整的数据结构
const staticTagCategories = ref([]) // 动态从API加载
const staticRootTagInfoList = ref([]) // 静态标签第一层根节点信息列表（支持多个）
// 已选静态标签（计算属性）
const selectedStaticTags = computed(() => {
  const tags = []
  staticTagCategories.value.forEach(cat => {
    tags.push(...cat.tags)
  })
  return tags.filter(tag => checkedTagIds.value.includes(String(tag.id)))
})
const checkedTagIds = ref([]) // 选中的标签ID
const bufferSettings = ref({}) // 缓冲区设置
// 计算每个类别的选中状态
const categoryCheckedStates = computed(() => {
  const states = {}
  staticTagCategories.value.forEach(cat => {
    const allTagIds = cat.tags.map(tag => String(tag.id))
    const checkedCount = allTagIds.filter(id => checkedTagIds.value.includes(id)).length
    states[cat.type] = {
      checked: checkedCount === cat.tags.length && cat.tags.length > 0,
      indeterminate: checkedCount > 0 && checkedCount < cat.tags.length
    }
  })
  return states
})

const isAllChecked = (cat) => {
  return cat.tags.length > 0 && cat.tags.every(tag => checkedTagIds.value.includes(String(tag.id)))
}

const isIndeterminate = (cat) => {
  const checkedCount = cat.tags.filter(tag => checkedTagIds.value.includes(String(tag.id))).length
  return checkedCount > 0 && checkedCount < cat.tags.length
}



// 获取静态标签数据
const fetchStaticTags = async () => {
  try {
    const res = await getTagTreeData('static')
    if (res.data && Array.isArray(res.data)) {
      // 处理三层树形结构
      staticRootTagInfoList.value = res.data.map(rootNode => ({
        id: rootNode.id,
        name: rootNode.name
      }))

      // 扁平化处理标签数据
      const categories = []
      res.data.forEach(rootNode => {
        if (rootNode.children && Array.isArray(rootNode.children)) {
          rootNode.children.forEach(categoryNode => {
            if (categoryNode.tagList && Array.isArray(categoryNode.tagList)) {
              categories.push({
                id: categoryNode.id,
                name: categoryNode.name,
                type: categoryNode.id, // 使用ID作为type
                rootId: rootNode.id,
                tags: categoryNode.tagList
              })
            }
          })
        }
      })

      staticTagCategories.value = categories
    }
  } catch (error) {
    console.error('获取静态标签失败:', error)
    showToast('获取静态标签失败', 'error')
  }
}

// 获取自车标签数据
const fetchSelfTags = async () => {
  try {
    const res = await getTagTreeData('ego')
    if (res.data && Array.isArray(res.data)) {
      // 处理三层树形结构
      selfRootTagInfoList.value = res.data.map(rootNode => ({
        id: rootNode.id,
        name: rootNode.name
      }))

      // 扁平化处理标签数据
      const categories = []
      res.data.forEach(rootNode => {
        if (rootNode.children && Array.isArray(rootNode.children)) {
          rootNode.children.forEach(categoryNode => {
            if (categoryNode.tagList && Array.isArray(categoryNode.tagList)) {
              categories.push({
                id: categoryNode.id,
                name: categoryNode.name,
                type: categoryNode.id, // 使用ID作为type
                rootId: rootNode.id,
                tags: categoryNode.tagList
              })
            }
          })
        }
      })

      selfTagCategories.value = categories
    }
  } catch (error) {
    console.error('获取自车标签失败:', error)
    showToast('获取自车标签失败', 'error')
  }
}

// 初始化标签的缓冲区设置
const initBufferSetting = (tagId) => {
  if (!bufferSettings.value[tagId]) {
    bufferSettings.value[tagId] = {
      enabled: false,
      type: 'distance',
      value: 0
    }
  }
}

// 更新缓冲区值
const handleBufferValueChange = (tagId, value) => {
  if (bufferSettings.value[tagId]) {
    bufferSettings.value[tagId].value = value
  }
}

// 更新缓冲区类型
const handleBufferTypeChange = (tagId, type) => {
  if (bufferSettings.value[tagId]) {
    bufferSettings.value[tagId].type = type
    // 重置值为0，避免切换类型时值超出范围
    bufferSettings.value[tagId].value = 0
  }
}

// 生成静态标签显示文本
const tagLabelWithBuffer = (tag) => {
  const tagId = String(tag.id)
  const parts = [tag.name]

  if (bufferSettings.value[tagId]?.enabled) {
    const buffer = bufferSettings.value[tagId]
    // 检查value是否为null、undefined或空字符串
    if (buffer.value !== null && buffer.value !== undefined && buffer.value !== '') {
      const unit = buffer.type === 'distance' ? 'm' : '个'
      parts.push(`匹配缓冲区(${buffer.type === 'distance' ? '距离' : '车道'}/${buffer.value}${unit})`)
    } else {
      // 如果值为空，显示提示信息
      parts.push(`匹配缓冲区(${buffer.type === 'distance' ? '距离' : '车道'}/未设置)`)
    }
  }

  return parts.join(' ')
}

// 处理类别复选框变化
const handleCategoryChange = (cat, checked) => {
  const ids = cat.tags.map(tag => String(tag.id))
  if (checked) {
    // 选中分类下所有标签
    const newIds = ids.filter(id => !checkedTagIds.value.includes(id))
    checkedTagIds.value = [...checkedTagIds.value, ...newIds]
  } else {
    // 取消选中分类下所有标签
    checkedTagIds.value = checkedTagIds.value.filter(id => !ids.includes(id))
  }
}

const removeStaticTag = tag => {
  const tagId = String(tag.id)
  checkedTagIds.value = checkedTagIds.value.filter(id => id !== tagId)
  delete bufferSettings.value[tagId]
}

const resetStaticTags = () => {
  checkedTagIds.value = []
  bufferSettings.value = {}
}

// 监听选中的标签变化
watch(checkedTagIds, (newVal, oldVal) => {
  if (!oldVal) return // 初始化时跳过

  // 为新选中的标签初始化缓冲区设置
  newVal.forEach(id => {
    if (!bufferSettings.value[id]) {
      bufferSettings.value[id] = {
        enabled: false,
        type: 'distance',
        value: 0
      }
    }
  })

  // 找出移除的标签
  const removedTags = oldVal.filter(id => !newVal.includes(id))

  // 移除不再选中的标签的缓冲区设置
  removedTags.forEach(tagId => {
    delete bufferSettings.value[tagId]
  })
}, { deep: true })

const isTagChecked = tag => checkedTagIds.value.includes(String(tag.id))

const toggleCategory = (cat, checked) => {
  const ids = cat.tags.map(tag => String(tag.id))
  if (checked) {
    // 选中分类下所有标签
    const newIds = ids.filter(id => !checkedTagIds.value.includes(id))
    checkedTagIds.value = [...checkedTagIds.value, ...newIds]
  } else {
    // 取消选中分类下所有标签
    checkedTagIds.value = checkedTagIds.value.filter(id => !ids.includes(id))
  }
}

// 步骤三自车筛选相关 - 完整的数据结构
const selfRootTagInfoList = ref([]) // 自车标签第一层根节点信息列表（支持多个）
const selfTagCategories = ref([]) // 自车标签分类
const selfCheckedTagIds = ref([]) // 选中的自车标签ID
const selfBufferSettings = ref({}) // 自车缓冲区设置
const selfPatchSettings = ref({}) // 自车数据补全设置

// 标签选择抽屉相关
const staticTagDrawerVisible = ref(false)
const staticRowTagList = ref([])
const selfTagDrawerVisible = ref(false)
const selfRowTagList = ref([])
const selfCategoryCheckedStates = computed(() => {
  const states = {}
  selfTagCategories.value.forEach(cat => {
    const allTagIds = cat.tags.map(tag => String(tag.id))
    const checkedCount = allTagIds.filter(id => selfCheckedTagIds.value.includes(id)).length
    states[cat.type] = {
      checked: checkedCount === cat.tags.length && cat.tags.length > 0,
      indeterminate: checkedCount > 0 && checkedCount < cat.tags.length
    }
  })
  return states
})

// 获取所有可选标签
const allTags = computed(() => {
  const tags = []
  staticTagCategories.value.forEach(cat => {
    tags.push(...cat.tags)
  })
  return tags
})

// 已选标签列表
const selfSelectedTags = computed(() => {
  return allTags.value.filter(tag => selfCheckedTagIds.value.includes(String(tag.id)))
})

// 生成自车标签显示文本
const selfTagLabelWithSettings = (tag) => {
  const tagId = String(tag.id)
  const parts = [tag.name]

  if (selfBufferSettings.value[tagId]?.enabled) {
    const buffer = selfBufferSettings.value[tagId]
    // 检查value是否为null、undefined或空字符串
    if (buffer.value !== null && buffer.value !== undefined && buffer.value !== '') {
      const unit = buffer.type === 'distance' ? 'm' : '个'
      parts.push(`匹配缓冲区(${buffer.type === 'distance' ? '距离' : '车道'}/${buffer.value}${unit})`)
    } else {
      // 如果值为空，显示提示信息
      parts.push(`匹配缓冲区(${buffer.type === 'distance' ? '距离' : '车道'}/未设置)`)
    }
  }

  if (selfPatchSettings.value[tagId]?.enabled) {
    const patch = selfPatchSettings.value[tagId]
    // 检查value是否为null、undefined或空字符串
    if (patch.value !== null && patch.value !== undefined && patch.value !== '') {
      const unit = patch.type === 'distance' ? 'm' : 's'
      parts.push(`数据补全(${patch.type === 'distance' ? '距离' : '时间'}/${patch.value}${unit})`)
    } else {
      // 如果值为空，显示提示信息
      parts.push(`数据补全(${patch.type === 'distance' ? '距离' : '时间'}/未设置)`)
    }
  }

  return parts.join(' ')
}

// 获取自车已选标签列表
const getSelfSelectedTags = () => {
  const tags = []
  selfTagCategories.value.forEach(cat => {
    tags.push(...cat.tags)
  })
  return tags.filter(tag => selfCheckedTagIds.value.includes(String(tag.id)))
}

// 获取根节点的选中状态
const getRootCheckedState = (rootId) => {
  const categories = getCategoriesByRootId(rootId)
  const allTagIds = []
  categories.forEach(cat => {
    allTagIds.push(...cat.tags.map(tag => String(tag.id)))
  })
  const checkedCount = allTagIds.filter(id => checkedTagIds.value.includes(id)).length
  return {
    checked: checkedCount === allTagIds.length && allTagIds.length > 0,
    indeterminate: checkedCount > 0 && checkedCount < allTagIds.length
  }
}

// 获取自车根节点的选中状态
const getSelfRootCheckedState = (rootId) => {
  const categories = getSelfCategoriesByRootId(rootId)
  const allTagIds = []
  categories.forEach(cat => {
    allTagIds.push(...cat.tags.map(tag => String(tag.id)))
  })
  const checkedCount = allTagIds.filter(id => selfCheckedTagIds.value.includes(id)).length
  return {
    checked: checkedCount === allTagIds.length && allTagIds.length > 0,
    indeterminate: checkedCount > 0 && checkedCount < allTagIds.length
  }
}

// 根据根节点ID获取分类
const getCategoriesByRootId = (rootId) => {
  return staticTagCategories.value.filter(cat => cat.rootId === rootId)
}

// 根据根节点ID获取自车分类
const getSelfCategoriesByRootId = (rootId) => {
  return selfTagCategories.value.filter(cat => cat.rootId === rootId)
}

// 处理根节点变化
const handleRootChange = (rootId, checked) => {
  const categories = getCategoriesByRootId(rootId)
  const allTagIds = []
  categories.forEach(cat => {
    allTagIds.push(...cat.tags.map(tag => String(tag.id)))
  })

  if (checked) {
    // 选中根节点下所有标签
    const newIds = allTagIds.filter(id => !checkedTagIds.value.includes(id))
    checkedTagIds.value = [...checkedTagIds.value, ...newIds]
  } else {
    // 取消选中根节点下所有标签
    checkedTagIds.value = checkedTagIds.value.filter(id => !allTagIds.includes(id))
  }
}

// 处理自车根节点变化
const handleSelfRootChange = (rootId, checked) => {
  const categories = getSelfCategoriesByRootId(rootId)
  const allTagIds = []
  categories.forEach(cat => {
    allTagIds.push(...cat.tags.map(tag => String(tag.id)))
  })

  if (checked) {
    // 选中根节点下所有标签
    const newIds = allTagIds.filter(id => !selfCheckedTagIds.value.includes(id))
    selfCheckedTagIds.value = [...selfCheckedTagIds.value, ...newIds]
  } else {
    // 取消选中根节点下所有标签
    selfCheckedTagIds.value = selfCheckedTagIds.value.filter(id => !allTagIds.includes(id))
  }
}

// 处理自车分类变化
const handleSelfCategoryChange = (cat, checked) => {
  const ids = cat.tags.map(tag => String(tag.id))
  if (checked) {
    // 选中分类下所有标签
    const newIds = ids.filter(id => !selfCheckedTagIds.value.includes(id))
    selfCheckedTagIds.value = [...selfCheckedTagIds.value, ...newIds]
  } else {
    // 取消选中分类下所有标签
    selfCheckedTagIds.value = selfCheckedTagIds.value.filter(id => !ids.includes(id))
  }
}

// 检查缓冲区值是否无效
const isBufferValueInvalid = (tagId) => {
  const buffer = bufferSettings.value[tagId]
  return buffer?.enabled && (buffer.value === null || buffer.value === undefined || buffer.value === '')
}

// 检查自车缓冲区值是否无效
const isSelfBufferValueInvalid = (tagId) => {
  const buffer = selfBufferSettings.value[tagId]
  return buffer?.enabled && (buffer.value === null || buffer.value === undefined || buffer.value === '')
}

// 检查自车数据补全值是否无效
const isSelfPatchValueInvalid = (tagId) => {
  const patch = selfPatchSettings.value[tagId]
  return patch?.enabled && (patch.value === null || patch.value === undefined || patch.value === '')
}

// 查找静态标签
const findTagById = (tagId) => {
  for (const category of staticTagCategories.value) {
    const tag = category.tags.find(t => String(t.id) === String(tagId))
    if (tag) return tag
  }
  return null
}

// 查找自车标签
const findSelfTagById = (tagId) => {
  for (const category of selfTagCategories.value) {
    const tag = category.tags.find(t => String(t.id) === String(tagId))
    if (tag) return tag
  }
  return null
}

// 移除标签
const removeSelfTag = (tag) => {
  const tagId = String(tag.id)
  selfCheckedTagIds.value = selfCheckedTagIds.value.filter(id => id !== tagId)
}

// 重置所有设置
const resetSelfTags = () => {
  selfCheckedTagIds.value = []
  selfBufferSettings.value = {}
  selfPatchSettings.value = {}
}

// 检查标签是否被选中
const isSelfTagChecked = (tag) => selfCheckedTagIds.value.includes(String(tag.id))

// 监听标签选中状态变化
watch(selfCheckedTagIds, (newVal, oldVal) => {
  // 初始化新选中标签的设置
  newVal.forEach(id => {
    if (!selfBufferSettings.value[id]) {
      selfBufferSettings.value[id] = {
        enabled: false,
        type: 'distance',
        value: 0
      }
    }
    if (!selfPatchSettings.value[id]) {
      selfPatchSettings.value[id] = {
        enabled: false,
        type: 'distance',
        value: 0
      }
    }
  })

  // 清理未选中标签的设置
  Object.keys(selfBufferSettings.value).forEach(id => {
    if (!newVal.includes(id)) {
      delete selfBufferSettings.value[id]
    }
  })
  Object.keys(selfPatchSettings.value).forEach(id => {
    if (!newVal.includes(id)) {
      delete selfPatchSettings.value[id]
    }
  })
}, { deep: true })

// 全选相关
const selfAllChecked = ref(false)
const selfIndeterminate = ref(false)

const handleSelfCheckAllChange = (val) => {
  selfCheckedTagIds.value = val ? allTags.value.map(tag => String(tag.id)) : []
  selfIndeterminate.value = false
}

// 监听选中标签变化，更新全选状态
watch(selfCheckedTagIds, (val) => {
  const checkedCount = val.length
  const total = allTags.value.length
  selfAllChecked.value = checkedCount === total
  selfIndeterminate.value = checkedCount > 0 && checkedCount < total
}, { deep: true })

// 跳转到指定步骤
const goToStep = (step) => {
  currentStep.value = step
}

// 返回按钮处理
const handleBack = () => {
  emit('back')
}

// 静态标签选择相关方法
const openStaticTagSelector = () => {
  // 将当前选中的标签转换为抽屉组件需要的格式
  staticRowTagList.value = selectedStaticTags.value.map(tag => ({
    id: tag.id,
    name: tag.name,
    groupName: tag.groupName,
    code: tag.code
  }))
  staticTagDrawerVisible.value = true
}

const handleStaticTagDrawerClick = (data) => {
  staticTagDrawerVisible.value = false

  if (data && data.tagList && data.tagList.length) {
    // 清空当前选中的标签
    checkedTagIds.value = []

    // 处理选中的标签
    const newSelectedTags = []
    data.tagList.forEach(tagGroup => {
      extractSelectedTags(tagGroup, newSelectedTags)
    })

    // 更新选中的标签ID
    checkedTagIds.value = newSelectedTags.map(tag => String(tag.id))

    // 初始化新标签的缓冲区设置
    newSelectedTags.forEach(tag => {
      const tagId = String(tag.id)
      if (!bufferSettings.value[tagId]) {
        bufferSettings.value[tagId] = {
          enabled: false,
          type: 'distance',
          value: null
        }
      }
    })
  }
}

// 自车标签选择相关方法
const openSelfTagSelector = () => {
  // 将当前选中的标签转换为抽屉组件需要的格式
  selfRowTagList.value = getSelfSelectedTags().map(tag => ({
    id: tag.id,
    name: tag.name,
    groupName: tag.groupName,
    code: tag.code
  }))
  selfTagDrawerVisible.value = true
}

const handleSelfTagDrawerClick = (data) => {
  selfTagDrawerVisible.value = false

  if (data && data.tagList && data.tagList.length) {
    // 清空当前选中的标签
    selfCheckedTagIds.value = []

    // 处理选中的标签
    const newSelectedTags = []
    data.tagList.forEach(tagGroup => {
      extractSelectedTags(tagGroup, newSelectedTags)
    })

    // 更新选中的标签ID
    selfCheckedTagIds.value = newSelectedTags.map(tag => String(tag.id))

    // 初始化新标签的设置
    newSelectedTags.forEach(tag => {
      const tagId = String(tag.id)
      if (!selfBufferSettings.value[tagId]) {
        selfBufferSettings.value[tagId] = {
          enabled: false,
          type: 'distance',
          value: null
        }
      }
      if (!selfPatchSettings.value[tagId]) {
        selfPatchSettings.value[tagId] = {
          enabled: false,
          type: 'distance',
          value: null
        }
      }
    })
  }
}

// 从标签组中提取选中的标签
const extractSelectedTags = (group, selectedTags) => {
  if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
    group.checkedTagIdList.forEach(tagId => {
      if (group.tagMap && group.tagMap[tagId]) {
        selectedTags.push(group.tagMap[tagId])
      }
    })
  }
  if (group.children && group.children.length > 0) {
    group.children.forEach(subGroup => {
      extractSelectedTags(subGroup, selectedTags)
    })
  }
}

// 任务成功弹窗状态
const successDialogVisible = ref(false)

// mock API
const startDataMiningTask = async () => {
  const param={
    dataList:[{
      acquisitionType: "driving",
      vinList:selectedVariables.value,
      timeRangeList: selectedDates.value,
      hourRangeList: selectedTimeRanges.value,
      // "tagList": null,
      // "cantonCode":

    }]
  }
  // 模拟API延迟
  return new Promise((resolve) => setTimeout(resolve, 1000))
}

// 发起任务按钮
const submitTask = async () => {
  // 验证所有步骤
  if (!validateAllSteps()) {
    return
  }

  try {
    await startDataMiningTask()
    successDialogVisible.value = true
  } catch (e) {
    showToast('任务发起失败', 'error')
  }
}

// 验证所有步骤
const validateAllSteps = () => {
  // 验证第一步
  if (!validateStep1()) {
    currentStep.value = 0
    return false
  }

  // 验证第二步
  if (!validateStep2()) {
    currentStep.value = 1
    return false
  }

  // 验证第三步
  if (!validateStep3()) {
    currentStep.value = 2
    return false
  }

  return true
}

const handleSuccessDialog = () => {
  successDialogVisible.value = false

  // 重置所有步骤的数据
  selectedVariables.value = []
  selectedDates.value = []
  selectedTimeRanges.value = []
  selectedTags.value = []
  selectedRegionsName.value = []

  checkedTagIds.value = []
  bufferSettings.value = {}

  selfCheckedTagIds.value = []
  selfBufferSettings.value = {}
  selfPatchSettings.value = {}
  selfAllChecked.value = false
  selfIndeterminate.value = false

  currentStep.value = 0
}

onMounted(() => {
  listCantonTree()
  fetchStaticTags()
  fetchSelfTags()
  listVehicle()
})
</script>

<style lang="scss" scoped>
.data-mining-initiated {
  height:100vh;
  overflow: hidden;
  padding: 20px;
  display: flex;
  flex-direction: column;
  border: 1px #e4e7ed solid;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

// 头部区域样式
.header-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-button {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .page-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .dataset-info {
      font-size: 14px;
      color: #606266;
      background-color: #f0f9ff;
      padding: 4px 12px;
      border-radius: 4px;
      border: 1px solid #b3d8ff;
    }
  }
}
.info-box {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
  min-height: 80px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  background: #fafbfc;
}
.steps {
  margin-bottom: 30px;
}
.fixed-btn-group{
  padding:10px 25px;
  text-align: right;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  min-height: calc(100vh - 200px);
}

.content-scroll {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  padding-bottom: 10px; /* 为底部按钮留出空间 */
}

.button-group {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background-color: #fff;
  text-align: center;
  border-top: 1px solid #ebeef5;
  z-index: 1;
}

.button-group .el-button {
  margin: 0 10px;
}

// 根节点样式
.root-section {
  margin-bottom: 20px;
}

.root-tag-card {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
  }
}

// 标签卡片样式
.static-tag-cards, .self-tag-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 16px;
}

.tag-card {
  flex: 1;
  min-width: 280px;
  max-width: 400px;

  .card-header {
    display: flex;
    align-items: center;
    font-weight: bold;
  }
}

// 标签项样式
.tag-item {
  margin-bottom: 12px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background-color: #fafafa;

  .tag-checkbox {
    margin-bottom: 8px;
  }

  .tag-buffer-settings {
    margin-left: 20px;
    padding: 8px;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .tag-box {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;

      .el-radio-group {
        margin-right: 8px;
      }

      .number-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        gap: 8px;

        .el-input-number {
          width: 120px;
        }

        .unit {
          color: #606266;
          font-size: 14px;
        }

        .error-tip {
          position: absolute;
          top: 100%;
          left: 0;
          font-size: 12px;
          color: #f56c6c;
          line-height: 1.2;
          margin-top: 2px;
          z-index: 1;
        }

        .input-error {
          :deep(.el-input__wrapper) {
            border-color: #f56c6c !important;
            box-shadow: 0 0 0 1px #f56c6c inset !important;
          }
        }
      }
    }
  }
}

.static-tags {
  margin-top: 16px;

  .tag-category {
    margin-bottom: 16px;

    .category-title {
      font-weight: bold;
      margin-bottom: 8px;
    }

    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .tag-item {
        display: flex;
        flex-direction: column;
        margin-bottom: 12px;

        .tag-checkbox {
          display: flex;
          align-items: center;
        }

        .tag-buffer-settings {
          margin-left: 24px;
          margin-top: 8px;
        //   display: flex;
        //   align-items: center;
          gap: 8px;

          .el-radio-group {
            margin-right: 8px;
          }

          .number-input-wrapper {
            display: inline-block;

            :deep(.el-input-number) {
              width: 100px;
            }
          }

          .unit {
            color: #606266;
            font-size: 14px;
          }
        }
      }
    }
  }
}

.tag-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;

  .tag-checkbox {
    display: flex;
    align-items: center;
  }

  .tag-buffer-settings {
    margin-left: 24px;
    margin-top: 8px;
    // display: flex;
    // align-items: center;
    gap: 8px;

    .el-radio-group {
      margin-right: 8px;
    }

    .number-input-wrapper {
      display: inline-block;

      :deep(.el-input-number) {
        width: 100px;
      }
    }

    .unit {
      color: #606266;
      font-size: 14px;
    }
  }
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
}

:deep(.el-input-number.el-input-number--small) {
  width: 100px !important;
}

.self-form {
  margin-bottom: 16px;

  .info-box {
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    min-height: 40px;
    display: flex;
    align-items: center;
  }
}

.self-tag-list-card {
  margin-top: 16px;
}

.self-tag-list-header {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 40px;
}

.self-tag-list-grid {
  display: flex;
  flex-wrap: wrap;
}

.self-tag-item {
  flex: 0 0 33.3333%;
  box-sizing: border-box;
  padding: 12px 16px 12px 0;
  border-bottom: 1px solid #ebeef5;
  min-width: 0;
}

.tag-settings {
  margin-left: 24px;
  margin-top: 8px;
  .setting-item {
    margin-bottom: 8px;
  }
  .setting-content {
    margin-left: 24px;
    margin-top: 8px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    .el-radio-group {
      margin-right: 8px;
    }
    .unit {
      margin-left: 8px;
      color: #606266;
    }
  }
}

.step4-card {
  margin-bottom: 24px;
  .card-header-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  .card-title {
    font-size: 18px;
    font-weight: 600;
  }
  .card-section {
    margin-bottom: 16px;
    .section-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 4px;
    }
    .el-input {
      width: 100%;
    }
  }
}

.step4-btns {
  margin-top: 32px;
  text-align: center;
}


.card-area {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 0; // 关键，防止flex溢出
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10), 0 1.5px 6px 0 rgba(0,0,0,0.06);
  background: #fff;

  .card-scroll {
    flex: 1;
    height: 100%;

    :deep(.el-scrollbar__wrap) {
      overflow-x: hidden;
    }

    :deep(.el-scrollbar__bar) {
      z-index: 10;
    }

    :deep(.el-scrollbar__view) {
      padding: 24px;
      padding-bottom: 80px; // 为底部按钮留出空间
    }
  }
}

.fixed-btn-group {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px 25px;
  text-align: right;
  background: #fff;
  border-top: 1px solid #ebeef5;
  z-index: 11;
}
</style>
