<template>
  <base-filter @filter="filter">
    <template #common-filters>
      <div class="flex">
        <span class="label-txt">{{ $t('任务编码') }}</span>
        <ltw-input
          v-model="formData.code"
          size="small"
          class="selector-container"
          clearable
          @clear="deleteFields('code')"
        ></ltw-input>
      </div>
      <div class="flex">
        <span class="label-txt">{{ $t('负责人') }}</span>
        <el-select
          v-model="formData.empId"
          :placeholder="$t('请选择')"
          clearable
          filterable
          size="small"
          class="selector-container"
          @clear="deleteFields('empId')"
        >
          <el-option v-for="item in employeeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
      <div class="flex">
        <span class="label-txt">{{ $t('标签分类') }}</span>
        <el-select
          v-model="formData.tagClassificationIds"
          :placeholder="$t('请选择')"
          clearable
          filterable
          size="small"
          class="selector-container"
          @clear="deleteFields('tagClassificationIds')"
        >
          <el-option v-for="item in tagClassificationList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
      <div class="flex">
        <span class="label-txt">{{ $t('状态') }}</span>
        <dictionary-selection
          v-model="formData.status"
          clearable
          dictionaryType="qa_tag_task_status"
          :placeholder="$t('根据状态搜索')"
          size="small"
          class="selector-container"
          filterable
          @clear="deleteFields('status')"
        />
      </div>
    </template>
    <template #more-filters>
      <div class="flex">
        <span class="label-txt">{{ $t('抽检人') }}</span>
        <el-select
          v-model="formData.samplingCheckEmpId"
          :placeholder="$t('请选择')"
          clearable
          filterable
          size="small"
          class="selector-container"
          @clear="deleteFields('samplingCheckEmpId')"
        >
          <el-option v-for="item in employeeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
    </template>
  </base-filter>
</template>
<script>
import BaseFilter from '@/components/filter/BaseFilter.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import {listDmProject} from '@/apis/dm/dm-project'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import {listSysRoleEmployee} from '@/apis/system/sys-role-employee'

const defaultForm = {}
export default {
  name: 'QaTagTaskFilter',
  components: {EmployeeSelection, BaseFilter, DictionarySelection},
  props: {},
  emits: ['filter'],
  data() {
    return {
      formData: Object.assign({}, defaultForm),
      employeeList: [],
      tagClassificationList: [
        {
          accessRoleId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          accessRoleName: '张慧君',
          accessRoleType: 'emp',
          accessRoleTypeName: '个人',
          acquisitionType: 'parking',
          code: 'parking_tag_4',
          id: '0c1d4a78e9374a5894ebeffb82cec836',
          name: 'Parking Tag 补打分组4',
          ownerEmpId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          ownerEmpName: 'Terry.ZHANG',
          tagAmount: 8,
          updatable: true
        },
        {
          accessRoleId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          accessRoleName: '张慧君',
          accessRoleType: 'emp',
          accessRoleTypeName: '个人',
          acquisitionType: 'parking',
          code: 'parking_tag_3',
          id: 'e92c3598e25e4209a19bc3819bb0f232',
          name: 'Parking Tag 补打分组3',
          ownerEmpId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          ownerEmpName: 'Terry.ZHANG',
          tagAmount: 25,
          updatable: true
        },
        {
          accessRoleId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          accessRoleName: '张慧君',
          accessRoleType: 'emp',
          accessRoleTypeName: '个人',
          acquisitionType: 'parking',
          code: 'parking_tag_2',
          id: '18877f7b95a2494b9cfa2127b588d54b',
          name: 'Parking Tag 补打分组2',
          ownerEmpId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          ownerEmpName: 'Terry.ZHANG',
          tagAmount: 19,
          updatable: true
        },
        {
          accessRoleId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          accessRoleName: '张慧君',
          accessRoleType: 'emp',
          accessRoleTypeName: '个人',
          acquisitionType: 'parking',
          code: 'parking_tag_1',
          id: 'd0edbaf52ad64da7826dd9b85da23398',
          name: 'Pakring Tag 补打分组1',
          ownerEmpId: '6e73d213b8e24cf4aeab5d6b6fe6dab1',
          ownerEmpName: 'Terry.ZHANG',
          tagAmount: 21,
          updatable: true
        }
      ]
    }
  },
  created() {
    if (!this.employeeList?.length) {
      this.listEmployee()
    }
  },
  methods: {
    listEmployee() {
      listSysRoleEmployee().then(res => {
        this.employeeList = res.data
      })
    },
    setEmpId(id) {
      this.formData.empId = id
    },
    deleteFields(field) {
      delete this.formData[field]
    },
    filter() {
      let postData = {...this.formData}
      this.$emit('filter', postData)
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  padding-right: 15px;
  width: 25%;

  .label-txt {
    font-size: 12px;
    width: 65px;
    line-height: 0;
    text-align: justify;
    padding-right: 5px;
  }

  .label-txt::after {
    content: '';
    display: inline-block;
    width: 100%;
    height: 0;
  }

  :deep(.selector-container) {
    width: calc(100% - 65px);
  }
}
</style>
