<template>
  <base-filter @filter="filter">
    <template #common-filters>
      <div class="flex">
        <span class="label-txt">{{ $t('任务编码') }}</span>
        <ltw-input
          v-model="formData.code"
          size="small"
          class="selector-container"
          clearable
          @clear="deleteFields('code')"
        ></ltw-input>
      </div>
      <div class="flex">
        <span class="label-txt">{{ $t('负责人') }}</span>
        <el-select
          v-model="formData.empId"
          :placeholder="$t('请选择')"
          clearable
          filterable
          size="small"
          class="selector-container"
          @clear="deleteFields('empId')"
        >
          <el-option v-for="item in employeeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
      <div class="flex">
        <span class="label-txt">{{ $t('标签分类') }}</span>
        <dictionary-selection
          v-model="formData.tagClassificationIds"
          clearable
          dictionaryType="qa_tag_classification"
          :placeholder="$t('请选择')"
          size="small"
          class="selector-container"
          filterable
          @clear="deleteFields('tagClassificationIds')"
        />
      </div>
      <div class="flex">
        <span class="label-txt">{{ $t('状态') }}</span>
        <dictionary-selection
          v-model="formData.status"
          clearable
          dictionaryType="qa_tag_task_status"
          :placeholder="$t('根据状态搜索')"
          size="small"
          class="selector-container"
          filterable
          @clear="deleteFields('status')"
        />
      </div>
    </template>
    <template #more-filters>
      <div class="flex">
        <span class="label-txt">{{ $t('开始时间') }}</span>
        <el-date-picker
          v-model="formData.startTime"
          type="date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :placeholder="$t('请选择开始时间')"
          size="small"
          @clear="deleteFields('startTime')"
        />
      </div>
      <div class="flex">
        <span class="label-txt">{{ $t('结束时间') }}</span>
        <el-date-picker
          v-model="formData.endTime"
          type="date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :placeholder="$t('请选择结束时间')"
          size="small"
          @clear="deleteFields('endTime')"
        />
      </div>
      <div class="flex">
        <span class="label-txt">{{ $t('采集类型') }}</span>
        <el-select
          v-model="formData.acquisitionType"
          :placeholder="$t('请选择采集类型')"
          clearable
          filterable
          size="small"
          class="selector-container"
          @clear="deleteFields('acquisitionType')"
        >
          <el-option v-for="item in acquisitionTypeList" :key="item.code" :label="item.code" :value="item.code" />
        </el-select>
      </div>
      <div class="flex">
        <span class="label-txt">{{ $t('抽检人') }}</span>
        <el-select
          v-model="formData.samplingCheckEmpId"
          :placeholder="$t('请选择抽检人')"
          clearable
          filterable
          size="small"
          class="selector-container"
          @clear="deleteFields('samplingCheckEmpId')"
        >
          <el-option v-for="item in employeeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
    </template>
  </base-filter>
</template>
<script>
import BaseFilter from '@/components/filter/BaseFilter.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import { listDmProject } from '@/apis/dm/dm-project'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import { listSysRoleEmployee } from '@/apis/system/sys-role-employee'
import { listSysDictionary } from '@/apis/system/sys-dictionary'

const defaultForm = {}
export default {
  name: 'QaTagTaskFilter',
  components: { EmployeeSelection, BaseFilter, DictionarySelection },
  props: {},
  emits: ['filter'],
  data() {
    return {
      formData: Object.assign({}, defaultForm),
      employeeList: [],
      acquisitionTypeList: []
    }
  },
  created() {
    if (!this.employeeList?.length) {
      this.listEmployee()
    }
    if (!this.acquisitionTypeList?.length) {
      this.listAcquisitionType()
    }
  },
  methods: {
    listEmployee() {
      listSysRoleEmployee().then(res => {
        this.employeeList = res.data
      })
    },
    listAcquisitionType() {
      return listSysDictionary({
        typeCode: 'data_acquisition_type'
      }).then(res => {
        this.acquisitionTypeList = res.data
      })
    },
    setEmpId(id) {
      this.formData.empId = id
    },
    deleteFields(field) {
      delete this.formData[field]
    },
    filter() {
      let postData = { ...this.formData }
      this.$emit('filter', postData)
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  padding-right: 15px;
  width: 25%;

  .label-txt {
    font-size: 12px;
    width: 65px;
    line-height: 0;
    text-align: justify;
    padding-right: 5px;
  }

  .label-txt::after {
    content: '';
    display: inline-block;
    width: 100%;
    height: 0;
  }

  :deep(.selector-container) {
    width: calc(100% - 65px);
  }
}
</style>
